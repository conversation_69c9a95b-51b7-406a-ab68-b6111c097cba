@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM =============================================
REM 新闻可视化系统 - Windows 一键部署脚本
REM =============================================

echo ==========================================
echo   新闻可视化系统 - 一键部署脚本
echo ==========================================

REM 检查 Docker
echo [INFO] 检查系统依赖...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

REM 检查 Docker Compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

echo [SUCCESS] 依赖检查完成

REM 检查环境配置
echo [INFO] 检查环境配置...
if not exist ".env" (
    echo [WARNING] .env 文件不存在，从模板创建...
    copy .env.example .env
    echo [WARNING] 请编辑 .env 文件，配置数据库密码和百度翻译API密钥
    echo [WARNING] 配置完成后重新运行此脚本
    pause
    exit /b 1
)

echo [SUCCESS] 环境配置检查完成

REM 创建必要的目录
echo [INFO] 创建必要的目录...
if not exist "backend\logs" mkdir backend\logs
if not exist "backend\data" mkdir backend\data
if not exist "docker\ssl" mkdir docker\ssl

echo [SUCCESS] 目录创建完成

REM 停止现有服务
echo [INFO] 停止现有服务...
docker-compose down --remove-orphans

REM 构建镜像
echo [INFO] 构建 Docker 镜像...
docker-compose build --no-cache

REM 启动服务
echo [INFO] 启动服务...
docker-compose up -d

echo [SUCCESS] 服务部署完成

REM 等待服务启动
echo [INFO] 等待服务启动...
timeout /t 30 /nobreak >nul

echo [SUCCESS] 部署完成！
echo.
echo ==========================================
echo   新闻可视化系统部署信息
echo ==========================================
echo 前端地址: http://localhost
echo 后端API: http://localhost:3001
echo 数据库: localhost:3306
echo.
echo 管理命令:
echo   查看日志: docker-compose logs -f [service_name]
echo   重启服务: docker-compose restart [service_name]
echo   停止服务: docker-compose down
echo   查看状态: docker-compose ps
echo.
echo 服务状态:
docker-compose ps
echo ==========================================

pause