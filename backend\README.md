# 新闻可视化系统 - 后端API

这是新闻可视化系统的后端API服务，提供MySQL数据库连接和RESTful API接口。

## 📋 功能特性

- ✅ MySQL数据库连接
- ✅ 多语言新闻支持 (中文/英文)
- ✅ RESTful API接口
- ✅ 实时新闻更新
- ✅ 国家和新闻源管理
- ✅ 错误处理和日志

## 🚀 快速开始

### 1. 安装依赖

```bash
cd backend
npm install
```

### 2. 配置数据库

1. 确保MySQL服务已启动
2. 复制环境变量配置文件：
```bash
copy .env.example .env
```

3. 编辑 `.env` 文件，配置数据库连接：
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password_here
DB_NAME=news_system
PORT=3001
```

### 3. 初始化数据库

```bash
# 创建数据库和表，插入示例数据
node scripts/init-db.js
```

### 4. 启动服务

```bash
# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

服务将在 `http://localhost:3001` 启动

## 📚 API 接口文档

### 获取所有新闻
```
GET /api/news/all?lang=zh
```
**参数：**
- `lang`: 语言代码 (zh/en)，默认为 zh

**响应：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "新闻标题",
      "content": "新闻内容",
      "publish_time": "2025-09-07T10:00:00.000Z",
      "country_code": "CN",
      "country_name": "中国",
      "source_name": "新华社"
    }
  ]
}
```

### 获取最新新闻
```
GET /api/news/latest?since=2025-09-07T10:00:00Z&lang=zh
```
**参数：**
- `since`: ISO时间戳，获取此时间之后的新闻
- `lang`: 语言代码 (zh/en)

### 添加新闻
```
POST /api/news
Content-Type: application/json

{
  "title": "新闻标题",
  "content": "新闻内容",
  "country_code": "CN",
  "source_id": 1
}
```

### 获取国家列表
```
GET /api/countries
```

### 获取新闻源列表
```
GET /api/sources
```

## 🗄️ 数据库结构

### 主要表格：

1. **countries** - 国家信息
   - `code`: 国家代码 (CN, US, etc.)
   - `name_zh`: 中文名称
   - `name_en`: 英文名称
   - `latitude`, `longitude`: 经纬度

2. **sources** - 新闻源
   - `name`: 新闻源名称
   - `website`: 官网地址

3. **news** - 新闻主表
   - `title`: 标题
   - `content`: 内容
   - `country_code`: 所属国家
   - `source_id`: 新闻源ID
   - `publish_time`: 发布时间
   - `status`: 状态 (draft/published/archived)

4. **news_translations** - 新闻翻译
   - `news_id`: 新闻ID
   - `language`: 语言代码
   - `title`: 翻译标题
   - `content`: 翻译内容

## 🔧 开发工具

### 测试API
```bash
# 测试获取所有新闻
curl http://localhost:3001/api/news/all?lang=zh

# 测试获取最新新闻
curl "http://localhost:3001/api/news/latest?since=2025-09-07T00:00:00Z&lang=zh"
```

### 添加测试数据
```bash
curl -X POST http://localhost:3001/api/news \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试新闻",
    "content": "这是一条测试新闻",
    "country_code": "CN",
    "source_id": 1
  }'
```

## 📝 注意事项

1. **数据库编码**: 确保MySQL使用 `utf8mb4` 编码以支持emoji和特殊字符
2. **时区设置**: 建议将MySQL时区设置为UTC
3. **连接池**: 生产环境建议调整连接池参数
4. **日志**: 生产环境建议配置详细的日志系统
5. **安全**: 生产环境请设置强密码和防火墙规则

## 🔒 安全配置

- 数据库密码请使用强密码
- 生产环境建议使用HTTPS
- 可以添加API访问频率限制
- 建议配置CORS白名单

## 📊 监控和日志

服务器会输出详细的运行日志：
- ✅ 数据库连接状态
- 📝 API请求日志
- ❌ 错误信息

## 🛠️ 故障排除

### 常见问题：

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 确认用户名密码正确
   - 检查数据库是否存在

2. **端口占用**
   - 修改 `.env` 文件中的 `PORT` 配置

3. **编码问题**
   - 确保数据库字符集为 utf8mb4
