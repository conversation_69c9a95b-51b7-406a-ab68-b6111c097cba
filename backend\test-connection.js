// 快速测试数据库连接
require('dotenv').config();

async function testConnection() {
  console.log('🔍 测试数据库连接...');
  console.log('配置信息:');
  console.log(`- 主机: ${process.env.DB_HOST}`);
  console.log(`- 用户: ${process.env.DB_USER}`);
  console.log(`- 数据库: ${process.env.DB_NAME}`);
  console.log('- 密码: [已隐藏]');
  console.log('');

  try {
    // 检查是否安装了 mysql2
    try {
      require('mysql2/promise');
    } catch (e) {
      console.log('❌ 缺少 mysql2 依赖包');
      console.log('请运行: npm install mysql2');
      return;
    }

    const mysql = require('mysql2/promise');
    
    // 尝试连接
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      charset: 'utf8mb4'
    });

    console.log('✅ 数据库连接成功！');
    
    // 测试查询
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ 数据库查询测试成功');
    
    await connection.end();
    
  } catch (error) {
    console.log('❌ 数据库连接失败:');
    console.log(`   错误信息: ${error.message}`);
    console.log('');
    console.log('🔧 可能的解决方案:');
    console.log('1. 检查MySQL服务是否启动');
    console.log('2. 确认主机名、用户名、密码是否正确');
    console.log('3. 检查数据库是否存在');
    console.log('4. 确认MySQL端口是否为3306');
  }
}

testConnection();
