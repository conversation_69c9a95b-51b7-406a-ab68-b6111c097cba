/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.132
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as G}from"./chunk-U4IEOH5K.js";import{a as z}from"./chunk-G5AGHVVC.js";import{b as B}from"./chunk-DMEY62ID.js";import{a as C}from"./chunk-HTFSEEMT.js";import{b as L}from"./chunk-PSPPBZWI.js";import{a,b as D,d as I}from"./chunk-64RSHJUE.js";import{a as S}from"./chunk-3SSKC3VN.js";import{a as x}from"./chunk-LEYMRMBK.js";import{e as T}from"./chunk-VTAIKJXX.js";var p={};p.numberOfPoints=function(r,n,o){let e=a.distance(r,n);return Math.ceil(e/o)};p.numberOfPointsRhumbLine=function(r,n,o){let e=Math.pow(r.longitude-n.longitude,2)+Math.pow(r.latitude-n.latitude,2);return Math.max(1,Math.ceil(Math.sqrt(e/(o*o))))};var Z=new D;p.extractHeights=function(r,n){let o=r.length,e=new Array(o);for(let t=0;t<o;t++){let l=r[t];e[t]=n.cartesianToCartographic(l,Z).height}return e};var Y=new L,V=new a,_=new a,F=new C(a.UNIT_X,0),v=new a,j=new C(a.UNIT_X,0),J=new a,K=new a,O=[];function U(r,n,o){let e=O;e.length=r;let t;if(n===o){for(t=0;t<r;t++)e[t]=n;return e}let u=(o-n)/r;for(t=0;t<r;t++){let d=n+t*u;e[t]=d}return e}var N=new D,E=new D,b=new a,k=new a,Q=new a,M=new G,R=new z;function W(r,n,o,e,t,l,u,d){let c=e.scaleToGeodeticSurface(r,k),w=e.scaleToGeodeticSurface(n,Q),h=p.numberOfPoints(r,n,o),f=e.cartesianToCartographic(c,N),y=e.cartesianToCartographic(w,E),g=U(h,t,l);M.setEndPoints(f,y);let P=M.surfaceDistance/h,i=d;f.height=t;let s=e.cartographicToCartesian(f,b);a.pack(s,u,i),i+=3;for(let m=1;m<h;m++){let A=M.interpolateUsingSurfaceDistance(m*P,E);A.height=g[m],s=e.cartographicToCartesian(A,b),a.pack(s,u,i),i+=3}return i}function $(r,n,o,e,t,l,u,d){let c=e.cartesianToCartographic(r,N),w=e.cartesianToCartographic(n,E),h=p.numberOfPointsRhumbLine(c,w,o);c.height=0,w.height=0;let f=U(h,t,l);R.ellipsoid.equals(e)||(R=new z(void 0,void 0,e)),R.setEndPoints(c,w);let y=R.surfaceDistance/h,g=d;c.height=t;let P=e.cartographicToCartesian(c,b);a.pack(P,u,g),g+=3;for(let i=1;i<h;i++){let s=R.interpolateUsingSurfaceDistance(i*y,E);s.height=f[i],P=e.cartographicToCartesian(s,b),a.pack(P,u,g),g+=3}return g}p.wrapLongitude=function(r,n){let o=[],e=[];if(T(r)&&r.length>0){n=n??L.IDENTITY;let t=L.inverseTransformation(n,Y),l=L.multiplyByPoint(t,a.ZERO,V),u=a.normalize(L.multiplyByPointAsVector(t,a.UNIT_Y,_),_),d=C.fromPointNormal(l,u,F),c=a.normalize(L.multiplyByPointAsVector(t,a.UNIT_X,v),v),w=C.fromPointNormal(l,c,j),h=1;o.push(a.clone(r[0]));let f=o[0],y=r.length;for(let g=1;g<y;++g){let P=r[g];if(C.getPointDistance(w,f)<0||C.getPointDistance(w,P)<0){let i=B.lineSegmentPlane(f,P,d,J);if(T(i)){let s=a.multiplyByScalar(u,5e-9,K);C.getPointDistance(d,f)<0&&a.negate(s,s),o.push(a.add(i,s,new a)),e.push(h+1),a.negate(s,s),o.push(a.add(i,s,new a)),h=1}}o.push(a.clone(r[g])),h++,f=P}e.push(h)}return{positions:o,lengths:e}};p.generateArc=function(r){T(r)||(r={});let n=r.positions;if(!T(n))throw new x("options.positions is required.");let o=n.length,e=r.ellipsoid??I.default,t=r.height??0,l=Array.isArray(t);if(o<1)return[];if(o===1){let i=e.scaleToGeodeticSurface(n[0],k);if(t=l?t[0]:t,t!==0){let s=e.geodeticSurfaceNormal(i,b);a.multiplyByScalar(s,t,s),a.add(i,s,i)}return[i.x,i.y,i.z]}let u=r.minDistance;if(!T(u)){let i=r.granularity??S.RADIANS_PER_DEGREE;u=S.chordLength(i,e.maximumRadius)}let d=0,c;for(c=0;c<o-1;c++)d+=p.numberOfPoints(n[c],n[c+1],u);let w=(d+1)*3,h=new Array(w),f=0;for(c=0;c<o-1;c++){let i=n[c],s=n[c+1],m=l?t[c]:t,A=l?t[c+1]:t;f=W(i,s,u,e,m,A,h,f)}O.length=0;let y=n[o-1],g=e.cartesianToCartographic(y,N);g.height=l?t[o-1]:t;let P=e.cartographicToCartesian(g,b);return a.pack(P,h,w-3),h};var H=new D,tt=new D;p.generateRhumbArc=function(r){T(r)||(r={});let n=r.positions;if(!T(n))throw new x("options.positions is required.");let o=n.length,e=r.ellipsoid??I.default,t=r.height??0,l=Array.isArray(t);if(o<1)return[];if(o===1){let m=e.scaleToGeodeticSurface(n[0],k);if(t=l?t[0]:t,t!==0){let A=e.geodeticSurfaceNormal(m,b);a.multiplyByScalar(A,t,A),a.add(m,A,m)}return[m.x,m.y,m.z]}let u=r.granularity??S.RADIANS_PER_DEGREE,d=0,c,w=e.cartesianToCartographic(n[0],H),h;for(c=0;c<o-1;c++)h=e.cartesianToCartographic(n[c+1],tt),d+=p.numberOfPointsRhumbLine(w,h,u),w=D.clone(h,H);let f=(d+1)*3,y=new Array(f),g=0;for(c=0;c<o-1;c++){let m=n[c],A=n[c+1],q=l?t[c]:t,X=l?t[c+1]:t;g=$(m,A,u,e,q,X,y,g)}O.length=0;let P=n[o-1],i=e.cartesianToCartographic(P,N);i.height=l?t[o-1]:t;let s=e.cartographicToCartesian(i,b);return a.pack(s,y,f-3),y};p.generateCartesianArc=function(r){let n=p.generateArc(r),o=n.length/3,e=new Array(o);for(let t=0;t<o;t++)e[t]=a.unpack(n,t*3);return e};p.generateCartesianRhumbArc=function(r){let n=p.generateRhumbArc(r),o=n.length/3,e=new Array(o);for(let t=0;t<o;t++)e[t]=a.unpack(n,t*3);return e};var gt=p;export{gt as a};
