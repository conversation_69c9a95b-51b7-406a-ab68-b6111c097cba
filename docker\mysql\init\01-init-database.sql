-- =============================================
-- 新闻可视化系统数据库初始化脚本
-- =============================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建新闻主表
CREATE TABLE IF NOT EXISTS `news` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '新闻主键ID',
  `publish_time` datetime NOT NULL COMMENT '发布时间',
  `link` text COMMENT '原始链接',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  <PERSON>EY `idx_publish_time` (`publish_time`),
  <PERSON>EY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻主表';

-- 创建新闻翻译表
CREATE TABLE IF NOT EXISTS `news_translations` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '翻译主键ID',
  `news_id` int NOT NULL COMMENT '关联的新闻ID',
  `lang` enum('zh','en') NOT NULL COMMENT '语言：zh=中文，en=英文',
  `title` varchar(500) NOT NULL COMMENT '新闻标题',
  `content` text COMMENT '新闻内容',
  `source` varchar(255) DEFAULT NULL COMMENT '新闻来源',
  `country` varchar(100) DEFAULT NULL COMMENT '国家名称',
  `keyword` text COMMENT '关键词',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_news_lang` (`news_id`,`lang`),
  KEY `idx_lang` (`lang`),
  KEY `idx_country` (`country`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_news_translations_news` FOREIGN KEY (`news_id`) REFERENCES `news` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻翻译表';

-- 创建国家配置表
CREATE TABLE IF NOT EXISTS `countries` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(3) NOT NULL COMMENT '国家代码',
  `name_en` varchar(100) NOT NULL COMMENT '英文名称',
  `name_zh` varchar(100) NOT NULL COMMENT '中文名称',
  `label_latitude` decimal(10,7) DEFAULT NULL COMMENT '标签纬度',
  `label_longitude` decimal(10,7) DEFAULT NULL COMMENT '标签经度',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国家配置表';

-- 创建域名映射表
CREATE TABLE IF NOT EXISTS `domain_mappings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `domain` varchar(255) NOT NULL COMMENT '域名',
  `country_code` varchar(3) NOT NULL COMMENT '国家代码',
  `media_type` varchar(50) DEFAULT 'news' COMMENT '媒体类型',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_domain` (`domain`),
  KEY `idx_country_code` (`country_code`),
  CONSTRAINT `fk_domain_mappings_countries` FOREIGN KEY (`country_code`) REFERENCES `countries` (`code`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='域名映射表';