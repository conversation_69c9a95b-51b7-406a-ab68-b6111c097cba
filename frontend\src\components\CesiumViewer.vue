<template>
  <div class="cesium-viewer-container">
    <!-- Cesium容器 -->
    <div id="cesiumContainer" class="cesium-container"></div>
    
    <!-- 语言切换按钮 -->
    <div class="language-switcher">
      <button 
        @click="switchLanguage('zh')" 
        :class="{ active: currentLanguage === 'zh' }"
        class="lang-btn"
      >
        中文
      </button>
      <button 
        @click="switchLanguage('en')" 
        :class="{ active: currentLanguage === 'en' }"
        class="lang-btn"
      >
        English
      </button>
    </div>

    <!-- 新闻显示窗口 -->
    <div id="newsDisplay" class="news-display">
      <div class="news-content">
        <!-- 新增NEW标识 -->
        <div id="newsBadge" class="news-badge">
          <span class="new-indicator">NEW!</span>
        </div>
        
        <!-- 固定的头部信息 -->
        <div class="news-header">
          <div id="newsProgress" class="news-progress"></div>
          <div class="news-header-row">
            <div id="newsTime" class="news-time"></div>
            <a
              v-if="currentNewsLink"
              :href="currentNewsLink"
              class="news-link-inline"
              target="_blank"
              rel="noopener"
            >
              [{{ directLinkText }}]
            </a>
          </div>
          <div id="newsTitle" class="news-title"></div>
          <div id="newsSource" class="news-source"></div>
        </div>
        
        <!-- 可滚动的内容区域 -->
        <div id="newsBodyContainer" class="news-body-container">
          <div id="newsBody" class="news-body"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, nextTick, computed } from 'vue'
import { 
  Cartesian3, 
  Cartesian2,
  Ion, 
  Math as CesiumMath, 
  Terrain, 
  Viewer,
  ScreenSpaceEventHandler,
  ScreenSpaceEventType,
  GeoJsonDataSource,
  Color,
  JulianDate,
  defined,
  BoundingSphere,
  Cartographic,
  LabelGraphics,
  LabelStyle,
  VERSION,
  PinBuilder,
  VerticalOrigin,
  NearFarScalar,
} from 'cesium'
import polylabel from 'polylabel'
import "cesium/Build/Cesium/Widgets/widgets.css"

// 响应式数据
const currentLanguage = ref('zh') // 默认中文
const currentNewsLink = ref('')

// 数据库驱动的国家配置
const countriesData = ref([]) // 存储从数据库获取的国家配置
const domainMappings = ref(new Map()) // 存储域名到国家的映射缓存

// 在外部作用域声明这些变量，让所有函数都能访问
let newsByCountry = {}
let allNews = []
let currentNewsIndex = 0
let isDisplayingNews = false  // 是否正在显示新闻
let shouldJumpToLatest = false
let startNewsRotation = null
let viewer = null
let handler = null
let newsRotationInterval = null
let typewriterTimeout = null
let timeUpdateInterval = null 


// 添加已播放新闻的追踪
let playedNewsIds = new Set() // 记录已播放过的新闻ID
// 管理pin的变量
let newsCountPins = new Map() // 存储所有数字pin
let highlightPin = null       // 存储高亮pin（pinImageUrl）
// 直达链接的多语言文本
const directLinkText = computed(() =>
  currentLanguage.value === 'zh' ? '直达链接' : 'Original Link'
)

// 从数据库加载国家配置
async function loadCountriesFromDatabase() {
  try {
    // 动态获取 API 基础地址
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 
      (import.meta.env.DEV ? 
        (window.location.hostname === 'localhost' || window.location.hostname.startsWith('192.168') ? 
          `http://${window.location.hostname}:3001` : 
          `http://${window.location.hostname}:8080`) : 
        '/api')
    
    const response = await fetch(`${apiBaseUrl}/api/countries`)
    const result = await response.json()
    
    if (result.success && result.data) {
      countriesData.value = result.data
      console.log('从数据库加载国家配置成功:', result.data.length, '个国家')
      return true
    } else {
      throw new Error(result.message || '获取国家配置失败')
    }
  } catch (error) {
    console.error('从数据库加载国家配置失败:', error)
    return false
  }
}

// 生成动态的国家标签映射
function generateCountryLabelMapping() {
  const mapping = {}
  countriesData.value.forEach(country => {
    mapping[country.name_en] = {
      zh: country.name_zh,
      en: country.name_en
    }
  })
  // 添加未知国家
  // mapping['Unknown'] = { zh: '未知', en: 'Unknown' }
  return mapping
}

// 生成动态的国家位置映射
function generateCountryPositions() {
  const positions = {}
  countriesData.value.forEach(country => {
    positions[country.name_en] = {
      lon: Number(country.label_longitude),
      lat: Number(country.label_latitude)
    }
  })
  // 添加未知国家默认位置
  // positions['Unknown'] = { lon: 114.0, lat: 12.0 }
  return positions
}

// 存储标签实体的引用
let labels = new Map()

// 添加计算相对时间的函数
function getRelativeTime(publishTime, language = 'zh') {
  const now = new Date()
  const publishDate = new Date(publishTime)
  const diffMs = now - publishDate
  
  // 转换为各种时间单位
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)
  const diffMonths = Math.floor(diffDays / 30)
  const diffYears = Math.floor(diffDays / 365)
  
  if (language === 'zh') {
    if (diffSeconds < 60) {
      return diffSeconds <= 0 ? '刚刚' : `${diffSeconds}秒前`
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`
    } else if (diffHours < 24) {
      return `${diffHours}小时前`
    } else if (diffDays < 30) {
      return `${diffDays}天前`
    } else if (diffMonths < 12) {
      return `${diffMonths}个月前`
    } else {
      return `${diffYears}年前`
    }
  } else {
    if (diffSeconds < 60) {
      return diffSeconds <= 0 ? 'Just now' : `${diffSeconds} seconds ago`
    } else if (diffMinutes < 60) {
      return diffMinutes === 1 ? '1 minute ago' : `${diffMinutes} minutes ago`
    } else if (diffHours < 24) {
      return diffHours === 1 ? '1 hour ago' : `${diffHours} hours ago`
    } else if (diffDays < 30) {
      return diffDays === 1 ? '1 day ago' : `${diffDays} days ago`
    } else if (diffMonths < 12) {
      return diffMonths === 1 ? '1 month ago' : `${diffMonths} months ago`
    } else {
      return diffYears === 1 ? '1 year ago' : `${diffYears} years ago`
    }
  }
}

// 从数据库加载新闻数据
async function loadNewsFromDatabase(language = 'zh') {
  try {
    console.log(`从数据库加载${language === 'zh' ? '中文' : '英文'}新闻数据...`)
    
    // 动态获取 API 基础地址
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 
      (import.meta.env.DEV ? 
        (window.location.hostname === 'localhost' || window.location.hostname.startsWith('192.168') ? 
          `http://${window.location.hostname}:3001` : 
          `http://${window.location.hostname}:8080`) : 
        '/api')
    const response = await fetch(`${apiBaseUrl}/api/news/all?lang=${language}`)
    const result = await response.json()
    
    if (!result.success) {
      throw new Error(result.message || '获取新闻数据失败')
    }
    
    const newsData = result.data
    console.log('从数据库获取到新闻数据:', newsData.length, '条')
    
    // 重新组织数据结构
    newsByCountry = {}
    allNews = []

    for (const news of newsData) {
      let countryInfo = null

      const foundCountry = countriesData.value.find(c => 
      c.name_zh === news.country || 
      c.name_en === news.country || 
      c.code === news.country
    )
        
      if (foundCountry) {
        countryInfo = {
          code: foundCountry.code,
          name_en: foundCountry.name_en,
          name_zh: foundCountry.name_zh,
          label_latitude: foundCountry.label_latitude,
          label_longitude: foundCountry.label_longitude,
          media_type: 'news'
        }
      } else {
        // 使用默认值
        countryInfo = {
          code: 'UNKNOWN',
          name_en: 'Unknown',
          name_zh: '未知',
          label_latitude: 12.0,
          label_longitude: 114.0,
          media_type: 'news'
        }
      }
      
      const countryName = countryInfo.name_en
      
      if (!newsByCountry[countryName]) {
        newsByCountry[countryName] = []
      }
      // 计算相对时间
      const relativeTime = getRelativeTime(news.publish_time, language)

      const newsItem = {
        id: news.id,
        time: relativeTime, // 相对时间，如5min前
        title: news.title || '',
        source: news.source || (language === 'zh' ? '未知来源' : 'Unknown Source'),
        content: news.content || '',
        keyword: news.keyword || '',
        country: countryName,
        link: news.link || '',
        publish_time: news.publish_time,
        created_at: news.created_at // 保存创建时间，用于判断是否为新新闻
      }
      
      newsByCountry[countryName].push(newsItem)
      allNews.push(newsItem)
    
      // 将初始化加载的新闻标记为已播放，这样就不会显示NEW标识
      playedNewsIds.add(newsItem.id)
    }

    // 按发布时间倒序排列（最新的在前）
    allNews.sort((a, b) => new Date(b.publish_time) - new Date(a.publish_time))
    
    console.log('新闻数据加载完成，共', allNews.length, '条新闻')
    console.log('涉及国家:', Object.keys(newsByCountry))
    
    // 更新国家标签语言
    updateCountryLabels(language)
    
    // 如果新闻轮播还没有开始，重新启动轮播
    if (allNews.length > 0 && !isDisplayingNews && startNewsRotation) {
      currentNewsIndex = 0
      setTimeout(() => {
        console.log('语言切换后重新开始新闻轮播...')
        startNewsRotation()
      }, 1000)
    }
    
    return true
  } catch (error) {
    console.error('从数据库加载新闻失败:', error)
  
    return false
  }
}

// 更新国家标签语言的函数
function updateCountryLabels(language) {
  if (!labels || labels.size === 0) return
  
  console.log(`更新国家标签为${language === 'zh' ? '中文' : '英文'}`)
  
  // 生成动态的标签映射
  const countryLabelMapping = generateCountryLabelMapping()
  
  labels.forEach((labelEntity, countryName) => {
    if (labelEntity && labelEntity.label) {
      const labelText = countryLabelMapping[countryName] 
        ? countryLabelMapping[countryName][language] 
        : countryName
      
      labelEntity.label.text = labelText
      console.log(`更新标签: ${countryName} -> ${labelText}`)
    }
  })
}

// 语言切换函数
const switchLanguage = async (lang) => {
  if (currentLanguage.value !== lang) {
    currentLanguage.value = lang
    console.log(`切换语言到: ${lang}`)

    // 更新页面标题
    document.title = lang === 'zh' ? '南海新闻监控系统' : 'South China Sea News Monitor'
       
    // 重新加载新闻数据
    try {
      await loadNewsFromDatabase(currentLanguage.value)
      console.log('语言切换后新闻数据重新加载完成')
    } catch (error) {
      console.error('切换语言后重新加载新闻失败:', error)
    }
  }
}

// 主体
onMounted(async () => {
  // 设置页面标题
  document.title = currentLanguage.value === 'zh' ? '南海新闻监控系统' : 'South China Sea News Monitor'
  // 等待DOM完全渲染
  await nextTick()
  
  // 检查容器是否存在
  const container = document.getElementById('cesiumContainer')
  if (!container) {
    console.error('找不到 cesiumContainer 元素，请检查模板')
    return
  }
  
  // 清理可能残留的实例
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
  
  if (handler) {
    handler.destroy()
    handler = null
  }

  if (newsRotationInterval) {
    clearInterval(newsRotationInterval)
    newsRotationInterval = null
  }

  if (typewriterTimeout) {
    clearTimeout(typewriterTimeout)
    typewriterTimeout = null
  }

  // 等待DOM完全加载
  await new Promise(resolve => setTimeout(resolve, 100))
  
  // 初始化 Cesium
  try {
    // 设置 Cesium 静态资源路径
    window.CESIUM_BASE_URL = '/cesium'

    // 设置 Cesium Ion Token
    Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiIyZTczMjdiMC01NzQ1LTQ2ZjctYTExNy1mNTVhMGUwN2M1ZDkiLCJpZCI6MzM3NTAyLCJpYXQiOjE3NTY4ODU2ODN9.W6eI1DMihJ29MqYbk7mvoc3D042roJgpQlMEAoIAoBU'

    console.log('开始初始化 Cesium Viewer...')

    // 初始化 Cesium Viewer
    viewer = new Viewer('cesiumContainer', {
      terrain: Terrain.fromWorldTerrain(),
      infoBox: false,
      selectionIndicator: false,
      animation: false,
      timeline: false,
      geocoder: false,
      homeButton: false,
      sceneModePicker: false,
      baseLayerPicker: false,
      navigationHelpButton: false,
      fullscreenButton: false,
      vrButton: false,
      creditContainer: document.createElement('div')
    })

    viewer.cesiumWidget.creditContainer.style.display = 'none'

    console.log('Cesium Viewer 初始化成功')

    // 相机视角
    viewer.camera.setView({
      destination: Cartesian3.fromDegrees(114, 12, 20000000)
    })

    // 延迟加载 GeoJSON，确保 Viewer 完全初始化
    setTimeout(async () => {
      try {
        console.log('开始加载国界数据...')

        const dataSource = await GeoJsonDataSource.load(
        //   'https://raw.githubusercontent.com/datasets/geo-boundaries-world-110m/master/countries.geojson',
            'countries.geo.json',
          {
            stroke: Color.WHITE,
            fill: Color.TRANSPARENT,
            strokeWidth: 5,
          }
        )
        
        console.log('国界数据加载成功，开始添加到场景...')
        
        if (!viewer) {
          console.error('Viewer 已被销毁')
          return
        }
        
        await viewer.dataSources.add(dataSource)
        console.log('数据源添加成功')
        
        const entities = dataSource.entities.values
        console.log('实体数量:', entities.length)
                
        // 首先加载国家配置数据
        console.log('加载国家配置数据...')
        const countriesLoaded = await loadCountriesFromDatabase()
        if (!countriesLoaded) {
          console.error('加载国家配置失败，使用默认配置')
          // 如果加载失败，至少添加一个默认的Unknown国家
          countriesData.value = [{
            code: 'UNKNOWN',
            name_en: 'Unknown',
            name_zh: '未知',
            label_latitude: 12.0,
            label_longitude: 114.0
          }]
        }
        
        // 初始化加载数据
        await loadNewsFromDatabase(currentLanguage.value)
        // 定义所需国家 - 这些将从数据库动态加载
        const supportedCountries = countriesData.value.map(country => country.name_en)
        
        // 生成动态的国家位置映射
        const countryLabelPositions = generateCountryPositions()

        // 初始化labels
        labels = new Map()
        const countryCenters = new Map()
        let currentPin = null // 用于存储当前的pin
        console.log('supportedCountries:', supportedCountries);
        
        // 国家标签创建 - 使用从数据库加载的国家
        supportedCountries.forEach(name => {
          // 跳过Unknown国家
          if (name === 'Unknown') {
            return
          }
          // 直接使用从数据库获取的位置
          const coords = countryLabelPositions[name]
          if (!coords) {
            console.warn(`未找到国家 ${name} 的位置配置`)
            return
          }
        
          const position = Cartesian3.fromDegrees(coords.lon, coords.lat)

          if (!viewer) return

          // 根据当前语言获取标签文本
          // 生成动态的标签映射
          const countryLabelMapping = generateCountryLabelMapping()
          const labelText = countryLabelMapping[name] 
            ? countryLabelMapping[name][currentLanguage.value] 
            : name

          const labelEntity = viewer.entities.add({
            position: position,
            label: new LabelGraphics({
              text: labelText,
              font: '24px sans-serif',
              fillColor: Color.YELLOW,
              // outlineColor: Color.WHITE,
              // outlineWidth: 1,
              style: LabelStyle.FILL_AND_OUTLINE,
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
              scale: 1.0,
              scaleByDistance: new NearFarScalar(
                10000000,    // 近距离 (1,000km)
                1.5,        // 近距离时的缩放倍数 (放大2.5倍)
                20000000,   // 远距离 (20,000km) 
                0.6         // 远距离时的缩放倍数 (缩小到0.6倍)
              )
            })
          })
          
          labels.set(name, labelEntity)
          countryCenters.set(name, position)
          
          console.log(`创建国家标签: ${name} -> ${labelText}`)
        })

        // 根据新闻数量选择颜色和大小 - 动态阈值版本
        function getNewsCountStyle(newsCount, allNewsCounts) {
          // 获取所有国家的新闻数量并排序
          const sortedCounts = allNewsCounts.filter(count => count > 0).sort((a, b) => b - a)
          
          if (sortedCounts.length === 0) {
            return {
              backgroundColor: '#66cc00',
              textColor: 'white',
              fontSize: 20,
              badgeSize: 32
            }
          }
          
          // 计算百分位数阈值
          const maxCount = sortedCounts[0]
          const minCount = sortedCounts[sortedCounts.length - 1]
          const range = maxCount - minCount
          
          // 如果所有国家新闻数量相同，使用默认样式
          if (range === 0) {
            return {
              backgroundColor: '#66cc00',
              textColor: 'white',
              fontSize: 20,
              badgeSize: 32
            }
          }
          
          // 计算当前新闻数量的百分位
          const percentile = (newsCount - minCount) / range
          
          let backgroundColor = '#66cc00' // 绿色
          let textColor = 'white'
          let fontSize = 20
          let badgeSize = 32

          if (percentile >= 0.8) {
            // 前20%：深红色
            backgroundColor = '#ff0000'
            fontSize = 24
            badgeSize = 40
          } else if (percentile >= 0.6) {
            // 60-80%：橙红色
            backgroundColor = '#ff6600'
            fontSize = 23
            badgeSize = 38
          } else if (percentile >= 0.4) {
            // 40-60%：橙色
            backgroundColor = '#ff9900'
            fontSize = 22
            badgeSize = 36
          } else if (percentile >= 0.2) {
            // 20-40%：黄色
            backgroundColor = '#ffcc00'
            textColor = 'black'
            fontSize = 21
            badgeSize = 34
          }
          // 0-20%：保持默认绿色

          return {
            backgroundColor,
            textColor,
            fontSize,
            badgeSize
          }
        }

        // 创建所有国家的数字pin（新闻数量）- 始终显示
        function createAllNewsCountPins() {
          // 先清除旧的数字pin
          newsCountPins.forEach(pin => {
            if (viewer && viewer.entities && !viewer.isDestroyed()) {
              viewer.entities.remove(pin)
            }
          })
          newsCountPins.clear()

          if (!viewer || !viewer.entities || viewer.isDestroyed()) {
            console.warn('Viewer 不可用，跳过创建数字pin')
            return
          }

          // 生成动态的国家位置映射
          const countryLabelPositions = generateCountryPositions()
          // 收集所有国家的新闻数量用于动态阈值计算
          const allNewsCounts = Object.keys(newsByCountry).map(countryName => 
            newsByCountry[countryName]?.length || 0
          )

          Object.keys(newsByCountry).forEach(countryName => {
            if (countryName === 'Unknown') {
              return
            }
            const newsCount = newsByCountry[countryName]?.length || 0
            if (newsCount > 0 && countryLabelPositions[countryName]) {
              const coords = countryLabelPositions[countryName]
              // 放在国家标签下方，偏移2.8度
              const pinPosition = Cartesian3.fromDegrees(coords.lon, coords.lat - 2.8)
              
              // 使用动态样式计算
              const style = getNewsCountStyle(newsCount, allNewsCounts)

              // 创建canvas绘制数字徽章
              const canvas = document.createElement('canvas')
              const ctx = canvas.getContext('2d')
              const size = style.badgeSize + 6
              canvas.width = size
              canvas.height = size

              // 绘制阴影
              ctx.beginPath()
              ctx.arc(size/2 + 1, size/2 + 1, size/2 - 3, 0, 2 * Math.PI)
              ctx.fillStyle = 'rgba(0,0,0,0.3)'
              ctx.fill()

              // 绘制白色边框
              ctx.beginPath()
              ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI)
              ctx.fillStyle = 'white'
              ctx.fill()
              
              // 绘制主圆
              ctx.beginPath()
              ctx.arc(size/2, size/2, size/2 - 4, 0, 2 * Math.PI)
              ctx.fillStyle = style.backgroundColor
              ctx.fill()
              
              // 绘制文字
              ctx.fillStyle = style.textColor
              ctx.font = `bold ${style.fontSize}px Arial`
              ctx.textAlign = 'center'
              ctx.textBaseline = 'middle'
              
              // 添加文字描边效果
              if (style.textColor === 'white') {
                ctx.strokeStyle = 'rgba(0,0,0,0.5)'
                ctx.lineWidth = 1
                ctx.strokeText(newsCount.toString(), size/2, size/2)
              }
              
              ctx.fillText(newsCount.toString(), size/2, size/2)

              // 将canvas转换为图片URL
              const imageUrl = canvas.toDataURL('image/png')

              const pinEntity = viewer.entities.add({
                position: pinPosition,
                billboard: {
                  image: imageUrl,
                  verticalOrigin: VerticalOrigin.CENTER,
                  disableDepthTestDistance: Number.POSITIVE_INFINITY,
                  scale: 1.0,
                  scaleByDistance: new NearFarScalar(
                    10000000,    // 近距离
                    1.5,        // 放大2.5倍
                    20000000,   // 远距离
                    0.6         // 缩小到0.6倍
                  )
                }
              })

              newsCountPins.set(countryName, pinEntity)
              console.log(`为 ${countryName} 创建数字pin: ${newsCount} 条新闻 (百分位: ${((newsCount - Math.min(...allNewsCounts)) / (Math.max(...allNewsCounts) - Math.min(...allNewsCounts)) * 100).toFixed(1)}%)`)
            }
          })
        }
        // 显示高亮pin（只在聚焦某国家时显示）
        function showHighlightPin(countryName) {
          // 生成动态的国家位置映射
          const countryLabelPositions = generateCountryPositions()
          const coords = countryLabelPositions[countryName]
          if (!coords) return
          
          // 放在国家标签上方，偏移0.8度
          const pinPosition = Cartesian3.fromDegrees(coords.lon, coords.lat + 0.8)
          const pinImageUrl = 'https://cdn-icons-png.flaticon.com/512/684/684908.png'
          
          highlightPin = viewer.entities.add({
            position: pinPosition,
            billboard: {
              image: pinImageUrl,
              verticalOrigin: VerticalOrigin.BOTTOM,
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
              scale: 0.1,
              scaleByDistance: new NearFarScalar(
                10000000,    // 近距离
                0.9,        // 放大2倍
                20000000,   // 远距离
                0.5         // 正常大小
              )
            }
          })
          
          console.log(`为 ${countryName} 显示高亮pin`)
        }

        // 移除高亮pin
        function removeHighlightPin() {
          if (highlightPin && viewer && viewer.entities) {
            viewer.entities.remove(highlightPin)
            highlightPin = null
          }
        }
        // 初始创建所有数字pin
        createAllNewsCountPins()
        
        console.log('准备开始新闻轮播，共', allNews.length, '条新闻')

        // ========== 新闻轮播系统 ==========
        // 打字机效果函数
        function typeWriter(element, text, speed = 20) {
          return new Promise((resolve) => {
            if (typewriterTimeout) {
              clearTimeout(typewriterTimeout)
              typewriterTimeout = null
            }
            element.innerHTML = ''
            let i = 0
            
            function type() {
              if (i < text.length) {
                element.innerHTML += text.charAt(i)
                i++
                // 如果是内容区域，自动滚动到底部
                if (element.id === 'newsBody') {
                  const container = document.getElementById('newsBodyContainer')
                  if (container) {
                    container.scrollTop = container.scrollHeight
                  }
                }

                typewriterTimeout = setTimeout(type, speed)
              } else {
                resolve()
              }
            }
            
            type()
          })
        }

        // 新闻显示函数
        async function displayNews(newsItem) {
          if (!viewer || isDisplayingNews) return
          
          isDisplayingNews = true
          
          // 移除之前的高亮pin
          removeHighlightPin()

          // 在当前位置提高到全球视角高度
          const currentPosition = viewer.camera.positionWC
          const currentCartographic = Cartographic.fromCartesian(currentPosition)
          
          await new Promise((resolve) => {
            viewer.camera.flyTo({
              destination: Cartesian3.fromRadians(
                currentCartographic.longitude,
                currentCartographic.latitude,
                20000000
              ),
              orientation: { 
                heading: 0, 
                pitch: CesiumMath.toRadians(-90), 
                roll: 0 
              },
              duration: 3,
              complete: resolve
            })
          })
          
          // 等待1秒后飞到对应国家
          await new Promise(resolve => setTimeout(resolve, 1000))

          // 飞到对应国家
          const pos = countryCenters.get(newsItem.country)
          if (pos) {
            const carto = Cartographic.fromCartesian(pos)
            const destination = Cartesian3.fromRadians(
              carto.longitude,
              carto.latitude,
              10000000
            )
            
            await new Promise((resolve) => {
              viewer.camera.flyTo({
                destination: destination,
                orientation: { heading: 0, pitch: CesiumMath.toRadians(-90), roll: 0 },
                duration: 3,
                complete: resolve
              })
            })
              // 显示当前国家的高亮pin
              showHighlightPin(newsItem.country)
          }else if (newsItem.country === 'Unknown') {
            // Unknown国家：飞到南海中心（原始位置）
            console.log('Unknown国家新闻，飞到南海中心位置')
            const southChinaSeaCenter = Cartesian3.fromDegrees(114, 12, 10000000)
            
            await new Promise((resolve) => {
              viewer.camera.flyTo({
                destination: southChinaSeaCenter,
                orientation: { heading: 0, pitch: CesiumMath.toRadians(-90), roll: 0 },
                duration: 3,
                complete: resolve
              })
            })           
            // Unknown国家不显示高亮pin
          } else {
            // 其他情况：保持当前位置
            console.log(`未找到国家 ${newsItem.country} 的位置配置，保持当前位置`)
          }
        
          // 显示新闻窗口
          const newsDisplay = document.getElementById('newsDisplay')
          const newsBadge = document.getElementById('newsBadge')
          const newsTime = document.getElementById('newsTime')
          const newsTitle = document.getElementById('newsTitle')
          const newsSource = document.getElementById('newsSource')
          const newsBody = document.getElementById('newsBody')
          const newsProgress = document.getElementById('newsProgress')

          currentNewsLink.value = newsItem.link || ''
          newsDisplay.style.display = 'block'
          newsTime.innerHTML = ''
          newsTitle.innerHTML = ''
          newsSource.innerHTML = ''
          newsBody.innerHTML = ''
          newsProgress.innerHTML = ''

          // 判断是否为首次播放的新闻
          const isNewNews = !playedNewsIds.has(newsItem.id)
          if (isNewNews) {
            newsBadge.style.display = 'block'
            console.log(`首次播放新闻: ${newsItem.title} (ID: ${newsItem.id})`)
            playedNewsIds.add(newsItem.id) // 标记为已播放
          } else {
            newsBadge.style.display = 'none'
          }

          // 根据当前语言获取国家显示名称
          // 生成动态的标签映射
          const countryLabelMapping = generateCountryLabelMapping()
          const countryDisplayName = countryLabelMapping[newsItem.country] 
            ? countryLabelMapping[newsItem.country][currentLanguage.value] 
            : newsItem.country

          // 更新进度信息
          const progressText = currentLanguage.value === 'zh' 
            ? `第 ${currentNewsIndex + 1} / ${allNews.length} 条新闻 | ${countryDisplayName}`
            : `News ${currentNewsIndex + 1} / ${allNews.length} | ${countryDisplayName}`
          
          newsProgress.innerHTML = progressText

          // 最新新闻，加入动画效果
          if (isNewNews) {
            newsBadge.style.animation = 'none'
            setTimeout(() => {
              newsBadge.style.animation = 'pulse 1.5s ease-in-out infinite'
            }, 100)
          }

          // 逐字显示新闻内容
          await typeWriter(newsTime, newsItem.time || '', 10)
          await new Promise(resolve => setTimeout(resolve, 500))
          
          await typeWriter(newsTitle, newsItem.title || '', 20)
          await new Promise(resolve => setTimeout(resolve, 500))
          
          const sourceText = currentLanguage.value === 'zh' 
            ? `来源：${newsItem.source || '未知'}` 
            : `Source: ${newsItem.source || 'Unknown'}`
          await typeWriter(newsSource, sourceText, 10)
          await new Promise(resolve => setTimeout(resolve, 800))
          
          // const keywords = (newsItem.keyword || '').split(',').map(k => k.trim()).filter(Boolean)
          // 逐字显示新闻关键词（根据语言调整速度）
          // const keywordSpeed = currentLanguage.value === 'zh' ? 40 : 10
          // await typeWriter(newsBody, newsItem.keyword || '', keywordSpeed)
          // 逐字显示新闻内容
          // const paragraphs = (newsItem.content || '').split(/\r?\n+/).filter(Boolean)
          // const firstTwoParagraphs = paragraphs.slice(0, 2).join('\n\n')
          const keywordSpeed = currentLanguage.value === 'zh' ? 40 : 10
          await typeWriter(newsBody, newsItem.content, keywordSpeed)

          // 新闻显示完毕后停留2秒
          newsProgress.innerHTML = progressText
          await new Promise(resolve => setTimeout(resolve, 3000))

          // 隐藏新闻窗口
          newsDisplay.style.display = 'none'
          newsBadge.style.display = 'none'
          
          // 移除高亮pin（回到全球视角时）
          removeHighlightPin()

          isDisplayingNews = false  
        }

        // 新闻更新监听函数
        function addNewNews(country, newsItem) {
          if (!newsByCountry[country]) {
            newsByCountry[country] = []
          }
          newsByCountry[country].unshift(newsItem)
          
          const updatedAllNews = []
          Object.keys(newsByCountry).forEach(countryName => {
            newsByCountry[countryName].forEach(news => {
              updatedAllNews.push({
                ...news,
                country: countryName
              })
            })
          })
          
          updatedAllNews.sort((a, b) => new Date(b.publish_time) - new Date(a.publish_time))
          
          allNews.length = 0
          allNews.push(...updatedAllNews)

          // 重新创建所有数字pin（更新数量）
          createAllNewsCountPins()

          console.log(`新增新闻: ${newsItem.title} (${country})`)
          console.log(`当前总新闻数: ${allNews.length}`)
          
          if (!isDisplayingNews) {
            console.log('立即跳转到新新闻')
            currentNewsIndex = 0
            interruptAndShowLatest()
          } else {
            console.log('当前正在播放新闻，将在下次轮播时显示新新闻')
            shouldJumpToLatest = true
          }
        }
        
        // 中断当前播放并跳转到最新新闻
        function interruptAndShowLatest() {
          if (allNews.length === 0) return
          
          if (typewriterTimeout) {
            clearTimeout(typewriterTimeout)
            typewriterTimeout = null
          }
          
          const newsDisplay = document.getElementById('newsDisplay')
          const newsBadge = document.getElementById('newsBadge')
          if (newsDisplay) {
            newsDisplay.style.display = 'none'
          }
          if (newsBadge) {
            newsBadge.style.display = 'none'
          }
          
          isDisplayingNews = false
          currentNewsIndex = 0
          
          setTimeout(() => {
            console.log('开始播放最新新闻...')
            displayNews(allNews[currentNewsIndex]).then(() => {
              currentNewsIndex = (currentNewsIndex + 1) % allNews.length
              setTimeout(() => {
                if (viewer && startNewsRotation) {
                  startNewsRotation()
                }
              }, 1000)
            })
          }, 500)
        }

        // 新闻轮播函数 - 赋值给外部变量
        startNewsRotation = function() {
          if (allNews.length === 0) {
            console.log('没有新闻数据')
            return
          }

          if (shouldJumpToLatest) {
            shouldJumpToLatest = false
            currentNewsIndex = 0
            console.log('跳转到最新新闻')
          }

          displayNews(allNews[currentNewsIndex]).then(() => {
            currentNewsIndex = (currentNewsIndex + 1) % allNews.length
            
            setTimeout(() => {
              if (viewer && startNewsRotation) {
                startNewsRotation()
              }
            }, 1000)
          })
        }

        // 暴露全局函数
        window.addNewNews = addNewNews

        // 等待3秒后开始首次轮播
        if (allNews.length > 0) {
          setTimeout(() => {
            console.log('开始新闻轮播...')
            startNewsRotation()
          }, 2000)
        }

        // 实时监听新新闻
        let lastCheckTime = new Date().toISOString()

        setInterval(async () => {
        try {
            const currentCheckTime = new Date().toISOString()
            // 动态获取 API 基础地址
            const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 
              (import.meta.env.DEV ? 
                (window.location.hostname === 'localhost' || window.location.hostname.startsWith('192.168') ? 
                  `http://${window.location.hostname}:3001` : 
                  `http://${window.location.hostname}:8080`) : 
                '/api')
            const response = await fetch(`${apiBaseUrl}/api/news/latest?since=${lastCheckTime}&lang=${currentLanguage.value}`)
            const result = await response.json()
            
            if (result.success && result.data.length > 0) {
            console.log(`发现 ${result.data.length} 条新新闻`)
            
            // 创建现有新闻ID集合用于去重
            const existingNewsIds = new Set(allNews.map(news => news.id))
            let newNewsAdded = 0
            
            result.data.forEach(async news => {
                // 检查是否已存在
                if (existingNewsIds.has(news.id)) {
                console.log(`跳过重复新闻: ${news.title} (ID: ${news.id})`)
                return
                }
                
                let countryInfo = null               
                // 获取国家信息
                if (!countryInfo) {
                  const foundCountry = countriesData.value.find(c => 
                    c.name_zh === news.country || c.name_en === news.country || c.code === news.country
                  )
                  
                  if (foundCountry) {
                    countryInfo = {
                      code: foundCountry.code,
                      name_en: foundCountry.name_en,
                      name_zh: foundCountry.name_zh,
                      label_latitude: foundCountry.label_latitude,
                      label_longitude: foundCountry.label_longitude,
                      media_type: 'news'
                    }
                  } else {
                    countryInfo = {
                      code: 'UNKNOWN',
                      name_en: 'Unknown',
                      name_zh: '未知',
                      label_latitude: 12.0,
                      label_longitude: 114.0,
                      media_type: 'news'
                    }
                  }
                }
                
                const countryName = countryInfo.name_en
                
                // 计算相对时间
                const relativeTime = getRelativeTime(news.publish_time, currentLanguage.value)

                console.log(`原始时间: ${news.publish_time} -> 相对时间: ${relativeTime}`)
                
                const newsItem = {
                id: news.id,
                time: relativeTime, // 相对时间
                title: news.title || '',
                source: news.source || (currentLanguage.value === 'zh' ? '未知来源' : 'Unknown Source'),
                content: news.content || '',
                country: countryName,
                publish_time: news.publish_time,
                created_at: news.created_at // 保存创建时间
                }
                addNewNews(countryName, newsItem)
                newNewsAdded++
            })
            
            console.log(`实际新增 ${newNewsAdded} 条新闻`)
            }
            
            // 更新检查时间
            lastCheckTime = currentCheckTime
            
        } catch (error) {
            console.error('获取最新新闻失败:', error)
        }
        }, 300000) // 每300秒检查一次
        
        // 添加定时器 - 定期更新相对时间
        timeUpdateInterval = setInterval(() => {
          if (allNews.length === 0) return

          console.log('更新所有新闻的相对时间...')
          
          // 更新所有新闻的相对时间
          allNews.forEach(newsItem => {
            const oldTime = newsItem.time
            newsItem.time = getRelativeTime(newsItem.publish_time, currentLanguage.value)
            
            // 同时更新 newsByCountry 中的数据
            if (newsByCountry[newsItem.country]) {
              const countryNews = newsByCountry[newsItem.country].find(n => n.id === newsItem.id)
              if (countryNews) {
                countryNews.time = newsItem.time
              }
            }
          })
          
          // 如果当前有新闻正在显示，更新显示的时间（但不打断打字机效果）
          if (!isDisplayingNews) {
            const newsTimeElement = document.getElementById('newsTime')
            if (newsTimeElement && newsTimeElement.innerHTML) {
              // 找到当前显示的新闻
              const currentNews = allNews[currentNewsIndex]
              if (currentNews) {
                newsTimeElement.innerHTML = currentNews.time
              }
            }
          }
          
          console.log(`已更新 ${allNews.length} 条新闻的相对时间`)
        }, 60000) // 每60秒更新一次相对时间

        // 动态隐藏背面的标签
        viewer.scene.postUpdate.addEventListener(() => {
          if (!viewer) return
          
          const cameraPosition = viewer.camera.positionWC

          function updateVisibility(entities, type = 'label') {
            entities.forEach((entity) => {
              const pos = entity.position.getValue(JulianDate.now())
              if (!pos) return
              const angle = Cartesian3.angleBetween(cameraPosition, pos)
              if (type === 'label' && entity.label) {
                entity.label.show = angle <= Math.PI / 2
              }
              if (type === 'billboard' && entity.billboard) {
                entity.billboard.show = angle <= Math.PI / 2
              }
            })
          }

          updateVisibility(Array.from(labels.values()), 'label')
          updateVisibility(Array.from(newsCountPins.values()), 'billboard')
          if (highlightPin) updateVisibility([highlightPin], 'billboard')
        })

        console.log('所有功能初始化完成')
        
      } catch (error) {
        console.error('国界加载失败:', error)
      }
    }, 500)

  } catch (error) {
    console.error('Cesium 初始化失败:', error)
  }
})

// 组件卸载时清理资源
onUnmounted(() => {
  console.log('清理 Cesium 资源...')

  if (timeUpdateInterval) {  // 清理定时器
    clearInterval(timeUpdateInterval)
    timeUpdateInterval = null
  }
  // 清理数字pin
  if (newsCountPins) {
    newsCountPins.forEach(pin => {
      if (viewer && viewer.entities) {
        viewer.entities.remove(pin)
      }
    })
    newsCountPins.clear()
  }
  
  // 清理高亮pin
  if (highlightPin && viewer && viewer.entities) {
    viewer.entities.remove(highlightPin)
    highlightPin = null
  }

  if (newsRotationInterval) {
    clearInterval(newsRotationInterval)
    newsRotationInterval = null
  }

  if (typewriterTimeout) {
    clearTimeout(typewriterTimeout)
    typewriterTimeout = null
  }

  const newsDisplay = document.getElementById('newsDisplay')
  const newsBadge = document.getElementById('newsBadge')
  if (newsDisplay) {
    newsDisplay.style.display = 'none'
  }
  if (newsBadge) {
    newsBadge.style.display = 'none'
  }
  
  if (handler) {
    handler.destroy()
    handler = null
  }
  
  if (viewer) {
    viewer.destroy()
    viewer = null
  }
})
</script>

<style scoped>
/* 颜色、大小等格式设置 */
.cesium-viewer-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.cesium-container {
  width: 100%;
  height: 100%;
}

/* 语言切换按钮 */
.language-switcher {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 5px;
}

.lang-btn {
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  border: 1px solid #fff;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.lang-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.lang-btn.active {
  background: #007ACC;
  border-color: #007ACC;
}

/* 新闻显示窗口 */
.news-display {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 500px;
  max-height: 70vh;
  background: rgba(0, 0, 0, 0.85);
  color: white;
  padding: 20px;
  border-radius: 10px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: none;
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
  overflow: hidden; /* 改为隐藏滚动条 */
}

.news-content {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  position: relative;
  height: calc(70vh - 40px);
  display: flex;
  flex-direction: column;
}

/* NEW标识样式 */
.news-badge {
  position: absolute;
  top: -10px;
  left: -10px;
  z-index: 1001;
  display: none;
}

.new-indicator {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  letter-spacing: 1px;
}

/* 脉动动画 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4px 16px rgba(255, 107, 107, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
  }
}

/* 固定的头部信息区域 */
.news-header {
  flex-shrink: 0; /* 不允许收缩 */
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 15px;
}

/* 新闻进度信息 */
.news-progress {
  font-size: 18px;
  color: #e0e0e0;
  margin-bottom: 10px;
  text-align: right;
}

.news-header-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

/* 新闻时间 */
.news-time {
  font-size: 20px;
  color: #5fbdfc;
  margin-bottom: 0;
}

/* 直达链接 */
.news-link-inline {
  color: #5fbdfc;
  font-size: 20px;
  margin-left: 20px;
  text-decoration: none;
  transition: color 0.2s;
  white-space: nowrap;
}
.news-link-inline:hover {
  color: #ff6b6b;
}

/* 新闻标题 */
.news-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  line-height: 1.4;
  word-wrap: break-word; /* 长单词自动换行 */
  word-break: break-word;
}
/* 新闻来源 */
.news-source {
  font-size: 18px;
  color: #e0e0e0;
  margin-bottom: 15px;
}

/* 可滚动的内容容器 */
.news-body-container {
  flex: 1; /* 占据剩余空间 */
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动条 */
  padding-right: 5px; /* 为滚动条留出空间 */
}

/* 自定义滚动条样式 */
.news-body-container::-webkit-scrollbar {
  width: 6px;
}

.news-body-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.news-body-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.news-body-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 新闻内容 */
.news-body {
  font-size: 22px;
  line-height: 1.6;
  text-align: justify;
  word-wrap: break-word;
  word-break: break-word;
  white-space: pre-wrap;
  padding-right: 5px;
}

/* 响应式设计 - 小屏幕适配 */
@media (max-width: 768px) {
  .news-display {
    width: calc(100vw - 40px);
    left: 20px;
    right: 20px;
    max-height: 70vh;
    font-size: 14px;
  }
  
  .news-title {
    font-size: 20px;
  }
  
  .news-body {
    font-size: 18px;
  }
  
  .news-time, .news-source, .news-progress {
    font-size: 16px;
  }
}

/* 隐藏Cesium默认版权信息 */
:deep(.cesium-widget-credits) {
  display: none !important;
}

:deep(.cesium-widget) {
  outline: none;
}
</style>