/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.132
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as g,b as Me,c as be,d as Ie,e as B,f as _e,g as un}from"./chunk-64RSHJUE.js";import{a as R}from"./chunk-3SSKC3VN.js";import{a as Se}from"./chunk-ED5JPB3S.js";import{a as A,b as s}from"./chunk-LEYMRMBK.js";import{a as Sn,c as Rn,d as Ze,e as p}from"./chunk-VTAIKJXX.js";var Pt=Rn((en,nn)=>{/*! https://mths.be/punycode v1.4.0 by @mathias */(function(e){var n=typeof en=="object"&&en&&!en.nodeType&&en,t=typeof nn=="object"&&nn&&!nn.nodeType&&nn,o=typeof global=="object"&&global;(o.global===o||o.window===o||o.self===o)&&(e=o);var i,r=**********,a=36,u=1,d=26,m=38,l=700,w=72,E=128,T="-",M=/^xn--/,v=/[^\x20-\x7E]/,I=/[\x2E\u3002\uFF0E\uFF61]/g,N={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},F=a-u,k=Math.floor,x=String.fromCharCode,Q;function W(b){throw new RangeError(N[b])}function K(b,z){for(var D=b.length,Y=[];D--;)Y[D]=z(b[D]);return Y}function oe(b,z){var D=b.split("@"),Y="";D.length>1&&(Y=D[0]+"@",b=D[1]),b=b.replace(I,".");var re=b.split("."),se=K(re,z).join(".");return Y+se}function X(b){for(var z=[],D=0,Y=b.length,re,se;D<Y;)re=b.charCodeAt(D++),re>=55296&&re<=56319&&D<Y?(se=b.charCodeAt(D++),(se&64512)==56320?z.push(((re&1023)<<10)+(se&1023)+65536):(z.push(re),D--)):z.push(re);return z}function te(b){return K(b,function(z){var D="";return z>65535&&(z-=65536,D+=x(z>>>10&1023|55296),z=56320|z&1023),D+=x(z),D}).join("")}function Z(b){return b-48<10?b-22:b-65<26?b-65:b-97<26?b-97:a}function c(b,z){return b+22+75*(b<26)-((z!=0)<<5)}function f(b,z,D){var Y=0;for(b=D?k(b/l):b>>1,b+=k(b/z);b>F*d>>1;Y+=a)b=k(b/F);return k(Y+(F+1)*b/(b+m))}function h(b){var z=[],D=b.length,Y,re=0,se=E,ee=w,fe,he,me,de,pe,V,ge,Te,je;for(fe=b.lastIndexOf(T),fe<0&&(fe=0),he=0;he<fe;++he)b.charCodeAt(he)>=128&&W("not-basic"),z.push(b.charCodeAt(he));for(me=fe>0?fe+1:0;me<D;){for(de=re,pe=1,V=a;me>=D&&W("invalid-input"),ge=Z(b.charCodeAt(me++)),(ge>=a||ge>k((r-re)/pe))&&W("overflow"),re+=ge*pe,Te=V<=ee?u:V>=ee+d?d:V-ee,!(ge<Te);V+=a)je=a-Te,pe>k(r/je)&&W("overflow"),pe*=je;Y=z.length+1,ee=f(re-de,Y,de==0),k(re/Y)>r-se&&W("overflow"),se+=k(re/Y),re%=Y,z.splice(re++,0,se)}return te(z)}function y(b){var z,D,Y,re,se,ee,fe,he,me,de,pe,V=[],ge,Te,je,fn;for(b=X(b),ge=b.length,z=E,D=0,se=w,ee=0;ee<ge;++ee)pe=b[ee],pe<128&&V.push(x(pe));for(Y=re=V.length,re&&V.push(T);Y<ge;){for(fe=r,ee=0;ee<ge;++ee)pe=b[ee],pe>=z&&pe<fe&&(fe=pe);for(Te=Y+1,fe-z>k((r-D)/Te)&&W("overflow"),D+=(fe-z)*Te,z=fe,ee=0;ee<ge;++ee)if(pe=b[ee],pe<z&&++D>r&&W("overflow"),pe==z){for(he=D,me=a;de=me<=se?u:me>=se+d?d:me-se,!(he<de);me+=a)fn=he-de,je=a-de,V.push(x(c(de+fn%je,0))),he=k(fn/je);V.push(x(c(he,0))),se=f(D,Te,Y==re),D=0,++Y}++D,++z}return V.join("")}function O(b){return oe(b,function(z){return M.test(z)?h(z.slice(4).toLowerCase()):z})}function C(b){return oe(b,function(z){return v.test(z)?"xn--"+y(z):z})}if(i={version:"1.3.2",ucs2:{decode:X,encode:te},decode:h,encode:y,toASCII:C,toUnicode:O},typeof define=="function"&&typeof define.amd=="object"&&define.amd)define("punycode",function(){return i});else if(n&&t)if(nn.exports==n)t.exports=i;else for(Q in i)i.hasOwnProperty(Q)&&(n[Q]=i[Q]);else e.punycode=i})(en)});var zt=Rn((Ut,Mn)=>{/*!
 * URI.js - Mutating URLs
 * IPv6 Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,n){"use strict";typeof Mn=="object"&&Mn.exports?Mn.exports=n():typeof define=="function"&&define.amd?define(n):e.IPv6=n(e)})(Ut,function(e){"use strict";var n=e&&e.IPv6;function t(i){var r=i.toLowerCase(),a=r.split(":"),u=a.length,d=8;a[0]===""&&a[1]===""&&a[2]===""?(a.shift(),a.shift()):a[0]===""&&a[1]===""?a.shift():a[u-1]===""&&a[u-2]===""&&a.pop(),u=a.length,a[u-1].indexOf(".")!==-1&&(d=7);var m;for(m=0;m<u&&a[m]!=="";m++);if(m<d)for(a.splice(m,1,"0000");a.length<d;)a.splice(m,0,"0000");for(var l,w=0;w<d;w++){l=a[w].split("");for(var E=0;E<3&&(l[0]==="0"&&l.length>1);E++)l.splice(0,1);a[w]=l.join("")}var T=-1,M=0,v=0,I=-1,N=!1;for(w=0;w<d;w++)N?a[w]==="0"?v+=1:(N=!1,v>M&&(T=I,M=v)):a[w]==="0"&&(N=!0,I=w,v=1);v>M&&(T=I,M=v),M>1&&a.splice(T,M,""),u=a.length;var F="";for(a[0]===""&&(F=":"),w=0;w<u&&(F+=a[w],w!==u-1);w++)F+=":";return a[u-1]===""&&(F+=":"),F}function o(){return e.IPv6===this&&(e.IPv6=n),this}return{best:t,noConflict:o}})});var qt=Rn((It,Pn)=>{/*!
 * URI.js - Mutating URLs
 * Second Level Domain (SLD) Support
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,n){"use strict";typeof Pn=="object"&&Pn.exports?Pn.exports=n():typeof define=="function"&&define.amd?define(n):e.SecondLevelDomains=n(e)})(It,function(e){"use strict";var n=e&&e.SecondLevelDomains,t={list:{ac:" com gov mil net org ",ae:" ac co gov mil name net org pro sch ",af:" com edu gov net org ",al:" com edu gov mil net org ",ao:" co ed gv it og pb ",ar:" com edu gob gov int mil net org tur ",at:" ac co gv or ",au:" asn com csiro edu gov id net org ",ba:" co com edu gov mil net org rs unbi unmo unsa untz unze ",bb:" biz co com edu gov info net org store tv ",bh:" biz cc com edu gov info net org ",bn:" com edu gov net org ",bo:" com edu gob gov int mil net org tv ",br:" adm adv agr am arq art ato b bio blog bmd cim cng cnt com coop ecn edu eng esp etc eti far flog fm fnd fot fst g12 ggf gov imb ind inf jor jus lel mat med mil mus net nom not ntr odo org ppg pro psc psi qsl rec slg srv tmp trd tur tv vet vlog wiki zlg ",bs:" com edu gov net org ",bz:" du et om ov rg ",ca:" ab bc mb nb nf nl ns nt nu on pe qc sk yk ",ck:" biz co edu gen gov info net org ",cn:" ac ah bj com cq edu fj gd gov gs gx gz ha hb he hi hl hn jl js jx ln mil net nm nx org qh sc sd sh sn sx tj tw xj xz yn zj ",co:" com edu gov mil net nom org ",cr:" ac c co ed fi go or sa ",cy:" ac biz com ekloges gov ltd name net org parliament press pro tm ",do:" art com edu gob gov mil net org sld web ",dz:" art asso com edu gov net org pol ",ec:" com edu fin gov info med mil net org pro ",eg:" com edu eun gov mil name net org sci ",er:" com edu gov ind mil net org rochest w ",es:" com edu gob nom org ",et:" biz com edu gov info name net org ",fj:" ac biz com info mil name net org pro ",fk:" ac co gov net nom org ",fr:" asso com f gouv nom prd presse tm ",gg:" co net org ",gh:" com edu gov mil org ",gn:" ac com gov net org ",gr:" com edu gov mil net org ",gt:" com edu gob ind mil net org ",gu:" com edu gov net org ",hk:" com edu gov idv net org ",hu:" 2000 agrar bolt casino city co erotica erotika film forum games hotel info ingatlan jogasz konyvelo lakas media news org priv reklam sex shop sport suli szex tm tozsde utazas video ",id:" ac co go mil net or sch web ",il:" ac co gov idf k12 muni net org ",in:" ac co edu ernet firm gen gov i ind mil net nic org res ",iq:" com edu gov i mil net org ",ir:" ac co dnssec gov i id net org sch ",it:" edu gov ",je:" co net org ",jo:" com edu gov mil name net org sch ",jp:" ac ad co ed go gr lg ne or ",ke:" ac co go info me mobi ne or sc ",kh:" com edu gov mil net org per ",ki:" biz com de edu gov info mob net org tel ",km:" asso com coop edu gouv k medecin mil nom notaires pharmaciens presse tm veterinaire ",kn:" edu gov net org ",kr:" ac busan chungbuk chungnam co daegu daejeon es gangwon go gwangju gyeongbuk gyeonggi gyeongnam hs incheon jeju jeonbuk jeonnam k kg mil ms ne or pe re sc seoul ulsan ",kw:" com edu gov net org ",ky:" com edu gov net org ",kz:" com edu gov mil net org ",lb:" com edu gov net org ",lk:" assn com edu gov grp hotel int ltd net ngo org sch soc web ",lr:" com edu gov net org ",lv:" asn com conf edu gov id mil net org ",ly:" com edu gov id med net org plc sch ",ma:" ac co gov m net org press ",mc:" asso tm ",me:" ac co edu gov its net org priv ",mg:" com edu gov mil nom org prd tm ",mk:" com edu gov inf name net org pro ",ml:" com edu gov net org presse ",mn:" edu gov org ",mo:" com edu gov net org ",mt:" com edu gov net org ",mv:" aero biz com coop edu gov info int mil museum name net org pro ",mw:" ac co com coop edu gov int museum net org ",mx:" com edu gob net org ",my:" com edu gov mil name net org sch ",nf:" arts com firm info net other per rec store web ",ng:" biz com edu gov mil mobi name net org sch ",ni:" ac co com edu gob mil net nom org ",np:" com edu gov mil net org ",nr:" biz com edu gov info net org ",om:" ac biz co com edu gov med mil museum net org pro sch ",pe:" com edu gob mil net nom org sld ",ph:" com edu gov i mil net ngo org ",pk:" biz com edu fam gob gok gon gop gos gov net org web ",pl:" art bialystok biz com edu gda gdansk gorzow gov info katowice krakow lodz lublin mil net ngo olsztyn org poznan pwr radom slupsk szczecin torun warszawa waw wroc wroclaw zgora ",pr:" ac biz com edu est gov info isla name net org pro prof ",ps:" com edu gov net org plo sec ",pw:" belau co ed go ne or ",ro:" arts com firm info nom nt org rec store tm www ",rs:" ac co edu gov in org ",sb:" com edu gov net org ",sc:" com edu gov net org ",sh:" co com edu gov net nom org ",sl:" com edu gov net org ",st:" co com consulado edu embaixada gov mil net org principe saotome store ",sv:" com edu gob org red ",sz:" ac co org ",tr:" av bbs bel biz com dr edu gen gov info k12 name net org pol tel tsk tv web ",tt:" aero biz cat co com coop edu gov info int jobs mil mobi museum name net org pro tel travel ",tw:" club com ebiz edu game gov idv mil net org ",mu:" ac co com gov net or org ",mz:" ac co edu gov org ",na:" co com ",nz:" ac co cri geek gen govt health iwi maori mil net org parliament school ",pa:" abo ac com edu gob ing med net nom org sld ",pt:" com edu gov int net nome org publ ",py:" com edu gov mil net org ",qa:" com edu gov mil net org ",re:" asso com nom ",ru:" ac adygeya altai amur arkhangelsk astrakhan bashkiria belgorod bir bryansk buryatia cbg chel chelyabinsk chita chukotka chuvashia com dagestan e-burg edu gov grozny int irkutsk ivanovo izhevsk jar joshkar-ola kalmykia kaluga kamchatka karelia kazan kchr kemerovo khabarovsk khakassia khv kirov koenig komi kostroma kranoyarsk kuban kurgan kursk lipetsk magadan mari mari-el marine mil mordovia mosreg msk murmansk nalchik net nnov nov novosibirsk nsk omsk orenburg org oryol penza perm pp pskov ptz rnd ryazan sakhalin samara saratov simbirsk smolensk spb stavropol stv surgut tambov tatarstan tom tomsk tsaritsyn tsk tula tuva tver tyumen udm udmurtia ulan-ude vladikavkaz vladimir vladivostok volgograd vologda voronezh vrn vyatka yakutia yamal yekaterinburg yuzhno-sakhalinsk ",rw:" ac co com edu gouv gov int mil net ",sa:" com edu gov med net org pub sch ",sd:" com edu gov info med net org tv ",se:" a ac b bd c d e f g h i k l m n o org p parti pp press r s t tm u w x y z ",sg:" com edu gov idn net org per ",sn:" art com edu gouv org perso univ ",sy:" com edu gov mil net news org ",th:" ac co go in mi net or ",tj:" ac biz co com edu go gov info int mil name net nic org test web ",tn:" agrinet com defense edunet ens fin gov ind info intl mincom nat net org perso rnrt rns rnu tourism ",tz:" ac co go ne or ",ua:" biz cherkassy chernigov chernovtsy ck cn co com crimea cv dn dnepropetrovsk donetsk dp edu gov if in ivano-frankivsk kh kharkov kherson khmelnitskiy kiev kirovograd km kr ks kv lg lugansk lutsk lviv me mk net nikolaev od odessa org pl poltava pp rovno rv sebastopol sumy te ternopil uzhgorod vinnica vn zaporizhzhe zhitomir zp zt ",ug:" ac co go ne or org sc ",uk:" ac bl british-library co cym gov govt icnet jet lea ltd me mil mod national-library-scotland nel net nhs nic nls org orgn parliament plc police sch scot soc ",us:" dni fed isa kids nsn ",uy:" com edu gub mil net org ",ve:" co com edu gob info mil net org web ",vi:" co com k12 net org ",vn:" ac biz com edu gov health info int name net org pro ",ye:" co com gov ltd me net org plc ",yu:" ac co edu gov org ",za:" ac agric alt bourse city co cybernet db edu gov grondar iaccess imt inca landesign law mil net ngo nis nom olivetti org pix school tm web ",zm:" ac co com edu gov net org sch ",com:"ar br cn de eu gb gr hu jpn kr no qc ru sa se uk us uy za ",net:"gb jp se uk ",org:"ae",de:"com "},has:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return!1;var r=o.lastIndexOf(".",i-1);if(r<=0||r>=i-1)return!1;var a=t.list[o.slice(i+1)];return a?a.indexOf(" "+o.slice(r+1,i)+" ")>=0:!1},is:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return!1;var r=o.lastIndexOf(".",i-1);if(r>=0)return!1;var a=t.list[o.slice(i+1)];return a?a.indexOf(" "+o.slice(0,i)+" ")>=0:!1},get:function(o){var i=o.lastIndexOf(".");if(i<=0||i>=o.length-1)return null;var r=o.lastIndexOf(".",i-1);if(r<=0||r>=i-1)return null;var a=t.list[o.slice(i+1)];return!a||a.indexOf(" "+o.slice(r+1,i)+" ")<0?null:o.slice(r+1)},noConflict:function(){return e.SecondLevelDomains===this&&(e.SecondLevelDomains=n),this}};return t})});var Qe=Rn((Dt,Un)=>{/*!
 * URI.js - Mutating URLs
 *
 * Version: 1.19.11
 *
 * Author: Rodney Rehm
 * Web: http://medialize.github.io/URI.js/
 *
 * Licensed under
 *   MIT License http://www.opensource.org/licenses/mit-license
 *
 */(function(e,n){"use strict";typeof Un=="object"&&Un.exports?Un.exports=n(Pt(),zt(),qt()):typeof define=="function"&&define.amd?define(["./punycode","./IPv6","./SecondLevelDomains"],n):e.URI=n(e.punycode,e.IPv6,e.SecondLevelDomains,e)})(Dt,function(e,n,t,o){"use strict";var i=o&&o.URI;function r(c,f){var h=arguments.length>=1,y=arguments.length>=2;if(!(this instanceof r))return h?y?new r(c,f):new r(c):new r;if(c===void 0){if(h)throw new TypeError("undefined is not a valid argument for URI");typeof location<"u"?c=location.href+"":c=""}if(c===null&&h)throw new TypeError("null is not a valid argument for URI");return this.href(c),f!==void 0?this.absoluteTo(f):this}function a(c){return/^[0-9]+$/.test(c)}r.version="1.19.11";var u=r.prototype,d=Object.prototype.hasOwnProperty;function m(c){return c.replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}function l(c){return c===void 0?"Undefined":String(Object.prototype.toString.call(c)).slice(8,-1)}function w(c){return l(c)==="Array"}function E(c,f){var h={},y,O;if(l(f)==="RegExp")h=null;else if(w(f))for(y=0,O=f.length;y<O;y++)h[f[y]]=!0;else h[f]=!0;for(y=0,O=c.length;y<O;y++){var C=h&&h[c[y]]!==void 0||!h&&f.test(c[y]);C&&(c.splice(y,1),O--,y--)}return c}function T(c,f){var h,y;if(w(f)){for(h=0,y=f.length;h<y;h++)if(!T(c,f[h]))return!1;return!0}var O=l(f);for(h=0,y=c.length;h<y;h++)if(O==="RegExp"){if(typeof c[h]=="string"&&c[h].match(f))return!0}else if(c[h]===f)return!0;return!1}function M(c,f){if(!w(c)||!w(f)||c.length!==f.length)return!1;c.sort(),f.sort();for(var h=0,y=c.length;h<y;h++)if(c[h]!==f[h])return!1;return!0}function v(c){var f=/^\/+|\/+$/g;return c.replace(f,"")}r._parts=function(){return{protocol:null,username:null,password:null,hostname:null,urn:null,port:null,path:null,query:null,fragment:null,preventInvalidHostname:r.preventInvalidHostname,duplicateQueryParameters:r.duplicateQueryParameters,escapeQuerySpace:r.escapeQuerySpace}},r.preventInvalidHostname=!1,r.duplicateQueryParameters=!1,r.escapeQuerySpace=!0,r.protocol_expression=/^[a-z][a-z0-9.+-]*$/i,r.idn_expression=/[^a-z0-9\._-]/i,r.punycode_expression=/(xn--)/i,r.ip4_expression=/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/,r.ip6_expression=/^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*$/,r.find_uri_expression=/\b((?:[a-z][\w-]+:(?:\/{1,3}|[a-z0-9%])|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))/ig,r.findUri={start:/\b(?:([a-z][a-z0-9.+-]*:\/\/)|www\.)/gi,end:/[\s\r\n]|$/,trim:/[`!()\[\]{};:'".,<>?«»“”„‘’]+$/,parens:/(\([^\)]*\)|\[[^\]]*\]|\{[^}]*\}|<[^>]*>)/g},r.leading_whitespace_expression=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,r.ascii_tab_whitespace=/[\u0009\u000A\u000D]+/g,r.defaultPorts={http:"80",https:"443",ftp:"21",gopher:"70",ws:"80",wss:"443"},r.hostProtocols=["http","https"],r.invalid_hostname_characters=/[^a-zA-Z0-9\.\-:_]/,r.domAttributes={a:"href",blockquote:"cite",link:"href",base:"href",script:"src",form:"action",img:"src",area:"href",iframe:"src",embed:"src",source:"src",track:"src",input:"src",audio:"src",video:"src"},r.getDomAttribute=function(c){if(!(!c||!c.nodeName)){var f=c.nodeName.toLowerCase();if(!(f==="input"&&c.type!=="image"))return r.domAttributes[f]}};function I(c){return escape(c)}function N(c){return encodeURIComponent(c).replace(/[!'()*]/g,I).replace(/\*/g,"%2A")}r.encode=N,r.decode=decodeURIComponent,r.iso8859=function(){r.encode=escape,r.decode=unescape},r.unicode=function(){r.encode=N,r.decode=decodeURIComponent},r.characters={pathname:{encode:{expression:/%(24|26|2B|2C|3B|3D|3A|40)/ig,map:{"%24":"$","%26":"&","%2B":"+","%2C":",","%3B":";","%3D":"=","%3A":":","%40":"@"}},decode:{expression:/[\/\?#]/g,map:{"/":"%2F","?":"%3F","#":"%23"}}},reserved:{encode:{expression:/%(21|23|24|26|27|28|29|2A|2B|2C|2F|3A|3B|3D|3F|40|5B|5D)/ig,map:{"%3A":":","%2F":"/","%3F":"?","%23":"#","%5B":"[","%5D":"]","%40":"@","%21":"!","%24":"$","%26":"&","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"="}}},urnpath:{encode:{expression:/%(21|24|27|28|29|2A|2B|2C|3B|3D|40)/ig,map:{"%21":"!","%24":"$","%27":"'","%28":"(","%29":")","%2A":"*","%2B":"+","%2C":",","%3B":";","%3D":"=","%40":"@"}},decode:{expression:/[\/\?#:]/g,map:{"/":"%2F","?":"%3F","#":"%23",":":"%3A"}}}},r.encodeQuery=function(c,f){var h=r.encode(c+"");return f===void 0&&(f=r.escapeQuerySpace),f?h.replace(/%20/g,"+"):h},r.decodeQuery=function(c,f){c+="",f===void 0&&(f=r.escapeQuerySpace);try{return r.decode(f?c.replace(/\+/g,"%20"):c)}catch{return c}};var F={encode:"encode",decode:"decode"},k,x=function(c,f){return function(h){try{return r[f](h+"").replace(r.characters[c][f].expression,function(y){return r.characters[c][f].map[y]})}catch{return h}}};for(k in F)r[k+"PathSegment"]=x("pathname",F[k]),r[k+"UrnPathSegment"]=x("urnpath",F[k]);var Q=function(c,f,h){return function(y){var O;h?O=function(D){return r[f](r[h](D))}:O=r[f];for(var C=(y+"").split(c),b=0,z=C.length;b<z;b++)C[b]=O(C[b]);return C.join(c)}};r.decodePath=Q("/","decodePathSegment"),r.decodeUrnPath=Q(":","decodeUrnPathSegment"),r.recodePath=Q("/","encodePathSegment","decode"),r.recodeUrnPath=Q(":","encodeUrnPathSegment","decode"),r.encodeReserved=x("reserved","encode"),r.parse=function(c,f){var h;return f||(f={preventInvalidHostname:r.preventInvalidHostname}),c=c.replace(r.leading_whitespace_expression,""),c=c.replace(r.ascii_tab_whitespace,""),h=c.indexOf("#"),h>-1&&(f.fragment=c.substring(h+1)||null,c=c.substring(0,h)),h=c.indexOf("?"),h>-1&&(f.query=c.substring(h+1)||null,c=c.substring(0,h)),c=c.replace(/^(https?|ftp|wss?)?:+[/\\]*/i,"$1://"),c=c.replace(/^[/\\]{2,}/i,"//"),c.substring(0,2)==="//"?(f.protocol=null,c=c.substring(2),c=r.parseAuthority(c,f)):(h=c.indexOf(":"),h>-1&&(f.protocol=c.substring(0,h)||null,f.protocol&&!f.protocol.match(r.protocol_expression)?f.protocol=void 0:c.substring(h+1,h+3).replace(/\\/g,"/")==="//"?(c=c.substring(h+3),c=r.parseAuthority(c,f)):(c=c.substring(h+1),f.urn=!0))),f.path=c,f},r.parseHost=function(c,f){c||(c=""),c=c.replace(/\\/g,"/");var h=c.indexOf("/"),y,O;if(h===-1&&(h=c.length),c.charAt(0)==="[")y=c.indexOf("]"),f.hostname=c.substring(1,y)||null,f.port=c.substring(y+2,h)||null,f.port==="/"&&(f.port=null);else{var C=c.indexOf(":"),b=c.indexOf("/"),z=c.indexOf(":",C+1);z!==-1&&(b===-1||z<b)?(f.hostname=c.substring(0,h)||null,f.port=null):(O=c.substring(0,h).split(":"),f.hostname=O[0]||null,f.port=O[1]||null)}return f.hostname&&c.substring(h).charAt(0)!=="/"&&(h++,c="/"+c),f.preventInvalidHostname&&r.ensureValidHostname(f.hostname,f.protocol),f.port&&r.ensureValidPort(f.port),c.substring(h)||"/"},r.parseAuthority=function(c,f){return c=r.parseUserinfo(c,f),r.parseHost(c,f)},r.parseUserinfo=function(c,f){var h=c,y=c.indexOf("\\");y!==-1&&(c=c.replace(/\\/g,"/"));var O=c.indexOf("/"),C=c.lastIndexOf("@",O>-1?O:c.length-1),b;return C>-1&&(O===-1||C<O)?(b=c.substring(0,C).split(":"),f.username=b[0]?r.decode(b[0]):null,b.shift(),f.password=b[0]?r.decode(b.join(":")):null,c=h.substring(C+1)):(f.username=null,f.password=null),c},r.parseQuery=function(c,f){if(!c)return{};if(c=c.replace(/&+/g,"&").replace(/^\?*&*|&+$/g,""),!c)return{};for(var h={},y=c.split("&"),O=y.length,C,b,z,D=0;D<O;D++)C=y[D].split("="),b=r.decodeQuery(C.shift(),f),z=C.length?r.decodeQuery(C.join("="),f):null,b!=="__proto__"&&(d.call(h,b)?((typeof h[b]=="string"||h[b]===null)&&(h[b]=[h[b]]),h[b].push(z)):h[b]=z);return h},r.build=function(c){var f="",h=!1;return c.protocol&&(f+=c.protocol+":"),!c.urn&&(f||c.hostname)&&(f+="//",h=!0),f+=r.buildAuthority(c)||"",typeof c.path=="string"&&(c.path.charAt(0)!=="/"&&h&&(f+="/"),f+=c.path),typeof c.query=="string"&&c.query&&(f+="?"+c.query),typeof c.fragment=="string"&&c.fragment&&(f+="#"+c.fragment),f},r.buildHost=function(c){var f="";if(c.hostname)r.ip6_expression.test(c.hostname)?f+="["+c.hostname+"]":f+=c.hostname;else return"";return c.port&&(f+=":"+c.port),f},r.buildAuthority=function(c){return r.buildUserinfo(c)+r.buildHost(c)},r.buildUserinfo=function(c){var f="";return c.username&&(f+=r.encode(c.username)),c.password&&(f+=":"+r.encode(c.password)),f&&(f+="@"),f},r.buildQuery=function(c,f,h){var y="",O,C,b,z;for(C in c)if(C!=="__proto__"&&d.call(c,C))if(w(c[C]))for(O={},b=0,z=c[C].length;b<z;b++)c[C][b]!==void 0&&O[c[C][b]+""]===void 0&&(y+="&"+r.buildQueryParameter(C,c[C][b],h),f!==!0&&(O[c[C][b]+""]=!0));else c[C]!==void 0&&(y+="&"+r.buildQueryParameter(C,c[C],h));return y.substring(1)},r.buildQueryParameter=function(c,f,h){return r.encodeQuery(c,h)+(f!==null?"="+r.encodeQuery(f,h):"")},r.addQuery=function(c,f,h){if(typeof f=="object")for(var y in f)d.call(f,y)&&r.addQuery(c,y,f[y]);else if(typeof f=="string"){if(c[f]===void 0){c[f]=h;return}else typeof c[f]=="string"&&(c[f]=[c[f]]);w(h)||(h=[h]),c[f]=(c[f]||[]).concat(h)}else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter")},r.setQuery=function(c,f,h){if(typeof f=="object")for(var y in f)d.call(f,y)&&r.setQuery(c,y,f[y]);else if(typeof f=="string")c[f]=h===void 0?null:h;else throw new TypeError("URI.setQuery() accepts an object, string as the name parameter")},r.removeQuery=function(c,f,h){var y,O,C;if(w(f))for(y=0,O=f.length;y<O;y++)c[f[y]]=void 0;else if(l(f)==="RegExp")for(C in c)f.test(C)&&(c[C]=void 0);else if(typeof f=="object")for(C in f)d.call(f,C)&&r.removeQuery(c,C,f[C]);else if(typeof f=="string")h!==void 0?l(h)==="RegExp"?!w(c[f])&&h.test(c[f])?c[f]=void 0:c[f]=E(c[f],h):c[f]===String(h)&&(!w(h)||h.length===1)?c[f]=void 0:w(c[f])&&(c[f]=E(c[f],h)):c[f]=void 0;else throw new TypeError("URI.removeQuery() accepts an object, string, RegExp as the first parameter")},r.hasQuery=function(c,f,h,y){switch(l(f)){case"String":break;case"RegExp":for(var O in c)if(d.call(c,O)&&f.test(O)&&(h===void 0||r.hasQuery(c,O,h)))return!0;return!1;case"Object":for(var C in f)if(d.call(f,C)&&!r.hasQuery(c,C,f[C]))return!1;return!0;default:throw new TypeError("URI.hasQuery() accepts a string, regular expression or object as the name parameter")}switch(l(h)){case"Undefined":return f in c;case"Boolean":var b=!!(w(c[f])?c[f].length:c[f]);return h===b;case"Function":return!!h(c[f],f,c);case"Array":if(!w(c[f]))return!1;var z=y?T:M;return z(c[f],h);case"RegExp":return w(c[f])?y?T(c[f],h):!1:!!(c[f]&&c[f].match(h));case"Number":h=String(h);case"String":return w(c[f])?y?T(c[f],h):!1:c[f]===h;default:throw new TypeError("URI.hasQuery() accepts undefined, boolean, string, number, RegExp, Function as the value parameter")}},r.joinPaths=function(){for(var c=[],f=[],h=0,y=0;y<arguments.length;y++){var O=new r(arguments[y]);c.push(O);for(var C=O.segment(),b=0;b<C.length;b++)typeof C[b]=="string"&&f.push(C[b]),C[b]&&h++}if(!f.length||!h)return new r("");var z=new r("").segment(f);return(c[0].path()===""||c[0].path().slice(0,1)==="/")&&z.path("/"+z.path()),z.normalize()},r.commonPath=function(c,f){var h=Math.min(c.length,f.length),y;for(y=0;y<h;y++)if(c.charAt(y)!==f.charAt(y)){y--;break}return y<1?c.charAt(0)===f.charAt(0)&&c.charAt(0)==="/"?"/":"":((c.charAt(y)!=="/"||f.charAt(y)!=="/")&&(y=c.substring(0,y).lastIndexOf("/")),c.substring(0,y+1))},r.withinString=function(c,f,h){h||(h={});var y=h.start||r.findUri.start,O=h.end||r.findUri.end,C=h.trim||r.findUri.trim,b=h.parens||r.findUri.parens,z=/[a-z0-9-]=["']?$/i;for(y.lastIndex=0;;){var D=y.exec(c);if(!D)break;var Y=D.index;if(h.ignoreHtml){var re=c.slice(Math.max(Y-3,0),Y);if(re&&z.test(re))continue}for(var se=Y+c.slice(Y).search(O),ee=c.slice(Y,se),fe=-1;;){var he=b.exec(ee);if(!he)break;var me=he.index+he[0].length;fe=Math.max(fe,me)}if(fe>-1?ee=ee.slice(0,fe)+ee.slice(fe).replace(C,""):ee=ee.replace(C,""),!(ee.length<=D[0].length)&&!(h.ignore&&h.ignore.test(ee))){se=Y+ee.length;var de=f(ee,Y,se,c);if(de===void 0){y.lastIndex=se;continue}de=String(de),c=c.slice(0,Y)+de+c.slice(se),y.lastIndex=Y+de.length}}return y.lastIndex=0,c},r.ensureValidHostname=function(c,f){var h=!!c,y=!!f,O=!1;if(y&&(O=T(r.hostProtocols,f)),O&&!h)throw new TypeError("Hostname cannot be empty, if protocol is "+f);if(c&&c.match(r.invalid_hostname_characters)){if(!e)throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-:_] and Punycode.js is not available');if(e.toASCII(c).match(r.invalid_hostname_characters))throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-:_]')}},r.ensureValidPort=function(c){if(c){var f=Number(c);if(!(a(f)&&f>0&&f<65536))throw new TypeError('Port "'+c+'" is not a valid port')}},r.noConflict=function(c){if(c){var f={URI:this.noConflict()};return o.URITemplate&&typeof o.URITemplate.noConflict=="function"&&(f.URITemplate=o.URITemplate.noConflict()),o.IPv6&&typeof o.IPv6.noConflict=="function"&&(f.IPv6=o.IPv6.noConflict()),o.SecondLevelDomains&&typeof o.SecondLevelDomains.noConflict=="function"&&(f.SecondLevelDomains=o.SecondLevelDomains.noConflict()),f}else o.URI===this&&(o.URI=i);return this},u.build=function(c){return c===!0?this._deferred_build=!0:(c===void 0||this._deferred_build)&&(this._string=r.build(this._parts),this._deferred_build=!1),this},u.clone=function(){return new r(this)},u.valueOf=u.toString=function(){return this.build(!1)._string};function W(c){return function(f,h){return f===void 0?this._parts[c]||"":(this._parts[c]=f||null,this.build(!h),this)}}function K(c,f){return function(h,y){return h===void 0?this._parts[c]||"":(h!==null&&(h=h+"",h.charAt(0)===f&&(h=h.substring(1))),this._parts[c]=h,this.build(!y),this)}}u.protocol=W("protocol"),u.username=W("username"),u.password=W("password"),u.hostname=W("hostname"),u.port=W("port"),u.query=K("query","?"),u.fragment=K("fragment","#"),u.search=function(c,f){var h=this.query(c,f);return typeof h=="string"&&h.length?"?"+h:h},u.hash=function(c,f){var h=this.fragment(c,f);return typeof h=="string"&&h.length?"#"+h:h},u.pathname=function(c,f){if(c===void 0||c===!0){var h=this._parts.path||(this._parts.hostname?"/":"");return c?(this._parts.urn?r.decodeUrnPath:r.decodePath)(h):h}else return this._parts.urn?this._parts.path=c?r.recodeUrnPath(c):"":this._parts.path=c?r.recodePath(c):"/",this.build(!f),this},u.path=u.pathname,u.href=function(c,f){var h;if(c===void 0)return this.toString();this._string="",this._parts=r._parts();var y=c instanceof r,O=typeof c=="object"&&(c.hostname||c.path||c.pathname);if(c.nodeName){var C=r.getDomAttribute(c);c=c[C]||"",O=!1}if(!y&&O&&c.pathname!==void 0&&(c=c.toString()),typeof c=="string"||c instanceof String)this._parts=r.parse(String(c),this._parts);else if(y||O){var b=y?c._parts:c;for(h in b)h!=="query"&&d.call(this._parts,h)&&(this._parts[h]=b[h]);b.query&&this.query(b.query,!1)}else throw new TypeError("invalid input");return this.build(!f),this},u.is=function(c){var f=!1,h=!1,y=!1,O=!1,C=!1,b=!1,z=!1,D=!this._parts.urn;switch(this._parts.hostname&&(D=!1,h=r.ip4_expression.test(this._parts.hostname),y=r.ip6_expression.test(this._parts.hostname),f=h||y,O=!f,C=O&&t&&t.has(this._parts.hostname),b=O&&r.idn_expression.test(this._parts.hostname),z=O&&r.punycode_expression.test(this._parts.hostname)),c.toLowerCase()){case"relative":return D;case"absolute":return!D;case"domain":case"name":return O;case"sld":return C;case"ip":return f;case"ip4":case"ipv4":case"inet4":return h;case"ip6":case"ipv6":case"inet6":return y;case"idn":return b;case"url":return!this._parts.urn;case"urn":return!!this._parts.urn;case"punycode":return z}return null};var oe=u.protocol,X=u.port,te=u.hostname;u.protocol=function(c,f){if(c&&(c=c.replace(/:(\/\/)?$/,""),!c.match(r.protocol_expression)))throw new TypeError('Protocol "'+c+`" contains characters other than [A-Z0-9.+-] or doesn't start with [A-Z]`);return oe.call(this,c,f)},u.scheme=u.protocol,u.port=function(c,f){return this._parts.urn?c===void 0?"":this:(c!==void 0&&(c===0&&(c=null),c&&(c+="",c.charAt(0)===":"&&(c=c.substring(1)),r.ensureValidPort(c))),X.call(this,c,f))},u.hostname=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c!==void 0){var h={preventInvalidHostname:this._parts.preventInvalidHostname},y=r.parseHost(c,h);if(y!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');c=h.hostname,this._parts.preventInvalidHostname&&r.ensureValidHostname(c,this._parts.protocol)}return te.call(this,c,f)},u.origin=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){var h=this.protocol(),y=this.authority();return y?(h?h+"://":"")+this.authority():""}else{var O=r(c);return this.protocol(O.protocol()).authority(O.authority()).build(!f),this}},u.host=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0)return this._parts.hostname?r.buildHost(this._parts):"";var h=r.parseHost(c,this._parts);if(h!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');return this.build(!f),this},u.authority=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0)return this._parts.hostname?r.buildAuthority(this._parts):"";var h=r.parseAuthority(c,this._parts);if(h!=="/")throw new TypeError('Hostname "'+c+'" contains characters other than [A-Z0-9.-]');return this.build(!f),this},u.userinfo=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){var h=r.buildUserinfo(this._parts);return h&&h.substring(0,h.length-1)}else return c[c.length-1]!=="@"&&(c+="@"),r.parseUserinfo(c,this._parts),this.build(!f),this},u.resource=function(c,f){var h;return c===void 0?this.path()+this.search()+this.hash():(h=r.parse(c),this._parts.path=h.path,this._parts.query=h.query,this._parts.fragment=h.fragment,this.build(!f),this)},u.subdomain=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.length-this.domain().length-1;return this._parts.hostname.substring(0,h)||""}else{var y=this._parts.hostname.length-this.domain().length,O=this._parts.hostname.substring(0,y),C=new RegExp("^"+m(O));if(c&&c.charAt(c.length-1)!=="."&&(c+="."),c.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");return c&&r.ensureValidHostname(c,this._parts.protocol),this._parts.hostname=this._parts.hostname.replace(C,c),this.build(!f),this}},u.domain=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c=="boolean"&&(f=c,c=void 0),c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.match(/\./g);if(h&&h.length<2)return this._parts.hostname;var y=this._parts.hostname.length-this.tld(f).length-1;return y=this._parts.hostname.lastIndexOf(".",y-1)+1,this._parts.hostname.substring(y)||""}else{if(!c)throw new TypeError("cannot set domain empty");if(c.indexOf(":")!==-1)throw new TypeError("Domains cannot contain colons");if(r.ensureValidHostname(c,this._parts.protocol),!this._parts.hostname||this.is("IP"))this._parts.hostname=c;else{var O=new RegExp(m(this.domain())+"$");this._parts.hostname=this._parts.hostname.replace(O,c)}return this.build(!f),this}},u.tld=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c=="boolean"&&(f=c,c=void 0),c===void 0){if(!this._parts.hostname||this.is("IP"))return"";var h=this._parts.hostname.lastIndexOf("."),y=this._parts.hostname.substring(h+1);return f!==!0&&t&&t.list[y.toLowerCase()]&&t.get(this._parts.hostname)||y}else{var O;if(c)if(c.match(/[^a-zA-Z0-9-]/))if(t&&t.is(c))O=new RegExp(m(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(O,c);else throw new TypeError('TLD "'+c+'" contains characters other than [A-Z0-9]');else{if(!this._parts.hostname||this.is("IP"))throw new ReferenceError("cannot set TLD on non-domain host");O=new RegExp(m(this.tld())+"$"),this._parts.hostname=this._parts.hostname.replace(O,c)}else throw new TypeError("cannot set TLD empty");return this.build(!f),this}},u.directory=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0||c===!0){if(!this._parts.path&&!this._parts.hostname)return"";if(this._parts.path==="/")return"/";var h=this._parts.path.length-this.filename().length-1,y=this._parts.path.substring(0,h)||(this._parts.hostname?"/":"");return c?r.decodePath(y):y}else{var O=this._parts.path.length-this.filename().length,C=this._parts.path.substring(0,O),b=new RegExp("^"+m(C));return this.is("relative")||(c||(c="/"),c.charAt(0)!=="/"&&(c="/"+c)),c&&c.charAt(c.length-1)!=="/"&&(c+="/"),c=r.recodePath(c),this._parts.path=this._parts.path.replace(b,c),this.build(!f),this}},u.filename=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(typeof c!="string"){if(!this._parts.path||this._parts.path==="/")return"";var h=this._parts.path.lastIndexOf("/"),y=this._parts.path.substring(h+1);return c?r.decodePathSegment(y):y}else{var O=!1;c.charAt(0)==="/"&&(c=c.substring(1)),c.match(/\.?\//)&&(O=!0);var C=new RegExp(m(this.filename())+"$");return c=r.recodePath(c),this._parts.path=this._parts.path.replace(C,c),O?this.normalizePath(f):this.build(!f),this}},u.suffix=function(c,f){if(this._parts.urn)return c===void 0?"":this;if(c===void 0||c===!0){if(!this._parts.path||this._parts.path==="/")return"";var h=this.filename(),y=h.lastIndexOf("."),O,C;return y===-1?"":(O=h.substring(y+1),C=/^[a-z0-9%]+$/i.test(O)?O:"",c?r.decodePathSegment(C):C)}else{c.charAt(0)==="."&&(c=c.substring(1));var b=this.suffix(),z;if(b)c?z=new RegExp(m(b)+"$"):z=new RegExp(m("."+b)+"$");else{if(!c)return this;this._parts.path+="."+r.recodePath(c)}return z&&(c=r.recodePath(c),this._parts.path=this._parts.path.replace(z,c)),this.build(!f),this}},u.segment=function(c,f,h){var y=this._parts.urn?":":"/",O=this.path(),C=O.substring(0,1)==="/",b=O.split(y);if(c!==void 0&&typeof c!="number"&&(h=f,f=c,c=void 0),c!==void 0&&typeof c!="number")throw new Error('Bad segment "'+c+'", must be 0-based integer');if(C&&b.shift(),c<0&&(c=Math.max(b.length+c,0)),f===void 0)return c===void 0?b:b[c];if(c===null||b[c]===void 0)if(w(f)){b=[];for(var z=0,D=f.length;z<D;z++)!f[z].length&&(!b.length||!b[b.length-1].length)||(b.length&&!b[b.length-1].length&&b.pop(),b.push(v(f[z])))}else(f||typeof f=="string")&&(f=v(f),b[b.length-1]===""?b[b.length-1]=f:b.push(f));else f?b[c]=v(f):b.splice(c,1);return C&&b.unshift(""),this.path(b.join(y),h)},u.segmentCoded=function(c,f,h){var y,O,C;if(typeof c!="number"&&(h=f,f=c,c=void 0),f===void 0){if(y=this.segment(c,f,h),!w(y))y=y!==void 0?r.decode(y):void 0;else for(O=0,C=y.length;O<C;O++)y[O]=r.decode(y[O]);return y}if(!w(f))f=typeof f=="string"||f instanceof String?r.encode(f):f;else for(O=0,C=f.length;O<C;O++)f[O]=r.encode(f[O]);return this.segment(c,f,h)};var Z=u.query;return u.query=function(c,f){if(c===!0)return r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof c=="function"){var h=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace),y=c.call(this,h);return this._parts.query=r.buildQuery(y||h,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!f),this}else return c!==void 0&&typeof c!="string"?(this._parts.query=r.buildQuery(c,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),this.build(!f),this):Z.call(this,c,f)},u.setQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);if(typeof c=="string"||c instanceof String)y[c]=f!==void 0?f:null;else if(typeof c=="object")for(var O in c)d.call(c,O)&&(y[O]=c[O]);else throw new TypeError("URI.addQuery() accepts an object, string as the name parameter");return this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.addQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.addQuery(y,c,f===void 0?null:f),this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.removeQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.removeQuery(y,c,f),this._parts.query=r.buildQuery(y,this._parts.duplicateQueryParameters,this._parts.escapeQuerySpace),typeof c!="string"&&(h=f),this.build(!h),this},u.hasQuery=function(c,f,h){var y=r.parseQuery(this._parts.query,this._parts.escapeQuerySpace);return r.hasQuery(y,c,f,h)},u.setSearch=u.setQuery,u.addSearch=u.addQuery,u.removeSearch=u.removeQuery,u.hasSearch=u.hasQuery,u.normalize=function(){return this._parts.urn?this.normalizeProtocol(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build():this.normalizeProtocol(!1).normalizeHostname(!1).normalizePort(!1).normalizePath(!1).normalizeQuery(!1).normalizeFragment(!1).build()},u.normalizeProtocol=function(c){return typeof this._parts.protocol=="string"&&(this._parts.protocol=this._parts.protocol.toLowerCase(),this.build(!c)),this},u.normalizeHostname=function(c){return this._parts.hostname&&(this.is("IDN")&&e?this._parts.hostname=e.toASCII(this._parts.hostname):this.is("IPv6")&&n&&(this._parts.hostname=n.best(this._parts.hostname)),this._parts.hostname=this._parts.hostname.toLowerCase(),this.build(!c)),this},u.normalizePort=function(c){return typeof this._parts.protocol=="string"&&this._parts.port===r.defaultPorts[this._parts.protocol]&&(this._parts.port=null,this.build(!c)),this},u.normalizePath=function(c){var f=this._parts.path;if(!f)return this;if(this._parts.urn)return this._parts.path=r.recodeUrnPath(this._parts.path),this.build(!c),this;if(this._parts.path==="/")return this;f=r.recodePath(f);var h,y="",O,C;for(f.charAt(0)!=="/"&&(h=!0,f="/"+f),(f.slice(-3)==="/.."||f.slice(-2)==="/.")&&(f+="/"),f=f.replace(/(\/(\.\/)+)|(\/\.$)/g,"/").replace(/\/{2,}/g,"/"),h&&(y=f.substring(1).match(/^(\.\.\/)+/)||"",y&&(y=y[0]));O=f.search(/\/\.\.(\/|$)/),O!==-1;){if(O===0){f=f.substring(3);continue}C=f.substring(0,O).lastIndexOf("/"),C===-1&&(C=O),f=f.substring(0,C)+f.substring(O+3)}return h&&this.is("relative")&&(f=y+f.substring(1)),this._parts.path=f,this.build(!c),this},u.normalizePathname=u.normalizePath,u.normalizeQuery=function(c){return typeof this._parts.query=="string"&&(this._parts.query.length?this.query(r.parseQuery(this._parts.query,this._parts.escapeQuerySpace)):this._parts.query=null,this.build(!c)),this},u.normalizeFragment=function(c){return this._parts.fragment||(this._parts.fragment=null,this.build(!c)),this},u.normalizeSearch=u.normalizeQuery,u.normalizeHash=u.normalizeFragment,u.iso8859=function(){var c=r.encode,f=r.decode;r.encode=escape,r.decode=decodeURIComponent;try{this.normalize()}finally{r.encode=c,r.decode=f}return this},u.unicode=function(){var c=r.encode,f=r.decode;r.encode=N,r.decode=unescape;try{this.normalize()}finally{r.encode=c,r.decode=f}return this},u.readable=function(){var c=this.clone();c.username("").password("").normalize();var f="";if(c._parts.protocol&&(f+=c._parts.protocol+"://"),c._parts.hostname&&(c.is("punycode")&&e?(f+=e.toUnicode(c._parts.hostname),c._parts.port&&(f+=":"+c._parts.port)):f+=c.host()),c._parts.hostname&&c._parts.path&&c._parts.path.charAt(0)!=="/"&&(f+="/"),f+=c.path(!0),c._parts.query){for(var h="",y=0,O=c._parts.query.split("&"),C=O.length;y<C;y++){var b=(O[y]||"").split("=");h+="&"+r.decodeQuery(b[0],this._parts.escapeQuerySpace).replace(/&/g,"%26"),b[1]!==void 0&&(h+="="+r.decodeQuery(b[1],this._parts.escapeQuerySpace).replace(/&/g,"%26"))}f+="?"+h.substring(1)}return f+=r.decodeQuery(c.hash(),!0),f},u.absoluteTo=function(c){var f=this.clone(),h=["protocol","username","password","hostname","port"],y,O,C;if(this._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(c instanceof r||(c=new r(c)),f._parts.protocol||(f._parts.protocol=c._parts.protocol,this._parts.hostname))return f;for(O=0;C=h[O];O++)f._parts[C]=c._parts[C];return f._parts.path?(f._parts.path.substring(-2)===".."&&(f._parts.path+="/"),f.path().charAt(0)!=="/"&&(y=c.directory(),y=y||(c.path().indexOf("/")===0?"/":""),f._parts.path=(y?y+"/":"")+f._parts.path,f.normalizePath())):(f._parts.path=c._parts.path,f._parts.query||(f._parts.query=c._parts.query)),f.build(),f},u.relativeTo=function(c){var f=this.clone().normalize(),h,y,O,C,b;if(f._parts.urn)throw new Error("URNs do not have any generally defined hierarchical components");if(c=new r(c).normalize(),h=f._parts,y=c._parts,C=f.path(),b=c.path(),C.charAt(0)!=="/")throw new Error("URI is already relative");if(b.charAt(0)!=="/")throw new Error("Cannot calculate a URI relative to another relative URI");if(h.protocol===y.protocol&&(h.protocol=null),h.username!==y.username||h.password!==y.password||h.protocol!==null||h.username!==null||h.password!==null)return f.build();if(h.hostname===y.hostname&&h.port===y.port)h.hostname=null,h.port=null;else return f.build();if(C===b)return h.path="",f.build();if(O=r.commonPath(C,b),!O)return f.build();var z=y.path.substring(O.length).replace(/[^\/]*$/,"").replace(/.*?\//g,"../");return h.path=z+h.path.substring(O.length)||"./",f.build()},u.equals=function(c){var f=this.clone(),h=new r(c),y={},O={},C={},b,z,D;if(f.normalize(),h.normalize(),f.toString()===h.toString())return!0;if(b=f.query(),z=h.query(),f.query(""),h.query(""),f.toString()!==h.toString()||b.length!==z.length)return!1;y=r.parseQuery(b,this._parts.escapeQuerySpace),O=r.parseQuery(z,this._parts.escapeQuerySpace);for(D in y)if(d.call(y,D)){if(w(y[D])){if(!M(y[D],O[D]))return!1}else if(y[D]!==O[D])return!1;C[D]=!0}for(D in O)if(d.call(O,D)&&!C[D])return!1;return!0},u.preventInvalidHostname=function(c){return this._parts.preventInvalidHostname=!!c,this},u.duplicateQueryParameters=function(c){return this._parts.duplicateQueryParameters=!!c,this},u.escapeQuerySpace=function(c){return this._parts.escapeQuerySpace=!!c,this},r})});function j(e,n,t,o){this.x=e??0,this.y=n??0,this.z=t??0,this.w=o??0}j.fromElements=function(e,n,t,o,i){return p(i)?(i.x=e,i.y=n,i.z=t,i.w=o,i):new j(e,n,t,o)};j.fromColor=function(e,n){return s.typeOf.object("color",e),p(n)?(n.x=e.red,n.y=e.green,n.z=e.blue,n.w=e.alpha,n):new j(e.red,e.green,e.blue,e.alpha)};j.clone=function(e,n){if(p(e))return p(n)?(n.x=e.x,n.y=e.y,n.z=e.z,n.w=e.w,n):new j(e.x,e.y,e.z,e.w)};j.packedLength=4;j.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=t??0,n[t++]=e.x,n[t++]=e.y,n[t++]=e.z,n[t]=e.w,n};j.unpack=function(e,n,t){return s.defined("array",e),n=n??0,p(t)||(t=new j),t.x=e[n++],t.y=e[n++],t.z=e[n++],t.w=e[n],t};j.packArray=function(e,n){s.defined("array",e);let t=e.length,o=t*4;if(!p(n))n=new Array(o);else{if(!Array.isArray(n)&&n.length!==o)throw new A("If result is a typed array, it must have exactly array.length * 4 elements");n.length!==o&&(n.length=o)}for(let i=0;i<t;++i)j.pack(e[i],n,i*4);return n};j.unpackArray=function(e,n){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,4),e.length%4!==0)throw new A("array length must be a multiple of 4.");let t=e.length;p(n)?n.length=t/4:n=new Array(t/4);for(let o=0;o<t;o+=4){let i=o/4;n[i]=j.unpack(e,o,n[i])}return n};j.fromArray=j.unpack;j.maximumComponent=function(e){return s.typeOf.object("cartesian",e),Math.max(e.x,e.y,e.z,e.w)};j.minimumComponent=function(e){return s.typeOf.object("cartesian",e),Math.min(e.x,e.y,e.z,e.w)};j.minimumByComponent=function(e,n,t){return s.typeOf.object("first",e),s.typeOf.object("second",n),s.typeOf.object("result",t),t.x=Math.min(e.x,n.x),t.y=Math.min(e.y,n.y),t.z=Math.min(e.z,n.z),t.w=Math.min(e.w,n.w),t};j.maximumByComponent=function(e,n,t){return s.typeOf.object("first",e),s.typeOf.object("second",n),s.typeOf.object("result",t),t.x=Math.max(e.x,n.x),t.y=Math.max(e.y,n.y),t.z=Math.max(e.z,n.z),t.w=Math.max(e.w,n.w),t};j.clamp=function(e,n,t,o){s.typeOf.object("value",e),s.typeOf.object("min",n),s.typeOf.object("max",t),s.typeOf.object("result",o);let i=R.clamp(e.x,n.x,t.x),r=R.clamp(e.y,n.y,t.y),a=R.clamp(e.z,n.z,t.z),u=R.clamp(e.w,n.w,t.w);return o.x=i,o.y=r,o.z=a,o.w=u,o};j.magnitudeSquared=function(e){return s.typeOf.object("cartesian",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w};j.magnitude=function(e){return Math.sqrt(j.magnitudeSquared(e))};var En=new j;j.distance=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),j.subtract(e,n,En),j.magnitude(En)};j.distanceSquared=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),j.subtract(e,n,En),j.magnitudeSquared(En)};j.normalize=function(e,n){s.typeOf.object("cartesian",e),s.typeOf.object("result",n);let t=j.magnitude(e);if(n.x=e.x/t,n.y=e.y/t,n.z=e.z/t,n.w=e.w/t,isNaN(n.x)||isNaN(n.y)||isNaN(n.z)||isNaN(n.w))throw new A("normalized result is not a number");return n};j.dot=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),e.x*n.x+e.y*n.y+e.z*n.z+e.w*n.w};j.multiplyComponents=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x*n.x,t.y=e.y*n.y,t.z=e.z*n.z,t.w=e.w*n.w,t};j.divideComponents=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x/n.x,t.y=e.y/n.y,t.z=e.z/n.z,t.w=e.w/n.w,t};j.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x+n.x,t.y=e.y+n.y,t.z=e.z+n.z,t.w=e.w+n.w,t};j.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x-n.x,t.y=e.y-n.y,t.z=e.z-n.z,t.w=e.w-n.w,t};j.multiplyByScalar=function(e,n,t){return s.typeOf.object("cartesian",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x*n,t.y=e.y*n,t.z=e.z*n,t.w=e.w*n,t};j.divideByScalar=function(e,n,t){return s.typeOf.object("cartesian",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x/n,t.y=e.y/n,t.z=e.z/n,t.w=e.w/n,t};j.negate=function(e,n){return s.typeOf.object("cartesian",e),s.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n.z=-e.z,n.w=-e.w,n};j.abs=function(e,n){return s.typeOf.object("cartesian",e),s.typeOf.object("result",n),n.x=Math.abs(e.x),n.y=Math.abs(e.y),n.z=Math.abs(e.z),n.w=Math.abs(e.w),n};var Et=new j;j.lerp=function(e,n,t,o){return s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o),j.multiplyByScalar(n,t,Et),o=j.multiplyByScalar(e,1-t,o),j.add(Et,o,o)};var zo=new j;j.mostOrthogonalAxis=function(e,n){s.typeOf.object("cartesian",e),s.typeOf.object("result",n);let t=j.normalize(e,zo);return j.abs(t,t),t.x<=t.y?t.x<=t.z?t.x<=t.w?n=j.clone(j.UNIT_X,n):n=j.clone(j.UNIT_W,n):t.z<=t.w?n=j.clone(j.UNIT_Z,n):n=j.clone(j.UNIT_W,n):t.y<=t.z?t.y<=t.w?n=j.clone(j.UNIT_Y,n):n=j.clone(j.UNIT_W,n):t.z<=t.w?n=j.clone(j.UNIT_Z,n):n=j.clone(j.UNIT_W,n),n};j.equals=function(e,n){return e===n||p(e)&&p(n)&&e.x===n.x&&e.y===n.y&&e.z===n.z&&e.w===n.w};j.equalsArray=function(e,n,t){return e.x===n[t]&&e.y===n[t+1]&&e.z===n[t+2]&&e.w===n[t+3]};j.equalsEpsilon=function(e,n,t,o){return e===n||p(e)&&p(n)&&R.equalsEpsilon(e.x,n.x,t,o)&&R.equalsEpsilon(e.y,n.y,t,o)&&R.equalsEpsilon(e.z,n.z,t,o)&&R.equalsEpsilon(e.w,n.w,t,o)};j.ZERO=Object.freeze(new j(0,0,0,0));j.ONE=Object.freeze(new j(1,1,1,1));j.UNIT_X=Object.freeze(new j(1,0,0,0));j.UNIT_Y=Object.freeze(new j(0,1,0,0));j.UNIT_Z=Object.freeze(new j(0,0,1,0));j.UNIT_W=Object.freeze(new j(0,0,0,1));j.prototype.clone=function(e){return j.clone(this,e)};j.prototype.equals=function(e){return j.equals(this,e)};j.prototype.equalsEpsilon=function(e,n,t){return j.equalsEpsilon(this,e,n,t)};j.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var Jn=new Float32Array(1),ye=new Uint8Array(Jn.buffer),Io=new Uint32Array([287454020]),qo=new Uint8Array(Io.buffer),Tt=qo[0]===68;j.packFloat=function(e,n){return s.typeOf.number("value",e),p(n)||(n=new j),Jn[0]=e,Tt?(n.x=ye[0],n.y=ye[1],n.z=ye[2],n.w=ye[3]):(n.x=ye[3],n.y=ye[2],n.z=ye[1],n.w=ye[0]),n};j.unpackFloat=function(e){return s.typeOf.object("packedFloat",e),Tt?(ye[0]=e.x,ye[1]=e.y,ye[2]=e.z,ye[3]=e.w):(ye[0]=e.w,ye[1]=e.z,ye[2]=e.y,ye[3]=e.x),Jn[0]};var qe=j;function _(e,n,t,o,i,r,a,u,d,m,l,w,E,T,M,v){this[0]=e??0,this[1]=i??0,this[2]=d??0,this[3]=E??0,this[4]=n??0,this[5]=r??0,this[6]=m??0,this[7]=T??0,this[8]=t??0,this[9]=a??0,this[10]=l??0,this[11]=M??0,this[12]=o??0,this[13]=u??0,this[14]=w??0,this[15]=v??0}_.packedLength=16;_.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=t??0,n[t++]=e[0],n[t++]=e[1],n[t++]=e[2],n[t++]=e[3],n[t++]=e[4],n[t++]=e[5],n[t++]=e[6],n[t++]=e[7],n[t++]=e[8],n[t++]=e[9],n[t++]=e[10],n[t++]=e[11],n[t++]=e[12],n[t++]=e[13],n[t++]=e[14],n[t]=e[15],n};_.unpack=function(e,n,t){return s.defined("array",e),n=n??0,p(t)||(t=new _),t[0]=e[n++],t[1]=e[n++],t[2]=e[n++],t[3]=e[n++],t[4]=e[n++],t[5]=e[n++],t[6]=e[n++],t[7]=e[n++],t[8]=e[n++],t[9]=e[n++],t[10]=e[n++],t[11]=e[n++],t[12]=e[n++],t[13]=e[n++],t[14]=e[n++],t[15]=e[n],t};_.packArray=function(e,n){s.defined("array",e);let t=e.length,o=t*16;if(!p(n))n=new Array(o);else{if(!Array.isArray(n)&&n.length!==o)throw new A("If result is a typed array, it must have exactly array.length * 16 elements");n.length!==o&&(n.length=o)}for(let i=0;i<t;++i)_.pack(e[i],n,i*16);return n};_.unpackArray=function(e,n){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,16),e.length%16!==0)throw new A("array length must be a multiple of 16.");let t=e.length;p(n)?n.length=t/16:n=new Array(t/16);for(let o=0;o<t;o+=16){let i=o/16;n[i]=_.unpack(e,o,n[i])}return n};_.clone=function(e,n){if(p(e))return p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n):new _(e[0],e[4],e[8],e[12],e[1],e[5],e[9],e[13],e[2],e[6],e[10],e[14],e[3],e[7],e[11],e[15])};_.fromArray=_.unpack;_.fromColumnMajorArray=function(e,n){return s.defined("values",e),_.clone(e,n)};_.fromRowMajorArray=function(e,n){return s.defined("values",e),p(n)?(n[0]=e[0],n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=e[1],n[5]=e[5],n[6]=e[9],n[7]=e[13],n[8]=e[2],n[9]=e[6],n[10]=e[10],n[11]=e[14],n[12]=e[3],n[13]=e[7],n[14]=e[11],n[15]=e[15],n):new _(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])};_.fromRotationTranslation=function(e,n,t){return s.typeOf.object("rotation",e),n=n??g.ZERO,p(t)?(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=0,t[4]=e[3],t[5]=e[4],t[6]=e[5],t[7]=0,t[8]=e[6],t[9]=e[7],t[10]=e[8],t[11]=0,t[12]=n.x,t[13]=n.y,t[14]=n.z,t[15]=1,t):new _(e[0],e[3],e[6],n.x,e[1],e[4],e[7],n.y,e[2],e[5],e[8],n.z,0,0,0,1)};_.fromTranslationQuaternionRotationScale=function(e,n,t,o){s.typeOf.object("translation",e),s.typeOf.object("rotation",n),s.typeOf.object("scale",t),p(o)||(o=new _);let i=t.x,r=t.y,a=t.z,u=n.x*n.x,d=n.x*n.y,m=n.x*n.z,l=n.x*n.w,w=n.y*n.y,E=n.y*n.z,T=n.y*n.w,M=n.z*n.z,v=n.z*n.w,I=n.w*n.w,N=u-w-M+I,F=2*(d-v),k=2*(m+T),x=2*(d+v),Q=-u+w-M+I,W=2*(E-l),K=2*(m-T),oe=2*(E+l),X=-u-w+M+I;return o[0]=N*i,o[1]=x*i,o[2]=K*i,o[3]=0,o[4]=F*r,o[5]=Q*r,o[6]=oe*r,o[7]=0,o[8]=k*a,o[9]=W*a,o[10]=X*a,o[11]=0,o[12]=e.x,o[13]=e.y,o[14]=e.z,o[15]=1,o};_.fromTranslationRotationScale=function(e,n){return s.typeOf.object("translationRotationScale",e),_.fromTranslationQuaternionRotationScale(e.translation,e.rotation,e.scale,n)};_.fromTranslation=function(e,n){return s.typeOf.object("translation",e),_.fromRotationTranslation(B.IDENTITY,e,n)};_.fromScale=function(e,n){return s.typeOf.object("scale",e),p(n)?(n[0]=e.x,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=e.y,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=e.z,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n):new _(e.x,0,0,0,0,e.y,0,0,0,0,e.z,0,0,0,0,1)};_.fromUniformScale=function(e,n){return s.typeOf.number("scale",e),p(n)?(n[0]=e,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=e,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=e,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n):new _(e,0,0,0,0,e,0,0,0,0,e,0,0,0,0,1)};_.fromRotation=function(e,n){return s.typeOf.object("rotation",e),p(n)||(n=new _),n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=0,n[4]=e[3],n[5]=e[4],n[6]=e[5],n[7]=0,n[8]=e[6],n[9]=e[7],n[10]=e[8],n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n};var Je=new g,Ge=new g,pn=new g;_.fromCamera=function(e,n){s.typeOf.object("camera",e);let t=e.position,o=e.direction,i=e.up;s.typeOf.object("camera.position",t),s.typeOf.object("camera.direction",o),s.typeOf.object("camera.up",i),g.normalize(o,Je),g.normalize(g.cross(Je,i,Ge),Ge),g.normalize(g.cross(Ge,Je,pn),pn);let r=Ge.x,a=Ge.y,u=Ge.z,d=Je.x,m=Je.y,l=Je.z,w=pn.x,E=pn.y,T=pn.z,M=t.x,v=t.y,I=t.z,N=r*-M+a*-v+u*-I,F=w*-M+E*-v+T*-I,k=d*M+m*v+l*I;return p(n)?(n[0]=r,n[1]=w,n[2]=-d,n[3]=0,n[4]=a,n[5]=E,n[6]=-m,n[7]=0,n[8]=u,n[9]=T,n[10]=-l,n[11]=0,n[12]=N,n[13]=F,n[14]=k,n[15]=1,n):new _(r,a,u,N,w,E,T,F,-d,-m,-l,k,0,0,0,1)};_.computePerspectiveFieldOfView=function(e,n,t,o,i){s.typeOf.number.greaterThan("fovY",e,0),s.typeOf.number.lessThan("fovY",e,Math.PI),s.typeOf.number.greaterThan("near",t,0),s.typeOf.number.greaterThan("far",o,0),s.typeOf.object("result",i);let a=1/Math.tan(e*.5),u=a/n,d=(o+t)/(t-o),m=2*o*t/(t-o);return i[0]=u,i[1]=0,i[2]=0,i[3]=0,i[4]=0,i[5]=a,i[6]=0,i[7]=0,i[8]=0,i[9]=0,i[10]=d,i[11]=-1,i[12]=0,i[13]=0,i[14]=m,i[15]=0,i};_.computeOrthographicOffCenter=function(e,n,t,o,i,r,a){s.typeOf.number("left",e),s.typeOf.number("right",n),s.typeOf.number("bottom",t),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.number("far",r),s.typeOf.object("result",a);let u=1/(n-e),d=1/(o-t),m=1/(r-i),l=-(n+e)*u,w=-(o+t)*d,E=-(r+i)*m;return u*=2,d*=2,m*=-2,a[0]=u,a[1]=0,a[2]=0,a[3]=0,a[4]=0,a[5]=d,a[6]=0,a[7]=0,a[8]=0,a[9]=0,a[10]=m,a[11]=0,a[12]=l,a[13]=w,a[14]=E,a[15]=1,a};_.computePerspectiveOffCenter=function(e,n,t,o,i,r,a){s.typeOf.number("left",e),s.typeOf.number("right",n),s.typeOf.number("bottom",t),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.number("far",r),s.typeOf.object("result",a);let u=2*i/(n-e),d=2*i/(o-t),m=(n+e)/(n-e),l=(o+t)/(o-t),w=-(r+i)/(r-i),E=-1,T=-2*r*i/(r-i);return a[0]=u,a[1]=0,a[2]=0,a[3]=0,a[4]=0,a[5]=d,a[6]=0,a[7]=0,a[8]=m,a[9]=l,a[10]=w,a[11]=E,a[12]=0,a[13]=0,a[14]=T,a[15]=0,a};_.computeInfinitePerspectiveOffCenter=function(e,n,t,o,i,r){s.typeOf.number("left",e),s.typeOf.number("right",n),s.typeOf.number("bottom",t),s.typeOf.number("top",o),s.typeOf.number("near",i),s.typeOf.object("result",r);let a=2*i/(n-e),u=2*i/(o-t),d=(n+e)/(n-e),m=(o+t)/(o-t),l=-1,w=-1,E=-2*i;return r[0]=a,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=u,r[6]=0,r[7]=0,r[8]=d,r[9]=m,r[10]=l,r[11]=w,r[12]=0,r[13]=0,r[14]=E,r[15]=0,r};_.computeViewportTransformation=function(e,n,t,o){p(o)||(o=new _),e=e??_e.EMPTY_OBJECT;let i=e.x??0,r=e.y??0,a=e.width??0,u=e.height??0;n=n??0,t=t??1;let d=a*.5,m=u*.5,l=(t-n)*.5,w=d,E=m,T=l,M=i+d,v=r+m,I=n+l,N=1;return o[0]=w,o[1]=0,o[2]=0,o[3]=0,o[4]=0,o[5]=E,o[6]=0,o[7]=0,o[8]=0,o[9]=0,o[10]=T,o[11]=0,o[12]=M,o[13]=v,o[14]=I,o[15]=N,o};_.computeView=function(e,n,t,o,i){return s.typeOf.object("position",e),s.typeOf.object("direction",n),s.typeOf.object("up",t),s.typeOf.object("right",o),s.typeOf.object("result",i),i[0]=o.x,i[1]=t.x,i[2]=-n.x,i[3]=0,i[4]=o.y,i[5]=t.y,i[6]=-n.y,i[7]=0,i[8]=o.z,i[9]=t.z,i[10]=-n.z,i[11]=0,i[12]=-g.dot(o,e),i[13]=-g.dot(t,e),i[14]=g.dot(n,e),i[15]=1,i};_.toArray=function(e,n){return s.typeOf.object("matrix",e),p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n):[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15]]};_.getElementIndex=function(e,n){return s.typeOf.number.greaterThanOrEquals("row",n,0),s.typeOf.number.lessThanOrEquals("row",n,3),s.typeOf.number.greaterThanOrEquals("column",e,0),s.typeOf.number.lessThanOrEquals("column",e,3),e*4+n};_.getColumn=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("result",t);let o=n*4,i=e[o],r=e[o+1],a=e[o+2],u=e[o+3];return t.x=i,t.y=r,t.z=a,t.w=u,t};_.setColumn=function(e,n,t,o){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=_.clone(e,o);let i=n*4;return o[i]=t.x,o[i+1]=t.y,o[i+2]=t.z,o[i+3]=t.w,o};_.getRow=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("result",t);let o=e[n],i=e[n+4],r=e[n+8],a=e[n+12];return t.x=o,t.y=i,t.z=r,t.w=a,t};_.setRow=function(e,n,t,o){return s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,3),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=_.clone(e,o),o[n]=t.x,o[n+4]=t.y,o[n+8]=t.z,o[n+12]=t.w,o};_.setTranslation=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.object("translation",n),s.typeOf.object("result",t),t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=n.x,t[13]=n.y,t[14]=n.z,t[15]=e[15],t};var Do=new g;_.setScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t);let o=_.getScale(e,Do),i=n.x/o.x,r=n.y/o.y,a=n.z/o.z;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*i,t[3]=e[3],t[4]=e[4]*r,t[5]=e[5]*r,t[6]=e[6]*r,t[7]=e[7],t[8]=e[8]*a,t[9]=e[9]*a,t[10]=e[10]*a,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};var No=new g;_.setUniformScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t);let o=_.getScale(e,No),i=n/o.x,r=n/o.y,a=n/o.z;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*i,t[3]=e[3],t[4]=e[4]*r,t[5]=e[5]*r,t[6]=e[6]*r,t[7]=e[7],t[8]=e[8]*a,t[9]=e[9]*a,t[10]=e[10]*a,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};var Gn=new g;_.getScale=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n.x=g.magnitude(g.fromElements(e[0],e[1],e[2],Gn)),n.y=g.magnitude(g.fromElements(e[4],e[5],e[6],Gn)),n.z=g.magnitude(g.fromElements(e[8],e[9],e[10],Gn)),n};var Ct=new g;_.getMaximumScale=function(e){return _.getScale(e,Ct),g.maximumComponent(Ct)};var ko=new g;_.setRotation=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let o=_.getScale(e,ko);return t[0]=n[0]*o.x,t[1]=n[1]*o.x,t[2]=n[2]*o.x,t[3]=e[3],t[4]=n[3]*o.y,t[5]=n[4]*o.y,t[6]=n[5]*o.y,t[7]=e[7],t[8]=n[6]*o.z,t[9]=n[7]*o.z,t[10]=n[8]*o.z,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};var Fo=new g;_.getRotation=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=_.getScale(e,Fo);return n[0]=e[0]/t.x,n[1]=e[1]/t.x,n[2]=e[2]/t.x,n[3]=e[4]/t.y,n[4]=e[5]/t.y,n[5]=e[6]/t.y,n[6]=e[8]/t.z,n[7]=e[9]/t.z,n[8]=e[10]/t.z,n};_.multiply=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e[0],i=e[1],r=e[2],a=e[3],u=e[4],d=e[5],m=e[6],l=e[7],w=e[8],E=e[9],T=e[10],M=e[11],v=e[12],I=e[13],N=e[14],F=e[15],k=n[0],x=n[1],Q=n[2],W=n[3],K=n[4],oe=n[5],X=n[6],te=n[7],Z=n[8],c=n[9],f=n[10],h=n[11],y=n[12],O=n[13],C=n[14],b=n[15],z=o*k+u*x+w*Q+v*W,D=i*k+d*x+E*Q+I*W,Y=r*k+m*x+T*Q+N*W,re=a*k+l*x+M*Q+F*W,se=o*K+u*oe+w*X+v*te,ee=i*K+d*oe+E*X+I*te,fe=r*K+m*oe+T*X+N*te,he=a*K+l*oe+M*X+F*te,me=o*Z+u*c+w*f+v*h,de=i*Z+d*c+E*f+I*h,pe=r*Z+m*c+T*f+N*h,V=a*Z+l*c+M*f+F*h,ge=o*y+u*O+w*C+v*b,Te=i*y+d*O+E*C+I*b,je=r*y+m*O+T*C+N*b,fn=a*y+l*O+M*C+F*b;return t[0]=z,t[1]=D,t[2]=Y,t[3]=re,t[4]=se,t[5]=ee,t[6]=fe,t[7]=he,t[8]=me,t[9]=de,t[10]=pe,t[11]=V,t[12]=ge,t[13]=Te,t[14]=je,t[15]=fn,t};_.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t[4]=e[4]+n[4],t[5]=e[5]+n[5],t[6]=e[6]+n[6],t[7]=e[7]+n[7],t[8]=e[8]+n[8],t[9]=e[9]+n[9],t[10]=e[10]+n[10],t[11]=e[11]+n[11],t[12]=e[12]+n[12],t[13]=e[13]+n[13],t[14]=e[14]+n[14],t[15]=e[15]+n[15],t};_.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t[4]=e[4]-n[4],t[5]=e[5]-n[5],t[6]=e[6]-n[6],t[7]=e[7]-n[7],t[8]=e[8]-n[8],t[9]=e[9]-n[9],t[10]=e[10]-n[10],t[11]=e[11]-n[11],t[12]=e[12]-n[12],t[13]=e[13]-n[13],t[14]=e[14]-n[14],t[15]=e[15]-n[15],t};_.multiplyTransformation=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e[0],i=e[1],r=e[2],a=e[4],u=e[5],d=e[6],m=e[8],l=e[9],w=e[10],E=e[12],T=e[13],M=e[14],v=n[0],I=n[1],N=n[2],F=n[4],k=n[5],x=n[6],Q=n[8],W=n[9],K=n[10],oe=n[12],X=n[13],te=n[14],Z=o*v+a*I+m*N,c=i*v+u*I+l*N,f=r*v+d*I+w*N,h=o*F+a*k+m*x,y=i*F+u*k+l*x,O=r*F+d*k+w*x,C=o*Q+a*W+m*K,b=i*Q+u*W+l*K,z=r*Q+d*W+w*K,D=o*oe+a*X+m*te+E,Y=i*oe+u*X+l*te+T,re=r*oe+d*X+w*te+M;return t[0]=Z,t[1]=c,t[2]=f,t[3]=0,t[4]=h,t[5]=y,t[6]=O,t[7]=0,t[8]=C,t[9]=b,t[10]=z,t[11]=0,t[12]=D,t[13]=Y,t[14]=re,t[15]=1,t};_.multiplyByMatrix3=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("rotation",n),s.typeOf.object("result",t);let o=e[0],i=e[1],r=e[2],a=e[4],u=e[5],d=e[6],m=e[8],l=e[9],w=e[10],E=n[0],T=n[1],M=n[2],v=n[3],I=n[4],N=n[5],F=n[6],k=n[7],x=n[8],Q=o*E+a*T+m*M,W=i*E+u*T+l*M,K=r*E+d*T+w*M,oe=o*v+a*I+m*N,X=i*v+u*I+l*N,te=r*v+d*I+w*N,Z=o*F+a*k+m*x,c=i*F+u*k+l*x,f=r*F+d*k+w*x;return t[0]=Q,t[1]=W,t[2]=K,t[3]=0,t[4]=oe,t[5]=X,t[6]=te,t[7]=0,t[8]=Z,t[9]=c,t[10]=f,t[11]=0,t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};_.multiplyByTranslation=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("translation",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=o*e[0]+i*e[4]+r*e[8]+e[12],u=o*e[1]+i*e[5]+r*e[9]+e[13],d=o*e[2]+i*e[6]+r*e[10]+e[14];return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t[6]=e[6],t[7]=e[7],t[8]=e[8],t[9]=e[9],t[10]=e[10],t[11]=e[11],t[12]=a,t[13]=u,t[14]=d,t[15]=e[15],t};_.multiplyByScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z;return o===1&&i===1&&r===1?_.clone(e,t):(t[0]=o*e[0],t[1]=o*e[1],t[2]=o*e[2],t[3]=e[3],t[4]=i*e[4],t[5]=i*e[5],t[6]=i*e[6],t[7]=e[7],t[8]=r*e[8],t[9]=r*e[9],t[10]=r*e[10],t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t)};_.multiplyByUniformScale=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3],t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7],t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11],t[12]=e[12],t[13]=e[13],t[14]=e[14],t[15]=e[15],t};_.multiplyByVector=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=n.w,u=e[0]*o+e[4]*i+e[8]*r+e[12]*a,d=e[1]*o+e[5]*i+e[9]*r+e[13]*a,m=e[2]*o+e[6]*i+e[10]*r+e[14]*a,l=e[3]*o+e[7]*i+e[11]*r+e[15]*a;return t.x=u,t.y=d,t.z=m,t.w=l,t};_.multiplyByPointAsVector=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=e[0]*o+e[4]*i+e[8]*r,u=e[1]*o+e[5]*i+e[9]*r,d=e[2]*o+e[6]*i+e[10]*r;return t.x=a,t.y=u,t.z=d,t};_.multiplyByPoint=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=n.x,i=n.y,r=n.z,a=e[0]*o+e[4]*i+e[8]*r+e[12],u=e[1]*o+e[5]*i+e[9]*r+e[13],d=e[2]*o+e[6]*i+e[10]*r+e[14];return t.x=a,t.y=u,t.z=d,t};_.multiplyByScalar=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t[4]=e[4]*n,t[5]=e[5]*n,t[6]=e[6]*n,t[7]=e[7]*n,t[8]=e[8]*n,t[9]=e[9]*n,t[10]=e[10]*n,t[11]=e[11]*n,t[12]=e[12]*n,t[13]=e[13]*n,t[14]=e[14]*n,t[15]=e[15]*n,t};_.negate=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n[4]=-e[4],n[5]=-e[5],n[6]=-e[6],n[7]=-e[7],n[8]=-e[8],n[9]=-e[9],n[10]=-e[10],n[11]=-e[11],n[12]=-e[12],n[13]=-e[13],n[14]=-e[14],n[15]=-e[15],n};_.transpose=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[1],o=e[2],i=e[3],r=e[6],a=e[7],u=e[11];return n[0]=e[0],n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=t,n[5]=e[5],n[6]=e[9],n[7]=e[13],n[8]=o,n[9]=r,n[10]=e[10],n[11]=e[14],n[12]=i,n[13]=a,n[14]=u,n[15]=e[15],n};_.abs=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=Math.abs(e[0]),n[1]=Math.abs(e[1]),n[2]=Math.abs(e[2]),n[3]=Math.abs(e[3]),n[4]=Math.abs(e[4]),n[5]=Math.abs(e[5]),n[6]=Math.abs(e[6]),n[7]=Math.abs(e[7]),n[8]=Math.abs(e[8]),n[9]=Math.abs(e[9]),n[10]=Math.abs(e[10]),n[11]=Math.abs(e[11]),n[12]=Math.abs(e[12]),n[13]=Math.abs(e[13]),n[14]=Math.abs(e[14]),n[15]=Math.abs(e[15]),n};_.equals=function(e,n){return e===n||p(e)&&p(n)&&e[12]===n[12]&&e[13]===n[13]&&e[14]===n[14]&&e[0]===n[0]&&e[1]===n[1]&&e[2]===n[2]&&e[4]===n[4]&&e[5]===n[5]&&e[6]===n[6]&&e[8]===n[8]&&e[9]===n[9]&&e[10]===n[10]&&e[3]===n[3]&&e[7]===n[7]&&e[11]===n[11]&&e[15]===n[15]};_.equalsEpsilon=function(e,n,t){return t=t??0,e===n||p(e)&&p(n)&&Math.abs(e[0]-n[0])<=t&&Math.abs(e[1]-n[1])<=t&&Math.abs(e[2]-n[2])<=t&&Math.abs(e[3]-n[3])<=t&&Math.abs(e[4]-n[4])<=t&&Math.abs(e[5]-n[5])<=t&&Math.abs(e[6]-n[6])<=t&&Math.abs(e[7]-n[7])<=t&&Math.abs(e[8]-n[8])<=t&&Math.abs(e[9]-n[9])<=t&&Math.abs(e[10]-n[10])<=t&&Math.abs(e[11]-n[11])<=t&&Math.abs(e[12]-n[12])<=t&&Math.abs(e[13]-n[13])<=t&&Math.abs(e[14]-n[14])<=t&&Math.abs(e[15]-n[15])<=t};_.getTranslation=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n.x=e[12],n.y=e[13],n.z=e[14],n};_.getMatrix3=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[4],n[4]=e[5],n[5]=e[6],n[6]=e[8],n[7]=e[9],n[8]=e[10],n};var Lo=new B,xo=new B,Bo=new qe,Qo=new qe(0,0,0,1);_.inverse=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[0],o=e[4],i=e[8],r=e[12],a=e[1],u=e[5],d=e[9],m=e[13],l=e[2],w=e[6],E=e[10],T=e[14],M=e[3],v=e[7],I=e[11],N=e[15],F=E*N,k=T*I,x=w*N,Q=T*v,W=w*I,K=E*v,oe=l*N,X=T*M,te=l*I,Z=E*M,c=l*v,f=w*M,h=F*u+Q*d+W*m-(k*u+x*d+K*m),y=k*a+oe*d+Z*m-(F*a+X*d+te*m),O=x*a+X*u+c*m-(Q*a+oe*u+f*m),C=K*a+te*u+f*d-(W*a+Z*u+c*d),b=k*o+x*i+K*r-(F*o+Q*i+W*r),z=F*t+X*i+te*r-(k*t+oe*i+Z*r),D=Q*t+oe*o+f*r-(x*t+X*o+c*r),Y=W*t+Z*o+c*i-(K*t+te*o+f*i);F=i*m,k=r*d,x=o*m,Q=r*u,W=o*d,K=i*u,oe=t*m,X=r*a,te=t*d,Z=i*a,c=t*u,f=o*a;let re=F*v+Q*I+W*N-(k*v+x*I+K*N),se=k*M+oe*I+Z*N-(F*M+X*I+te*N),ee=x*M+X*v+c*N-(Q*M+oe*v+f*N),fe=K*M+te*v+f*I-(W*M+Z*v+c*I),he=x*E+K*T+k*w-(W*T+F*w+Q*E),me=te*T+F*l+X*E-(oe*E+Z*T+k*l),de=oe*w+f*T+Q*l-(c*T+x*l+X*w),pe=c*E+W*l+Z*w-(te*w+f*E+K*l),V=t*h+o*y+i*O+r*C;if(Math.abs(V)<R.EPSILON21){if(B.equalsEpsilon(_.getMatrix3(e,Lo),xo,R.EPSILON7)&&qe.equals(_.getRow(e,3,Bo),Qo))return n[0]=0,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=0,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=0,n[11]=0,n[12]=-e[12],n[13]=-e[13],n[14]=-e[14],n[15]=1,n;throw new Se("matrix is not invertible because its determinate is zero.")}return V=1/V,n[0]=h*V,n[1]=y*V,n[2]=O*V,n[3]=C*V,n[4]=b*V,n[5]=z*V,n[6]=D*V,n[7]=Y*V,n[8]=re*V,n[9]=se*V,n[10]=ee*V,n[11]=fe*V,n[12]=he*V,n[13]=me*V,n[14]=de*V,n[15]=pe*V,n};_.inverseTransformation=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[0],o=e[1],i=e[2],r=e[4],a=e[5],u=e[6],d=e[8],m=e[9],l=e[10],w=e[12],E=e[13],T=e[14],M=-t*w-o*E-i*T,v=-r*w-a*E-u*T,I=-d*w-m*E-l*T;return n[0]=t,n[1]=r,n[2]=d,n[3]=0,n[4]=o,n[5]=a,n[6]=m,n[7]=0,n[8]=i,n[9]=u,n[10]=l,n[11]=0,n[12]=M,n[13]=v,n[14]=I,n[15]=1,n};var Wo=new _;_.inverseTranspose=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),_.inverse(_.transpose(e,Wo),n)};_.IDENTITY=Object.freeze(new _(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1));_.ZERO=Object.freeze(new _(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0));_.COLUMN0ROW0=0;_.COLUMN0ROW1=1;_.COLUMN0ROW2=2;_.COLUMN0ROW3=3;_.COLUMN1ROW0=4;_.COLUMN1ROW1=5;_.COLUMN1ROW2=6;_.COLUMN1ROW3=7;_.COLUMN2ROW0=8;_.COLUMN2ROW1=9;_.COLUMN2ROW2=10;_.COLUMN2ROW3=11;_.COLUMN3ROW0=12;_.COLUMN3ROW1=13;_.COLUMN3ROW2=14;_.COLUMN3ROW3=15;Object.defineProperties(_.prototype,{length:{get:function(){return _.packedLength}}});_.prototype.clone=function(e){return _.clone(this,e)};_.prototype.equals=function(e){return _.equals(this,e)};_.equalsArray=function(e,n,t){return e[0]===n[t]&&e[1]===n[t+1]&&e[2]===n[t+2]&&e[3]===n[t+3]&&e[4]===n[t+4]&&e[5]===n[t+5]&&e[6]===n[t+6]&&e[7]===n[t+7]&&e[8]===n[t+8]&&e[9]===n[t+9]&&e[10]===n[t+10]&&e[11]===n[t+11]&&e[12]===n[t+12]&&e[13]===n[t+13]&&e[14]===n[t+14]&&e[15]===n[t+15]};_.prototype.equalsEpsilon=function(e,n){return _.equalsEpsilon(this,e,n)};_.prototype.toString=function(){return`(${this[0]}, ${this[4]}, ${this[8]}, ${this[12]})
(${this[1]}, ${this[5]}, ${this[9]}, ${this[13]})
(${this[2]}, ${this[6]}, ${this[10]}, ${this[14]})
(${this[3]}, ${this[7]}, ${this[11]}, ${this[15]})`};var J=_;function vt(e,n,t){t=t??!1;let o={},i=p(e),r=p(n),a,u,d;if(i)for(a in e)e.hasOwnProperty(a)&&(u=e[a],r&&t&&typeof u=="object"&&n.hasOwnProperty(a)?(d=n[a],typeof d=="object"?o[a]=vt(u,d,t):o[a]=u):o[a]=u);if(r)for(a in n)n.hasOwnProperty(a)&&!o.hasOwnProperty(a)&&(d=n[a],o[a]=d);return o}var De=vt;function Ho(e,n,t){s.defined("array",e),s.defined("itemToFind",n),s.defined("comparator",t);let o=0,i=e.length-1,r,a;for(;o<=i;){if(r=~~((o+i)/2),a=t(e[r],n),a<0){o=r+1;continue}if(a>0){i=r-1;continue}return r}return~(i+1)}var Be=Ho;function $o(e,n,t,o,i){this.xPoleWander=e,this.yPoleWander=n,this.xPoleOffset=t,this.yPoleOffset=o,this.ut1MinusUtc=i}var hn=$o;function Yo(e){if(e===null||isNaN(e))throw new A("year is required and must be a number.");return e%4===0&&e%100!==0||e%400===0}var dn=Yo;var At=[31,28,31,30,31,30,31,31,30,31,30,31];function Vo(e,n,t,o,i,r,a,u){e=e??1,n=n??1,t=t??1,o=o??0,i=i??0,r=r??0,a=a??0,u=u??!1,v(),I(),this.year=e,this.month=n,this.day=t,this.hour=o,this.minute=i,this.second=r,this.millisecond=a,this.isLeapSecond=u;function v(){s.typeOf.number.greaterThanOrEquals("Year",e,1),s.typeOf.number.lessThanOrEquals("Year",e,9999),s.typeOf.number.greaterThanOrEquals("Month",n,1),s.typeOf.number.lessThanOrEquals("Month",n,12),s.typeOf.number.greaterThanOrEquals("Day",t,1),s.typeOf.number.lessThanOrEquals("Day",t,31),s.typeOf.number.greaterThanOrEquals("Hour",o,0),s.typeOf.number.lessThanOrEquals("Hour",o,23),s.typeOf.number.greaterThanOrEquals("Minute",i,0),s.typeOf.number.lessThanOrEquals("Minute",i,59),s.typeOf.bool("IsLeapSecond",u),s.typeOf.number.greaterThanOrEquals("Second",r,0),s.typeOf.number.lessThanOrEquals("Second",r,u?60:59),s.typeOf.number.greaterThanOrEquals("Millisecond",a,0),s.typeOf.number.lessThan("Millisecond",a,1e3)}function I(){let N=n===2&&dn(e)?At[n-1]+1:At[n-1];if(t>N)throw new A("Month and Day represents invalid date")}}var Tn=Vo;function Xo(e,n){this.julianDate=e,this.offset=n}var ne=Xo;var Zo={SECONDS_PER_MILLISECOND:.001,SECONDS_PER_MINUTE:60,MINUTES_PER_HOUR:60,HOURS_PER_DAY:24,SECONDS_PER_HOUR:3600,MINUTES_PER_DAY:1440,SECONDS_PER_DAY:86400,DAYS_PER_JULIAN_CENTURY:36525,PICOSECOND:1e-9,MODIFIED_JULIAN_DATE_DIFFERENCE:24000005e-1},ie=Object.freeze(Zo);var Jo={UTC:0,TAI:1},H=Object.freeze(Jo);var Mt=new Tn,Cn=[31,28,31,30,31,30,31,31,30,31,30,31],vn=29;function Kn(e,n){return U.compare(e.julianDate,n.julianDate)}var Ke=new ne;function jn(e){Ke.julianDate=e;let n=U.leapSeconds,t=Be(n,Ke,Kn);t<0&&(t=~t),t>=n.length&&(t=n.length-1);let o=n[t].offset;t>0&&U.secondsDifference(n[t].julianDate,e)>o&&(t--,o=n[t].offset),U.addSeconds(e,o,e)}function jt(e,n){Ke.julianDate=e;let t=U.leapSeconds,o=Be(t,Ke,Kn);if(o<0&&(o=~o),o===0)return U.addSeconds(e,-t[0].offset,n);if(o>=t.length)return U.addSeconds(e,-t[o-1].offset,n);let i=U.secondsDifference(t[o].julianDate,e);if(i===0)return U.addSeconds(e,-t[o].offset,n);if(!(i<=1))return U.addSeconds(e,-t[--o].offset,n)}function Ne(e,n,t){let o=n/ie.SECONDS_PER_DAY|0;return e+=o,n-=ie.SECONDS_PER_DAY*o,n<0&&(e--,n+=ie.SECONDS_PER_DAY),t.dayNumber=e,t.secondsOfDay=n,t}function et(e,n,t,o,i,r,a){let u=(n-14)/12|0,d=e+4800+u,m=(1461*d/4|0)+(367*(n-2-12*u)/12|0)-(3*((d+100)/100|0)/4|0)+t-32075;o=o-12,o<0&&(o+=24);let l=r+(o*ie.SECONDS_PER_HOUR+i*ie.SECONDS_PER_MINUTE+a*ie.SECONDS_PER_MILLISECOND);return l>=43200&&(m-=1),[m,l]}var Go=/^(\d{4})$/,Ko=/^(\d{4})-(\d{2})$/,er=/^(\d{4})-?(\d{3})$/,nr=/^(\d{4})-?W(\d{2})-?(\d{1})?$/,tr=/^(\d{4})-?(\d{2})-?(\d{2})$/,nt=/([Z+\-])?(\d{2})?:?(\d{2})?$/,or=/^(\d{2})(\.\d+)?/.source+nt.source,rr=/^(\d{2}):?(\d{2})(\.\d+)?/.source+nt.source,ir=/^(\d{2}):?(\d{2}):?(\d{2})(\.\d+)?/.source+nt.source,Re="Invalid ISO 8601 date.";function U(e,n,t){this.dayNumber=void 0,this.secondsOfDay=void 0,e=e??0,n=n??0,t=t??H.UTC;let o=e|0;n=n+(e-o)*ie.SECONDS_PER_DAY,Ne(o,n,this),t===H.UTC&&jn(this)}U.fromGregorianDate=function(e,n){if(!(e instanceof Tn))throw new A("date must be a valid GregorianDate.");let t=et(e.year,e.month,e.day,e.hour,e.minute,e.second,e.millisecond);return p(n)?(Ne(t[0],t[1],n),jn(n),n):new U(t[0],t[1],H.UTC)};U.fromDate=function(e,n){if(!(e instanceof Date)||isNaN(e.getTime()))throw new A("date must be a valid JavaScript Date.");let t=et(e.getUTCFullYear(),e.getUTCMonth()+1,e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds());return p(n)?(Ne(t[0],t[1],n),jn(n),n):new U(t[0],t[1],H.UTC)};U.fromIso8601=function(e,n){if(typeof e!="string")throw new A(Re);e=e.replace(",",".");let t=e.split("T"),o,i=1,r=1,a=0,u=0,d=0,m=0,l=t[0],w=t[1],E,T;if(!p(l))throw new A(Re);let M;if(t=l.match(tr),t!==null){if(M=l.split("-").length-1,M>0&&M!==2)throw new A(Re);o=+t[1],i=+t[2],r=+t[3]}else if(t=l.match(Ko),t!==null)o=+t[1],i=+t[2];else if(t=l.match(Go),t!==null)o=+t[1];else{let F;if(t=l.match(er),t!==null){if(o=+t[1],F=+t[2],T=dn(o),F<1||T&&F>366||!T&&F>365)throw new A(Re)}else if(t=l.match(nr),t!==null){o=+t[1];let k=+t[2],x=+t[3]||0;if(M=l.split("-").length-1,M>0&&(!p(t[3])&&M!==1||p(t[3])&&M!==2))throw new A(Re);let Q=new Date(Date.UTC(o,0,4));F=k*7+x-Q.getUTCDay()-3}else throw new A(Re);E=new Date(Date.UTC(o,0,1)),E.setUTCDate(F),i=E.getUTCMonth()+1,r=E.getUTCDate()}if(T=dn(o),i<1||i>12||r<1||(i!==2||!T)&&r>Cn[i-1]||T&&i===2&&r>vn)throw new A(Re);let v;if(p(w)){if(t=w.match(ir),t!==null){if(M=w.split(":").length-1,M>0&&M!==2&&M!==3)throw new A(Re);a=+t[1],u=+t[2],d=+t[3],m=+(t[4]||0)*1e3,v=5}else if(t=w.match(rr),t!==null){if(M=w.split(":").length-1,M>2)throw new A(Re);a=+t[1],u=+t[2],d=+(t[3]||0)*60,v=4}else if(t=w.match(or),t!==null)a=+t[1],u=+(t[2]||0)*60,v=3;else throw new A(Re);if(u>=60||d>=61||a>24||a===24&&(u>0||d>0||m>0))throw new A(Re);let F=t[v],k=+t[v+1],x=+(t[v+2]||0);switch(F){case"+":a=a-k,u=u-x;break;case"-":a=a+k,u=u+x;break;case"Z":break;default:u=u+new Date(Date.UTC(o,i-1,r,a,u)).getTimezoneOffset();break}}let I=d===60;for(I&&d--;u>=60;)u-=60,a++;for(;a>=24;)a-=24,r++;for(E=T&&i===2?vn:Cn[i-1];r>E;)r-=E,i++,i>12&&(i-=12,o++),E=T&&i===2?vn:Cn[i-1];for(;u<0;)u+=60,a--;for(;a<0;)a+=24,r--;for(;r<1;)i--,i<1&&(i+=12,o--),E=T&&i===2?vn:Cn[i-1],r+=E;let N=et(o,i,r,a,u,d,m);return p(n)?(Ne(N[0],N[1],n),jn(n)):n=new U(N[0],N[1],H.UTC),I&&U.addSeconds(n,1,n),n};U.now=function(e){return U.fromDate(new Date,e)};var An=new U(0,0,H.TAI);U.toGregorianDate=function(e,n){if(!p(e))throw new A("julianDate is required.");let t=!1,o=jt(e,An);p(o)||(U.addSeconds(e,-1,An),o=jt(An,An),t=!0);let i=o.dayNumber,r=o.secondsOfDay;r>=43200&&(i+=1);let a=i+68569|0,u=4*a/146097|0;a=a-((146097*u+3)/4|0)|0;let d=4e3*(a+1)/1461001|0;a=a-(1461*d/4|0)+31|0;let m=80*a/2447|0,l=a-(2447*m/80|0)|0;a=m/11|0;let w=m+2-12*a|0,E=100*(u-49)+d+a|0,T=r/ie.SECONDS_PER_HOUR|0,M=r-T*ie.SECONDS_PER_HOUR,v=M/ie.SECONDS_PER_MINUTE|0;M=M-v*ie.SECONDS_PER_MINUTE;let I=M|0,N=(M-I)/ie.SECONDS_PER_MILLISECOND;return T+=12,T>23&&(T-=24),t&&(I+=1),p(n)?(n.year=E,n.month=w,n.day=l,n.hour=T,n.minute=v,n.second=I,n.millisecond=N,n.isLeapSecond=t,n):new Tn(E,w,l,T,v,I,N,t)};U.toDate=function(e){if(!p(e))throw new A("julianDate is required.");let n=U.toGregorianDate(e,Mt),t=n.second;return n.isLeapSecond&&(t-=1),new Date(Date.UTC(n.year,n.month-1,n.day,n.hour,n.minute,t,n.millisecond))};U.toIso8601=function(e,n){if(!p(e))throw new A("julianDate is required.");let t=U.toGregorianDate(e,Mt),o=t.year,i=t.month,r=t.day,a=t.hour,u=t.minute,d=t.second,m=t.millisecond;o===1e4&&i===1&&r===1&&a===0&&u===0&&d===0&&m===0&&(o=9999,i=12,r=31,a=24);let l;if(!p(n)&&m!==0){let w=m*.01;return l=w<1e-6?w.toFixed(20).replace(".","").replace(/0+$/,""):w.toString().replace(".",""),`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}.${l}Z`}return!p(n)||n===0?`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}Z`:(l=(m*.01).toFixed(n).replace(".","").slice(0,n),`${o.toString().padStart(4,"0")}-${i.toString().padStart(2,"0")}-${r.toString().padStart(2,"0")}T${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}:${d.toString().padStart(2,"0")}.${l}Z`)};U.clone=function(e,n){if(p(e))return p(n)?(n.dayNumber=e.dayNumber,n.secondsOfDay=e.secondsOfDay,n):new U(e.dayNumber,e.secondsOfDay,H.TAI)};U.compare=function(e,n){if(!p(e))throw new A("left is required.");if(!p(n))throw new A("right is required.");let t=e.dayNumber-n.dayNumber;return t!==0?t:e.secondsOfDay-n.secondsOfDay};U.equals=function(e,n){return e===n||p(e)&&p(n)&&e.dayNumber===n.dayNumber&&e.secondsOfDay===n.secondsOfDay};U.equalsEpsilon=function(e,n,t){return t=t??0,e===n||p(e)&&p(n)&&Math.abs(U.secondsDifference(e,n))<=t};U.totalDays=function(e){if(!p(e))throw new A("julianDate is required.");return e.dayNumber+e.secondsOfDay/ie.SECONDS_PER_DAY};U.secondsDifference=function(e,n){if(!p(e))throw new A("left is required.");if(!p(n))throw new A("right is required.");return(e.dayNumber-n.dayNumber)*ie.SECONDS_PER_DAY+(e.secondsOfDay-n.secondsOfDay)};U.daysDifference=function(e,n){if(!p(e))throw new A("left is required.");if(!p(n))throw new A("right is required.");let t=e.dayNumber-n.dayNumber,o=(e.secondsOfDay-n.secondsOfDay)/ie.SECONDS_PER_DAY;return t+o};U.computeTaiMinusUtc=function(e){Ke.julianDate=e;let n=U.leapSeconds,t=Be(n,Ke,Kn);return t<0&&(t=~t,--t,t<0&&(t=0)),n[t].offset};U.addSeconds=function(e,n,t){if(!p(e))throw new A("julianDate is required.");if(!p(n))throw new A("seconds is required.");if(!p(t))throw new A("result is required.");return Ne(e.dayNumber,e.secondsOfDay+n,t)};U.addMinutes=function(e,n,t){if(!p(e))throw new A("julianDate is required.");if(!p(n))throw new A("minutes is required.");if(!p(t))throw new A("result is required.");let o=e.secondsOfDay+n*ie.SECONDS_PER_MINUTE;return Ne(e.dayNumber,o,t)};U.addHours=function(e,n,t){if(!p(e))throw new A("julianDate is required.");if(!p(n))throw new A("hours is required.");if(!p(t))throw new A("result is required.");let o=e.secondsOfDay+n*ie.SECONDS_PER_HOUR;return Ne(e.dayNumber,o,t)};U.addDays=function(e,n,t){if(!p(e))throw new A("julianDate is required.");if(!p(n))throw new A("days is required.");if(!p(t))throw new A("result is required.");let o=e.dayNumber+n;return Ne(o,e.secondsOfDay,t)};U.lessThan=function(e,n){return U.compare(e,n)<0};U.lessThanOrEquals=function(e,n){return U.compare(e,n)<=0};U.greaterThan=function(e,n){return U.compare(e,n)>0};U.greaterThanOrEquals=function(e,n){return U.compare(e,n)>=0};U.prototype.clone=function(e){return U.clone(this,e)};U.prototype.equals=function(e){return U.equals(this,e)};U.prototype.equalsEpsilon=function(e,n){return U.equalsEpsilon(this,e,n)};U.prototype.toString=function(){return U.toIso8601(this)};U.leapSeconds=[new ne(new U(2441317,43210,H.TAI),10),new ne(new U(2441499,43211,H.TAI),11),new ne(new U(2441683,43212,H.TAI),12),new ne(new U(2442048,43213,H.TAI),13),new ne(new U(2442413,43214,H.TAI),14),new ne(new U(2442778,43215,H.TAI),15),new ne(new U(2443144,43216,H.TAI),16),new ne(new U(2443509,43217,H.TAI),17),new ne(new U(2443874,43218,H.TAI),18),new ne(new U(2444239,43219,H.TAI),19),new ne(new U(2444786,43220,H.TAI),20),new ne(new U(2445151,43221,H.TAI),21),new ne(new U(2445516,43222,H.TAI),22),new ne(new U(2446247,43223,H.TAI),23),new ne(new U(2447161,43224,H.TAI),24),new ne(new U(2447892,43225,H.TAI),25),new ne(new U(2448257,43226,H.TAI),26),new ne(new U(2448804,43227,H.TAI),27),new ne(new U(2449169,43228,H.TAI),28),new ne(new U(2449534,43229,H.TAI),29),new ne(new U(2450083,43230,H.TAI),30),new ne(new U(2450630,43231,H.TAI),31),new ne(new U(2451179,43232,H.TAI),32),new ne(new U(2453736,43233,H.TAI),33),new ne(new U(2454832,43234,H.TAI),34),new ne(new U(2456109,43235,H.TAI),35),new ne(new U(2457204,43236,H.TAI),36),new ne(new U(2457754,43237,H.TAI),37)];var ue=U;var co=Ze(Qe(),1);function cr(e){return(e.length===0||e[e.length-1]!=="/")&&(e=`${e}/`),e}var Nt=cr;function kt(e,n){if(e===null||typeof e!="object")return e;n=n??!1;let t=new e.constructor;for(let o in e)if(e.hasOwnProperty(o)){let i=e[o];n&&(i=kt(i,n)),t[o]=i}return t}var tn=kt;function sr(){let e,n,t=new Promise(function(o,i){e=o,n=i});return{resolve:e,reject:n,promise:t}}var We=sr;var Ft=Ze(Qe(),1);function tt(e,n){let t;return typeof document<"u"&&(t=document),tt._implementation(e,n,t)}tt._implementation=function(e,n,t){if(!p(e))throw new A("relative uri is required.");if(!p(n)){if(typeof t>"u")return e;n=t.baseURI??t.location.href}let o=new Ft.default(e);return o.scheme()!==""?o.toString():o.absoluteTo(n).toString()};var mn=tt;var Lt=Ze(Qe(),1);function ar(e,n){if(!p(e))throw new A("uri is required.");let t="",o=e.lastIndexOf("/");return o!==-1&&(t=e.substring(0,o+1)),n&&(e=new Lt.default(e),e.query().length!==0&&(t+=`?${e.query()}`),e.fragment().length!==0&&(t+=`#${e.fragment()}`)),t}var xt=ar;var Bt=Ze(Qe(),1);function fr(e){if(!p(e))throw new A("uri is required.");let n=new Bt.default(e);n.normalize();let t=n.path(),o=t.lastIndexOf("/");return o!==-1&&(t=t.substr(o+1)),o=t.lastIndexOf("."),o===-1?t="":t=t.substr(o+1),t}var Qt=fr;var Wt={};function ur(e,n,t){p(n)||(n=e.width),p(t)||(t=e.height);let o=Wt[n];p(o)||(o={},Wt[n]=o);let i=o[t];if(!p(i)){let r=document.createElement("canvas");r.width=n,r.height=t,i=r.getContext("2d",{willReadFrequently:!0}),i.globalCompositeOperation="copy",o[t]=i}return i.drawImage(e,0,0,n,t),i.getImageData(0,0,n,t).data}var ot=ur;var pr=/^blob:/i;function hr(e){return s.typeOf.string("uri",e),pr.test(e)}var zn=hr;var Ce;function dr(e){p(Ce)||(Ce=document.createElement("a")),Ce.href=window.location.href;let n=Ce.host,t=Ce.protocol;return Ce.href=e,Ce.href=Ce.href,t!==Ce.protocol||n!==Ce.host}var Ht=dr;var mr=/^data:/i;function yr(e){return s.typeOf.string("uri",e),mr.test(e)}var In=yr;function lr(e){let n=document.createElement("script");return n.async=!0,n.src=e,new Promise((t,o)=>{window.crossOriginIsolated&&n.setAttribute("crossorigin","anonymous");let i=document.getElementsByTagName("head")[0];n.onload=function(){n.onload=void 0,i.removeChild(n),t()},n.onerror=function(r){o(r)},i.appendChild(n)})}var $t=lr;function wr(e){if(!p(e))throw new A("obj is required.");let n="";for(let t in e)if(e.hasOwnProperty(t)){let o=e[t],i=`${encodeURIComponent(t)}=`;if(Array.isArray(o))for(let r=0,a=o.length;r<a;++r)n+=`${i+encodeURIComponent(o[r])}&`;else n+=`${i+encodeURIComponent(o)}&`}return n=n.slice(0,-1),n}var Yt=wr;function br(e){if(!p(e))throw new A("queryString is required.");let n={};if(e==="")return n;let t=e.replace(/\+/g,"%20").split(/[&;]/);for(let o=0,i=t.length;o<i;++o){let r=t[o].split("="),a=decodeURIComponent(r[0]),u=r[1];p(u)?u=decodeURIComponent(u):u="";let d=n[a];typeof d=="string"?n[a]=[d,u]:Array.isArray(d)?d.push(u):n[a]=u}return n}var Vt=br;var Or={UNISSUED:0,ISSUED:1,ACTIVE:2,RECEIVED:3,CANCELLED:4,FAILED:5},ae=Object.freeze(Or);var gr={TERRAIN:0,IMAGERY:1,TILES3D:2,OTHER:3},Xt=Object.freeze(gr);function qn(e){e=e??_e.EMPTY_OBJECT;let n=e.throttleByServer??!1,t=e.throttle??!1;this.url=e.url,this.requestFunction=e.requestFunction,this.cancelFunction=e.cancelFunction,this.priorityFunction=e.priorityFunction,this.priority=e.priority??0,this.throttle=t,this.throttleByServer=n,this.type=e.type??Xt.OTHER,this.serverKey=e.serverKey,this.state=ae.UNISSUED,this.deferred=void 0,this.cancelled=!1}qn.prototype.cancel=function(){this.cancelled=!0};qn.prototype.clone=function(e){return p(e)?(e.url=this.url,e.requestFunction=this.requestFunction,e.cancelFunction=this.cancelFunction,e.priorityFunction=this.priorityFunction,e.priority=this.priority,e.throttle=this.throttle,e.throttleByServer=this.throttleByServer,e.type=this.type,e.serverKey=this.serverKey,e.state=ae.UNISSUED,e.deferred=void 0,e.cancelled=!1,e):new qn(this)};var Zt=qn;function _r(e){let n={};if(!e)return n;let t=e.split(`\r
`);for(let o=0;o<t.length;++o){let i=t[o],r=i.indexOf(": ");if(r>0){let a=i.substring(0,r),u=i.substring(r+2);n[a]=u}}return n}var Jt=_r;function Gt(e,n,t){this.statusCode=e,this.response=n,this.responseHeaders=t,typeof this.responseHeaders=="string"&&(this.responseHeaders=Jt(this.responseHeaders))}Gt.prototype.toString=function(){let e="Request has failed.";return p(this.statusCode)&&(e+=` Status Code: ${this.statusCode}`),e};var yn=Gt;var Dn=Ze(Qe(),1);function ln(){this._listeners=[],this._scopes=[],this._toRemove=[],this._insideRaiseEvent=!1}Object.defineProperties(ln.prototype,{numberOfListeners:{get:function(){return this._listeners.length-this._toRemove.length}}});ln.prototype.addEventListener=function(e,n){s.typeOf.func("listener",e),this._listeners.push(e),this._scopes.push(n);let t=this;return function(){t.removeEventListener(e,n)}};ln.prototype.removeEventListener=function(e,n){s.typeOf.func("listener",e);let t=this._listeners,o=this._scopes,i=-1;for(let r=0;r<t.length;r++)if(t[r]===e&&o[r]===n){i=r;break}return i!==-1?(this._insideRaiseEvent?(this._toRemove.push(i),t[i]=void 0,o[i]=void 0):(t.splice(i,1),o.splice(i,1)),!0):!1};function Sr(e,n){return n-e}ln.prototype.raiseEvent=function(){this._insideRaiseEvent=!0;let e,n=this._listeners,t=this._scopes,o=n.length;for(e=0;e<o;e++){let r=n[e];p(r)&&n[e].apply(t[e],arguments)}let i=this._toRemove;if(o=i.length,o>0){for(i.sort(Sr),e=0;e<o;e++){let r=i[e];n.splice(r,1),t.splice(r,1)}i.length=0}this._insideRaiseEvent=!1};var Kt=ln;function He(e){s.typeOf.object("options",e),s.defined("options.comparator",e.comparator),this._comparator=e.comparator,this._array=[],this._length=0,this._maximumLength=void 0}Object.defineProperties(He.prototype,{length:{get:function(){return this._length}},internalArray:{get:function(){return this._array}},maximumLength:{get:function(){return this._maximumLength},set:function(e){s.typeOf.number.greaterThanOrEquals("maximumLength",e,0);let n=this._length;if(e<n){let t=this._array;for(let o=e;o<n;++o)t[o]=void 0;this._length=e,t.length=e}this._maximumLength=e}},comparator:{get:function(){return this._comparator}}});function rt(e,n,t){let o=e[n];e[n]=e[t],e[t]=o}He.prototype.reserve=function(e){e=e??this._length,this._array.length=e};He.prototype.heapify=function(e){e=e??0;let n=this._length,t=this._comparator,o=this._array,i=-1,r=!0;for(;r;){let a=2*(e+1),u=a-1;u<n&&t(o[u],o[e])<0?i=u:i=e,a<n&&t(o[a],o[i])<0&&(i=a),i!==e?(rt(o,i,e),e=i):r=!1}};He.prototype.resort=function(){let e=this._length;for(let n=Math.ceil(e/2);n>=0;--n)this.heapify(n)};He.prototype.insert=function(e){s.defined("element",e);let n=this._array,t=this._comparator,o=this._maximumLength,i=this._length++;for(i<n.length?n[i]=e:n.push(e);i!==0;){let a=Math.floor((i-1)/2);if(t(n[i],n[a])<0)rt(n,i,a),i=a;else break}let r;return p(o)&&this._length>o&&(r=n[o],this._length=o),r};He.prototype.pop=function(e){if(e=e??0,this._length===0)return;s.typeOf.number.lessThan("index",e,this._length);let n=this._array,t=n[e];return rt(n,e,--this._length),this.heapify(e),n[this._length]=void 0,t};var eo=He;function Rr(e,n){return e.priority-n.priority}var G={numberOfAttemptedRequests:0,numberOfActiveRequests:0,numberOfCancelledRequests:0,numberOfCancelledActiveRequests:0,numberOfFailedRequests:0,numberOfActiveRequestsEver:0,lastNumberOfActiveRequests:0},on=20,le=new eo({comparator:Rr});le.maximumLength=on;le.reserve(on);var ve=[],Pe={},Er=typeof document<"u"?new Dn.default(document.location.href):new Dn.default,Nn=new Kt;function ce(){}ce.maximumRequests=50;ce.maximumRequestsPerServer=18;ce.requestsByServer={};ce.throttleRequests=!0;ce.debugShowStatistics=!1;ce.requestCompletedEvent=Nn;Object.defineProperties(ce,{statistics:{get:function(){return G}},priorityHeapLength:{get:function(){return on},set:function(e){if(e<on)for(;le.length>e;){let n=le.pop();$e(n)}on=e,le.maximumLength=e,le.reserve(e)}}});function no(e){p(e.priorityFunction)&&(e.priority=e.priorityFunction())}ce.serverHasOpenSlots=function(e,n){n=n??1;let t=ce.requestsByServer[e]??ce.maximumRequestsPerServer;return Pe[e]+n<=t};ce.heapHasOpenSlots=function(e){return le.length+e<=on};function to(e){return e.state===ae.UNISSUED&&(e.state=ae.ISSUED,e.deferred=We()),e.deferred.promise}function Tr(e){return function(n){if(e.state===ae.CANCELLED)return;let t=e.deferred;--G.numberOfActiveRequests,--Pe[e.serverKey],Nn.raiseEvent(),e.state=ae.RECEIVED,e.deferred=void 0,t.resolve(n)}}function Cr(e){return function(n){e.state!==ae.CANCELLED&&(++G.numberOfFailedRequests,--G.numberOfActiveRequests,--Pe[e.serverKey],Nn.raiseEvent(n),e.state=ae.FAILED,e.deferred.reject(n))}}function oo(e){let n=to(e);return e.state=ae.ACTIVE,ve.push(e),++G.numberOfActiveRequests,++G.numberOfActiveRequestsEver,++Pe[e.serverKey],e.requestFunction().then(Tr(e)).catch(Cr(e)),n}function $e(e){let n=e.state===ae.ACTIVE;if(e.state=ae.CANCELLED,++G.numberOfCancelledRequests,p(e.deferred)){let t=e.deferred;e.deferred=void 0,t.reject()}n&&(--G.numberOfActiveRequests,--Pe[e.serverKey],++G.numberOfCancelledActiveRequests),p(e.cancelFunction)&&e.cancelFunction()}ce.update=function(){let e,n,t=0,o=ve.length;for(e=0;e<o;++e){if(n=ve[e],n.cancelled&&$e(n),n.state!==ae.ACTIVE){++t;continue}t>0&&(ve[e-t]=n)}ve.length-=t;let i=le.internalArray,r=le.length;for(e=0;e<r;++e)no(i[e]);le.resort();let a=Math.max(ce.maximumRequests-ve.length,0),u=0;for(;u<a&&le.length>0;){if(n=le.pop(),n.cancelled){$e(n);continue}if(n.throttleByServer&&!ce.serverHasOpenSlots(n.serverKey)){$e(n);continue}oo(n),++u}vr()};ce.getServerKey=function(e){s.typeOf.string("url",e);let n=new Dn.default(e);n.scheme()===""&&(n=n.absoluteTo(Er),n.normalize());let t=n.authority();/:/.test(t)||(t=`${t}:${n.scheme()==="https"?"443":"80"}`);let o=Pe[t];return p(o)||(Pe[t]=0),t};ce.request=function(e){if(s.typeOf.object("request",e),s.typeOf.string("request.url",e.url),s.typeOf.func("request.requestFunction",e.requestFunction),In(e.url)||zn(e.url))return Nn.raiseEvent(),e.state=ae.RECEIVED,e.requestFunction();if(++G.numberOfAttemptedRequests,p(e.serverKey)||(e.serverKey=ce.getServerKey(e.url)),ce.throttleRequests&&e.throttleByServer&&!ce.serverHasOpenSlots(e.serverKey))return;if(!ce.throttleRequests||!e.throttle)return oo(e);if(ve.length>=ce.maximumRequests)return;no(e);let n=le.insert(e);if(p(n)){if(n===e)return;$e(n)}return to(e)};function vr(){ce.debugShowStatistics&&(G.numberOfActiveRequests===0&&G.lastNumberOfActiveRequests>0&&(G.numberOfAttemptedRequests>0&&(console.log(`Number of attempted requests: ${G.numberOfAttemptedRequests}`),G.numberOfAttemptedRequests=0),G.numberOfCancelledRequests>0&&(console.log(`Number of cancelled requests: ${G.numberOfCancelledRequests}`),G.numberOfCancelledRequests=0),G.numberOfCancelledActiveRequests>0&&(console.log(`Number of cancelled active requests: ${G.numberOfCancelledActiveRequests}`),G.numberOfCancelledActiveRequests=0),G.numberOfFailedRequests>0&&(console.log(`Number of failed requests: ${G.numberOfFailedRequests}`),G.numberOfFailedRequests=0)),G.lastNumberOfActiveRequests=G.numberOfActiveRequests)}ce.clearForSpecs=function(){for(;le.length>0;){let n=le.pop();$e(n)}let e=ve.length;for(let n=0;n<e;++n)$e(ve[n]);ve.length=0,Pe={},G.numberOfAttemptedRequests=0,G.numberOfActiveRequests=0,G.numberOfCancelledRequests=0,G.numberOfCancelledActiveRequests=0,G.numberOfFailedRequests=0,G.numberOfActiveRequestsEver=0,G.lastNumberOfActiveRequests=0};ce.numberOfActiveRequestsByServer=function(e){return Pe[e]};ce.requestHeap=le;var kn=ce;var ro=Ze(Qe(),1);var wn={},rn={};wn.add=function(e,n){if(!p(e))throw new A("host is required.");if(!p(n)||n<=0)throw new A("port is required to be greater than 0.");let t=`${e.toLowerCase()}:${n}`;p(rn[t])||(rn[t]=!0)};wn.remove=function(e,n){if(!p(e))throw new A("host is required.");if(!p(n)||n<=0)throw new A("port is required to be greater than 0.");let t=`${e.toLowerCase()}:${n}`;p(rn[t])&&delete rn[t]};function Ar(e){let n=new ro.default(e);n.normalize();let t=n.authority();if(t.length!==0){if(n.authority(t),t.indexOf("@")!==-1&&(t=t.split("@")[1]),t.indexOf(":")===-1){let o=n.scheme();if(o.length===0&&(o=window.location.protocol,o=o.substring(0,o.length-1)),o==="http")t+=":80";else if(o==="https")t+=":443";else return}return t}}wn.contains=function(e){if(!p(e))throw new A("url is required.");let n=Ar(e);return!!(p(n)&&p(rn[n]))};wn.clear=function(){rn={}};var it=wn;var so=function(){try{let e=new XMLHttpRequest;return e.open("GET","#",!0),e.responseType="blob",e.responseType==="blob"}catch{return!1}}();function P(e){e=e??_e.EMPTY_OBJECT,typeof e=="string"&&(e={url:e}),s.typeOf.string("options.url",e.url),this._url=void 0,this._templateValues=Ae(e.templateValues,{}),this._queryParameters=Ae(e.queryParameters,{}),this.headers=Ae(e.headers,{}),this.request=e.request??new Zt,this.proxy=e.proxy,this.retryCallback=e.retryCallback,this.retryAttempts=e.retryAttempts??0,this._retryCount=0,e.parseUrl??!0?this.parseUrl(e.url,!0,!0):this._url=e.url,this._credits=e.credits}function Ae(e,n){return p(e)?tn(e):n}P.createIfNeeded=function(e){return e instanceof P?e.getDerivedResource({request:e.request}):typeof e!="string"?e:new P({url:e})};var cn;P.supportsImageBitmapOptions=function(){return p(cn)?cn:typeof createImageBitmap!="function"?(cn=Promise.resolve(!1),cn):(cn=P.fetchBlob({url:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAABGdBTUEAAE4g3rEiDgAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAADElEQVQI12Ng6GAAAAEUAIngE3ZiAAAAAElFTkSuQmCC"}).then(function(n){let t={imageOrientation:"flipY",premultiplyAlpha:"none",colorSpaceConversion:"none"};return Promise.all([createImageBitmap(n,t),createImageBitmap(n)])}).then(function(n){let t=ot(n[0]),o=ot(n[1]);return t[1]!==o[1]}).catch(function(){return!1}),cn)};Object.defineProperties(P,{isBlobSupported:{get:function(){return so}}});Object.defineProperties(P.prototype,{queryParameters:{get:function(){return this._queryParameters}},templateValues:{get:function(){return this._templateValues}},url:{get:function(){return this.getUrlComponent(!0,!0)},set:function(e){this.parseUrl(e,!1,!1)}},extension:{get:function(){return Qt(this._url)}},isDataUri:{get:function(){return In(this._url)}},isBlobUri:{get:function(){return zn(this._url)}},isCrossOriginUrl:{get:function(){return Ht(this._url)}},hasHeaders:{get:function(){return Object.keys(this.headers).length>0}},credits:{get:function(){return this._credits}}});P.prototype.toString=function(){return this.getUrlComponent(!0,!0)};P.prototype.parseUrl=function(e,n,t,o){let i=new co.default(e),r=jr(i.query());this._queryParameters=n?Ln(r,this.queryParameters,t):r,i.search(""),i.fragment(""),p(o)&&i.scheme()===""&&(i=i.absoluteTo(mn(o))),this._url=i.toString()};function jr(e){return e.length===0?{}:e.indexOf("=")===-1?{[e]:void 0}:Vt(e)}function Ln(e,n,t){if(!t)return De(e,n);let o=tn(e,!0);for(let i in n)if(n.hasOwnProperty(i)){let r=o[i],a=n[i];p(r)?(Array.isArray(r)||(r=o[i]=[r]),o[i]=r.concat(a)):o[i]=Array.isArray(a)?a.slice():a}return o}P.prototype.getUrlComponent=function(e,n){if(this.isDataUri)return this._url;let t=this._url;e&&(t=`${t}${Mr(this.queryParameters)}`),t=t.replace(/%7B/g,"{").replace(/%7D/g,"}");let o=this._templateValues;return Object.keys(o).length>0&&(t=t.replace(/{(.*?)}/g,function(i,r){let a=o[r];return p(a)?encodeURIComponent(a):i})),n&&p(this.proxy)&&(t=this.proxy.getURL(t)),t};function Mr(e){let n=Object.keys(e);return n.length===0?"":n.length===1&&!p(e[n[0]])?`?${n[0]}`:`?${Yt(e)}`}P.prototype.setQueryParameters=function(e,n){n?this._queryParameters=Ln(this._queryParameters,e,!1):this._queryParameters=Ln(e,this._queryParameters,!1)};P.prototype.appendQueryParameters=function(e){this._queryParameters=Ln(e,this._queryParameters,!0)};P.prototype.setTemplateValues=function(e,n){n?this._templateValues=De(this._templateValues,e):this._templateValues=De(e,this._templateValues)};P.prototype.getDerivedResource=function(e){let n=this.clone();if(n._retryCount=0,p(e.url)){let t=e.preserveQueryParameters??!1;n.parseUrl(e.url,!0,t,this._url)}return p(e.queryParameters)&&(n._queryParameters=De(e.queryParameters,n.queryParameters)),p(e.templateValues)&&(n._templateValues=De(e.templateValues,n.templateValues)),p(e.headers)&&(n.headers=De(e.headers,n.headers)),p(e.proxy)&&(n.proxy=e.proxy),p(e.request)&&(n.request=e.request),p(e.retryCallback)&&(n.retryCallback=e.retryCallback),p(e.retryAttempts)&&(n.retryAttempts=e.retryAttempts),n};P.prototype.retryOnError=function(e){let n=this.retryCallback;if(typeof n!="function"||this._retryCount>=this.retryAttempts)return Promise.resolve(!1);let t=this;return Promise.resolve(n(this,e)).then(function(o){return++t._retryCount,o})};P.prototype.clone=function(e){return p(e)?(e._url=this._url,e._queryParameters=tn(this._queryParameters),e._templateValues=tn(this._templateValues),e.headers=tn(this.headers),e.proxy=this.proxy,e.retryCallback=this.retryCallback,e.retryAttempts=this.retryAttempts,e._retryCount=0,e.request=this.request.clone(),e):new P({url:this._url,queryParameters:this.queryParameters,templateValues:this.templateValues,headers:this.headers,proxy:this.proxy,retryCallback:this.retryCallback,retryAttempts:this.retryAttempts,request:this.request.clone(),parseUrl:!1,credits:p(this.credits)?this.credits.slice():void 0})};P.prototype.getBaseUri=function(e){return xt(this.getUrlComponent(e),e)};P.prototype.appendForwardSlash=function(){this._url=Nt(this._url)};P.prototype.fetchArrayBuffer=function(){return this.fetch({responseType:"arraybuffer"})};P.fetchArrayBuffer=function(e){return new P(e).fetchArrayBuffer()};P.prototype.fetchBlob=function(){return this.fetch({responseType:"blob"})};P.fetchBlob=function(e){return new P(e).fetchBlob()};P.prototype.fetchImage=function(e){e=e??_e.EMPTY_OBJECT;let n=e.preferImageBitmap??!1,t=e.preferBlob??!1,o=e.flipY??!1,i=e.skipColorSpaceConversion??!1;if(st(this.request),!so||this.isDataUri||this.isBlobUri||!this.hasHeaders&&!t)return ct({resource:this,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:n});let r=this.fetchBlob();if(!p(r))return;let a,u,d,m;return P.supportsImageBitmapOptions().then(function(l){return a=l,u=a&&n,r}).then(function(l){if(!p(l))return;if(m=l,u)return P.createImageBitmapFromBlob(l,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i});let w=window.URL.createObjectURL(l);return d=new P({url:w}),ct({resource:d,flipY:o,skipColorSpaceConversion:i,preferImageBitmap:!1})}).then(function(l){if(p(l))return l.blob=m,u||window.URL.revokeObjectURL(d.url),l}).catch(function(l){return p(d)&&window.URL.revokeObjectURL(d.url),l.blob=m,Promise.reject(l)})};function ct(e){let n=e.resource,t=e.flipY,o=e.skipColorSpaceConversion,i=e.preferImageBitmap,r=n.request;r.url=n.url,r.requestFunction=function(){let u=!1;!n.isDataUri&&!n.isBlobUri&&(u=n.isCrossOriginUrl);let d=We();return P._Implementations.createImage(r,u,d,t,o,i),d.promise};let a=kn.request(r);if(p(a))return a.catch(function(u){return r.state!==ae.FAILED?Promise.reject(u):n.retryOnError(u).then(function(d){return d?(r.state=ae.UNISSUED,r.deferred=void 0,ct({resource:n,flipY:t,skipColorSpaceConversion:o,preferImageBitmap:i})):Promise.reject(u)})})}P.fetchImage=function(e){return new P(e).fetchImage({flipY:e.flipY,skipColorSpaceConversion:e.skipColorSpaceConversion,preferBlob:e.preferBlob,preferImageBitmap:e.preferImageBitmap})};P.prototype.fetchText=function(){return this.fetch({responseType:"text"})};P.fetchText=function(e){return new P(e).fetchText()};P.prototype.fetchJson=function(){let e=this.fetch({responseType:"text",headers:{Accept:"application/json,*/*;q=0.01"}});if(p(e))return e.then(function(n){if(p(n))return JSON.parse(n)})};P.fetchJson=function(e){return new P(e).fetchJson()};P.prototype.fetchXML=function(){return this.fetch({responseType:"document",overrideMimeType:"text/xml"})};P.fetchXML=function(e){return new P(e).fetchXML()};P.prototype.fetchJsonp=function(e){e=e??"callback",st(this.request);let n;do n=`loadJsonp${R.nextRandomNumber().toString().substring(2,8)}`;while(p(window[n]));return ao(this,e,n)};function ao(e,n,t){let o={};o[n]=t,e.setQueryParameters(o);let i=e.request,r=e.url;i.url=r,i.requestFunction=function(){let u=We();return window[t]=function(d){u.resolve(d);try{delete window[t]}catch{window[t]=void 0}},P._Implementations.loadAndExecuteScript(r,t,u),u.promise};let a=kn.request(i);if(p(a))return a.catch(function(u){return i.state!==ae.FAILED?Promise.reject(u):e.retryOnError(u).then(function(d){return d?(i.state=ae.UNISSUED,i.deferred=void 0,ao(e,n,t)):Promise.reject(u)})})}P.fetchJsonp=function(e){return new P(e).fetchJsonp(e.callbackParameterName)};P.prototype._makeRequest=function(e){let n=this;st(n.request);let t=n.request,o=n.url;t.url=o,t.requestFunction=function(){let r=e.responseType,a=De(e.headers,n.headers),u=e.overrideMimeType,d=e.method,m=e.data,l=We(),w=P._Implementations.loadWithXhr(o,r,d,m,a,l,u);return p(w)&&p(w.abort)&&(t.cancelFunction=function(){w.abort()}),l.promise};let i=kn.request(t);if(p(i))return i.then(function(r){return t.cancelFunction=void 0,r}).catch(function(r){return t.cancelFunction=void 0,t.state!==ae.FAILED?Promise.reject(r):n.retryOnError(r).then(function(a){return a?(t.state=ae.UNISSUED,t.deferred=void 0,n.fetch(e)):Promise.reject(r)})})};function st(e){if(e.state===ae.ISSUED||e.state===ae.ACTIVE)throw new Se("The Resource is already being fetched.");e.state=ae.UNISSUED,e.deferred=void 0}var Pr=/^data:(.*?)(;base64)?,(.*)$/;function Fn(e,n){let t=decodeURIComponent(n);return e?atob(t):t}function io(e,n){let t=Fn(e,n),o=new ArrayBuffer(t.length),i=new Uint8Array(o);for(let r=0;r<t.length;r++)i[r]=t.charCodeAt(r);return o}function Ur(e,n){n=n??"";let t=e[1],o=!!e[2],i=e[3],r,a;switch(n){case"":case"text":return Fn(o,i);case"arraybuffer":return io(o,i);case"blob":return r=io(o,i),new Blob([r],{type:t});case"document":return a=new DOMParser,a.parseFromString(Fn(o,i),t);case"json":return JSON.parse(Fn(o,i));default:throw new A(`Unhandled responseType: ${n}`)}}P.prototype.fetch=function(e){return e=Ae(e,{}),e.method="GET",this._makeRequest(e)};P.fetch=function(e){return new P(e).fetch({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};P.prototype.delete=function(e){return e=Ae(e,{}),e.method="DELETE",this._makeRequest(e)};P.delete=function(e){return new P(e).delete({responseType:e.responseType,overrideMimeType:e.overrideMimeType,data:e.data})};P.prototype.head=function(e){return e=Ae(e,{}),e.method="HEAD",this._makeRequest(e)};P.head=function(e){return new P(e).head({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};P.prototype.options=function(e){return e=Ae(e,{}),e.method="OPTIONS",this._makeRequest(e)};P.options=function(e){return new P(e).options({responseType:e.responseType,overrideMimeType:e.overrideMimeType})};P.prototype.post=function(e,n){return s.defined("data",e),n=Ae(n,{}),n.method="POST",n.data=e,this._makeRequest(n)};P.post=function(e){return new P(e).post(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};P.prototype.put=function(e,n){return s.defined("data",e),n=Ae(n,{}),n.method="PUT",n.data=e,this._makeRequest(n)};P.put=function(e){return new P(e).put(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};P.prototype.patch=function(e,n){return s.defined("data",e),n=Ae(n,{}),n.method="PATCH",n.data=e,this._makeRequest(n)};P.patch=function(e){return new P(e).patch(e.data,{responseType:e.responseType,overrideMimeType:e.overrideMimeType})};P._Implementations={};P._Implementations.loadImageElement=function(e,n,t){let o=new Image;o.onload=function(){o.naturalWidth===0&&o.naturalHeight===0&&o.width===0&&o.height===0&&(o.width=300,o.height=150),t.resolve(o)},o.onerror=function(i){t.reject(i)},n&&(it.contains(e)?o.crossOrigin="use-credentials":o.crossOrigin=""),o.src=e};P._Implementations.createImage=function(e,n,t,o,i,r){let a=e.url;P.supportsImageBitmapOptions().then(function(u){if(!(u&&r)){P._Implementations.loadImageElement(a,n,t);return}let d="blob",m="GET",l=We(),w=P._Implementations.loadWithXhr(a,d,m,void 0,void 0,l,void 0,void 0,void 0);return p(w)&&p(w.abort)&&(e.cancelFunction=function(){w.abort()}),l.promise.then(function(E){if(!p(E)){t.reject(new Se(`Successfully retrieved ${a} but it contained no content.`));return}return P.createImageBitmapFromBlob(E,{flipY:o,premultiplyAlpha:!1,skipColorSpaceConversion:i})}).then(function(E){t.resolve(E)})}).catch(function(u){t.reject(u)})};P.createImageBitmapFromBlob=function(e,n){return s.defined("options",n),s.typeOf.bool("options.flipY",n.flipY),s.typeOf.bool("options.premultiplyAlpha",n.premultiplyAlpha),s.typeOf.bool("options.skipColorSpaceConversion",n.skipColorSpaceConversion),createImageBitmap(e,{imageOrientation:n.flipY?"flipY":"from-image",premultiplyAlpha:n.premultiplyAlpha?"premultiply":"none",colorSpaceConversion:n.skipColorSpaceConversion?"none":"default"})};function zr(e,n,t,o,i,r,a){fetch(e,{method:t,headers:i}).then(async u=>{if(!u.ok){let d={};u.headers.forEach((m,l)=>{d[l]=m}),r.reject(new yn(u.status,u,d));return}switch(n){case"text":r.resolve(u.text());break;case"json":r.resolve(u.json());break;default:r.resolve(new Uint8Array(await u.arrayBuffer()).buffer);break}}).catch(()=>{r.reject(new yn)})}var Ir=typeof XMLHttpRequest>"u";P._Implementations.loadWithXhr=function(e,n,t,o,i,r,a){let u=Pr.exec(e);if(u!==null){r.resolve(Ur(u,n));return}if(Ir){zr(e,n,t,o,i,r,a);return}let d=new XMLHttpRequest;if(it.contains(e)&&(d.withCredentials=!0),d.open(t,e,!0),p(a)&&p(d.overrideMimeType)&&d.overrideMimeType(a),p(i))for(let l in i)i.hasOwnProperty(l)&&d.setRequestHeader(l,i[l]);p(n)&&(d.responseType=n);let m=!1;return typeof e=="string"&&(m=e.indexOf("file://")===0||typeof window<"u"&&window.location.origin==="file://"),d.onload=function(){if((d.status<200||d.status>=300)&&!(m&&d.status===0)){r.reject(new yn(d.status,d.response,d.getAllResponseHeaders()));return}let l=d.response,w=d.responseType;if(t==="HEAD"||t==="OPTIONS"){let T=d.getAllResponseHeaders().trim().split(/[\r\n]+/),M={};T.forEach(function(v){let I=v.split(": "),N=I.shift();M[N]=I.join(": ")}),r.resolve(M);return}if(d.status===204)r.resolve(void 0);else if(p(l)&&(!p(n)||w===n))r.resolve(l);else if(n==="json"&&typeof l=="string")try{r.resolve(JSON.parse(l))}catch(E){r.reject(E)}else(w===""||w==="document")&&p(d.responseXML)&&d.responseXML.hasChildNodes()?r.resolve(d.responseXML):(w===""||w==="text")&&p(d.responseText)?r.resolve(d.responseText):r.reject(new Se("Invalid XMLHttpRequest response type."))},d.onerror=function(l){r.reject(new yn)},d.send(o),d};P._Implementations.loadAndExecuteScript=function(e,n,t){return $t(e,n).catch(function(o){t.reject(o)})};P._DefaultImplementations={};P._DefaultImplementations.createImage=P._Implementations.createImage;P._DefaultImplementations.loadWithXhr=P._Implementations.loadWithXhr;P._DefaultImplementations.loadAndExecuteScript=P._Implementations.loadAndExecuteScript;P.DEFAULT=Object.freeze(new P({url:typeof document>"u"?"":document.location.href.split("?")[0]}));var ke=P;function On(e){e=e??_e.EMPTY_OBJECT,this._dates=void 0,this._samples=void 0,this._dateColumn=-1,this._xPoleWanderRadiansColumn=-1,this._yPoleWanderRadiansColumn=-1,this._ut1MinusUtcSecondsColumn=-1,this._xCelestialPoleOffsetRadiansColumn=-1,this._yCelestialPoleOffsetRadiansColumn=-1,this._taiMinusUtcSecondsColumn=-1,this._columnCount=0,this._lastIndex=-1,this._addNewLeapSeconds=e.addNewLeapSeconds??!0,p(e.data)?fo(this,e.data):fo(this,{columnNames:["dateIso8601","modifiedJulianDateUtc","xPoleWanderRadians","yPoleWanderRadians","ut1MinusUtcSeconds","lengthOfDayCorrectionSeconds","xCelestialPoleOffsetRadians","yCelestialPoleOffsetRadians","taiMinusUtcSeconds"],samples:[]})}On.fromUrl=async function(e,n){s.defined("url",e),n=n??_e.EMPTY_OBJECT;let t=ke.createIfNeeded(e),o;try{o=await t.fetchJson()}catch{throw new Se(`An error occurred while retrieving the EOP data from the URL ${t.url}.`)}return new On({addNewLeapSeconds:n.addNewLeapSeconds,data:o})};On.NONE=Object.freeze({compute:function(e,n){return p(n)?(n.xPoleWander=0,n.yPoleWander=0,n.xPoleOffset=0,n.yPoleOffset=0,n.ut1MinusUtc=0):n=new hn(0,0,0,0,0),n}});On.prototype.compute=function(e,n){if(!p(this._samples))return;if(p(n)||(n=new hn(0,0,0,0,0)),this._samples.length===0)return n.xPoleWander=0,n.yPoleWander=0,n.xPoleOffset=0,n.yPoleOffset=0,n.ut1MinusUtc=0,n;let t=this._dates,o=this._lastIndex,i=0,r=0;if(p(o)){let u=t[o],d=t[o+1],m=ue.lessThanOrEquals(u,e),l=!p(d),w=l||ue.greaterThanOrEquals(d,e);if(m&&w)return i=o,!l&&d.equals(e)&&++i,r=i+1,po(this,t,this._samples,e,i,r,n),n}let a=Be(t,e,ue.compare,this._dateColumn);return a>=0?(a<t.length-1&&t[a+1].equals(e)&&++a,i=a,r=a):(r=~a,i=r-1,i<0&&(i=0)),this._lastIndex=i,po(this,t,this._samples,e,i,r,n),n};function qr(e,n){return ue.compare(e.julianDate,n)}function fo(e,n){if(!p(n.columnNames))throw new Se("Error in loaded EOP data: The columnNames property is required.");if(!p(n.samples))throw new Se("Error in loaded EOP data: The samples property is required.");let t=n.columnNames.indexOf("modifiedJulianDateUtc"),o=n.columnNames.indexOf("xPoleWanderRadians"),i=n.columnNames.indexOf("yPoleWanderRadians"),r=n.columnNames.indexOf("ut1MinusUtcSeconds"),a=n.columnNames.indexOf("xCelestialPoleOffsetRadians"),u=n.columnNames.indexOf("yCelestialPoleOffsetRadians"),d=n.columnNames.indexOf("taiMinusUtcSeconds");if(t<0||o<0||i<0||r<0||a<0||u<0||d<0)throw new Se("Error in loaded EOP data: The columnNames property must include modifiedJulianDateUtc, xPoleWanderRadians, yPoleWanderRadians, ut1MinusUtcSeconds, xCelestialPoleOffsetRadians, yCelestialPoleOffsetRadians, and taiMinusUtcSeconds columns");let m=e._samples=n.samples,l=e._dates=[];e._dateColumn=t,e._xPoleWanderRadiansColumn=o,e._yPoleWanderRadiansColumn=i,e._ut1MinusUtcSecondsColumn=r,e._xCelestialPoleOffsetRadiansColumn=a,e._yCelestialPoleOffsetRadiansColumn=u,e._taiMinusUtcSecondsColumn=d,e._columnCount=n.columnNames.length,e._lastIndex=void 0;let w,E=e._addNewLeapSeconds;for(let T=0,M=m.length;T<M;T+=e._columnCount){let v=m[T+t],I=m[T+d],N=v+ie.MODIFIED_JULIAN_DATE_DIFFERENCE,F=new ue(N,I,H.TAI);if(l.push(F),E){if(I!==w&&p(w)){let k=ue.leapSeconds,x=Be(k,F,qr);if(x<0){let Q=new ne(F,I);k.splice(~x,0,Q)}}w=I}}}function uo(e,n,t,o,i){let r=t*o;i.xPoleWander=n[r+e._xPoleWanderRadiansColumn],i.yPoleWander=n[r+e._yPoleWanderRadiansColumn],i.xPoleOffset=n[r+e._xCelestialPoleOffsetRadiansColumn],i.yPoleOffset=n[r+e._yCelestialPoleOffsetRadiansColumn],i.ut1MinusUtc=n[r+e._ut1MinusUtcSecondsColumn]}function bn(e,n,t){return n+e*(t-n)}function po(e,n,t,o,i,r,a){let u=e._columnCount;if(r>n.length-1)return a.xPoleWander=0,a.yPoleWander=0,a.xPoleOffset=0,a.yPoleOffset=0,a.ut1MinusUtc=0,a;let d=n[i],m=n[r];if(d.equals(m)||o.equals(d))return uo(e,t,i,u,a),a;if(o.equals(m))return uo(e,t,r,u,a),a;let l=ue.secondsDifference(o,d)/ue.secondsDifference(m,d),w=i*u,E=r*u,T=t[w+e._ut1MinusUtcSecondsColumn],M=t[E+e._ut1MinusUtcSecondsColumn],v=M-T;if(v>.5||v<-.5){let I=t[w+e._taiMinusUtcSecondsColumn],N=t[E+e._taiMinusUtcSecondsColumn];I!==N&&(m.equals(o)?T=M:M-=N-I)}return a.xPoleWander=bn(l,t[w+e._xPoleWanderRadiansColumn],t[E+e._xPoleWanderRadiansColumn]),a.yPoleWander=bn(l,t[w+e._yPoleWanderRadiansColumn],t[E+e._yPoleWanderRadiansColumn]),a.xPoleOffset=bn(l,t[w+e._xCelestialPoleOffsetRadiansColumn],t[E+e._xCelestialPoleOffsetRadiansColumn]),a.yPoleOffset=bn(l,t[w+e._yCelestialPoleOffsetRadiansColumn],t[E+e._yCelestialPoleOffsetRadiansColumn]),a.ut1MinusUtc=bn(l,T,M),a}var ho=On;function we(e,n,t){this.heading=e??0,this.pitch=n??0,this.roll=t??0}we.fromQuaternion=function(e,n){if(!p(e))throw new A("quaternion is required");p(n)||(n=new we);let t=2*(e.w*e.y-e.z*e.x),o=1-2*(e.x*e.x+e.y*e.y),i=2*(e.w*e.x+e.y*e.z),r=1-2*(e.y*e.y+e.z*e.z),a=2*(e.w*e.z+e.x*e.y);return n.heading=-Math.atan2(a,r),n.roll=Math.atan2(i,o),n.pitch=-R.asinClamped(t),n};we.fromDegrees=function(e,n,t,o){if(!p(e))throw new A("heading is required");if(!p(n))throw new A("pitch is required");if(!p(t))throw new A("roll is required");return p(o)||(o=new we),o.heading=e*R.RADIANS_PER_DEGREE,o.pitch=n*R.RADIANS_PER_DEGREE,o.roll=t*R.RADIANS_PER_DEGREE,o};we.clone=function(e,n){if(p(e))return p(n)?(n.heading=e.heading,n.pitch=e.pitch,n.roll=e.roll,n):new we(e.heading,e.pitch,e.roll)};we.equals=function(e,n){return e===n||p(e)&&p(n)&&e.heading===n.heading&&e.pitch===n.pitch&&e.roll===n.roll};we.equalsEpsilon=function(e,n,t,o){return e===n||p(e)&&p(n)&&R.equalsEpsilon(e.heading,n.heading,t,o)&&R.equalsEpsilon(e.pitch,n.pitch,t,o)&&R.equalsEpsilon(e.roll,n.roll,t,o)};we.prototype.clone=function(e){return we.clone(this,e)};we.prototype.equals=function(e){return we.equals(this,e)};we.prototype.equalsEpsilon=function(e,n,t){return we.equalsEpsilon(this,e,n,t)};we.prototype.toString=function(){return`(${this.heading}, ${this.pitch}, ${this.roll})`};var xn=we;var mo=/((?:.*\/)|^)Cesium\.js(?:\?|\#|$)/;function Dr(){let e=document.getElementsByTagName("script");for(let n=0,t=e.length;n<t;++n){let o=e[n].getAttribute("src"),i=mo.exec(o);if(i!==null)return i[1]}}var Bn;function yo(e){return typeof document>"u"?e:(p(Bn)||(Bn=document.createElement("a")),Bn.href=e,Bn.href)}var Ye;function lo(){if(p(Ye))return Ye;let e;if(typeof CESIUM_BASE_URL<"u"?e=CESIUM_BASE_URL:p(import.meta?.url)?e=mn(".",import.meta.url):typeof define=="object"&&p(define.amd)&&!define.amd.toUrlUndefined&&p(Sn.toUrl)?e=mn("..",Ve("Core/buildModuleUrl.js")):e=Dr(),!p(e))throw new A("Unable to determine Cesium base URL automatically, try defining a global variable called CESIUM_BASE_URL.");return Ye=new ke({url:yo(e)}),Ye.appendForwardSlash(),Ye}function Nr(e){return yo(Sn.toUrl(`../${e}`))}function wo(e){return lo().getDerivedResource({url:e}).url}var Qn;function Ve(e){return p(Qn)||(typeof define=="object"&&p(define.amd)&&!define.amd.toUrlUndefined&&p(Sn.toUrl)?Qn=Nr:Qn=wo),Qn(e)}Ve._cesiumScriptRegex=mo;Ve._buildModuleUrlFromBaseUrl=wo;Ve._clearBaseResource=function(){Ye=void 0};Ve.setBaseUrl=function(e){Ye=ke.DEFAULT.getDerivedResource({url:e})};Ve.getCesiumBaseUrl=lo;var bo=Ve;function kr(e,n,t){this.x=e,this.y=n,this.s=t}var Wn=kr;function ut(e){e=e??_e.EMPTY_OBJECT,this._xysFileUrlTemplate=ke.createIfNeeded(e.xysFileUrlTemplate),this._interpolationOrder=e.interpolationOrder??9,this._sampleZeroJulianEphemerisDate=e.sampleZeroJulianEphemerisDate??24423965e-1,this._sampleZeroDateTT=new ue(this._sampleZeroJulianEphemerisDate,0,H.TAI),this._stepSizeDays=e.stepSizeDays??1,this._samplesPerXysFile=e.samplesPerXysFile??1e3,this._totalSamples=e.totalSamples??27426,this._samples=new Array(this._totalSamples*3),this._chunkDownloadsInProgress=[];let n=this._interpolationOrder,t=this._denominators=new Array(n+1),o=this._xTable=new Array(n+1),i=Math.pow(this._stepSizeDays,n);for(let r=0;r<=n;++r){t[r]=i,o[r]=r*this._stepSizeDays;for(let a=0;a<=n;++a)a!==r&&(t[r]*=r-a);t[r]=1/t[r]}this._work=new Array(n+1),this._coef=new Array(n+1)}var Fr=new ue(0,0,H.TAI);function at(e,n,t){let o=Fr;return o.dayNumber=n,o.secondsOfDay=t,ue.daysDifference(o,e._sampleZeroDateTT)}ut.prototype.preload=function(e,n,t,o){let i=at(this,e,n),r=at(this,t,o),a=i/this._stepSizeDays-this._interpolationOrder/2|0;a<0&&(a=0);let u=r/this._stepSizeDays-this._interpolationOrder/2|0+this._interpolationOrder;u>=this._totalSamples&&(u=this._totalSamples-1);let d=a/this._samplesPerXysFile|0,m=u/this._samplesPerXysFile|0,l=[];for(let w=d;w<=m;++w)l.push(ft(this,w));return Promise.all(l)};ut.prototype.computeXysRadians=function(e,n,t){let o=at(this,e,n);if(o<0)return;let i=o/this._stepSizeDays|0;if(i>=this._totalSamples)return;let r=this._interpolationOrder,a=i-(r/2|0);a<0&&(a=0);let u=a+r;u>=this._totalSamples&&(u=this._totalSamples-1,a=u-r,a<0&&(a=0));let d=!1,m=this._samples;if(p(m[a*3])||(ft(this,a/this._samplesPerXysFile|0),d=!0),p(m[u*3])||(ft(this,u/this._samplesPerXysFile|0),d=!0),d)return;p(t)?(t.x=0,t.y=0,t.s=0):t=new Wn(0,0,0);let l=o-a*this._stepSizeDays,w=this._work,E=this._denominators,T=this._coef,M=this._xTable,v,I;for(v=0;v<=r;++v)w[v]=l-M[v];for(v=0;v<=r;++v){for(T[v]=1,I=0;I<=r;++I)I!==v&&(T[v]*=w[I]);T[v]*=E[v];let N=(a+v)*3;t.x+=T[v]*m[N++],t.y+=T[v]*m[N++],t.s+=T[v]*m[N]}return t};function ft(e,n){if(e._chunkDownloadsInProgress[n])return e._chunkDownloadsInProgress[n];let t,o=e._xysFileUrlTemplate;p(o)?t=o.getDerivedResource({templateValues:{0:n}}):t=new ke({url:bo(`Assets/IAU2006_XYS/IAU2006_XYS_${n}.json`)});let i=t.fetchJson().then(function(r){e._chunkDownloadsInProgress[n]=!1;let a=e._samples,u=r.samples,d=n*e._samplesPerXysFile*3;for(let m=0,l=u.length;m<l;++m)a[d+m]=u[m]});return e._chunkDownloadsInProgress[n]=i,i}var Oo=ut;function S(e,n,t,o){this.x=e??0,this.y=n??0,this.z=t??0,this.w=o??0}var gn=new g;S.fromAxisAngle=function(e,n,t){s.typeOf.object("axis",e),s.typeOf.number("angle",n);let o=n/2,i=Math.sin(o);gn=g.normalize(e,gn);let r=gn.x*i,a=gn.y*i,u=gn.z*i,d=Math.cos(o);return p(t)?(t.x=r,t.y=a,t.z=u,t.w=d,t):new S(r,a,u,d)};var Lr=[1,2,0],xr=new Array(3);S.fromRotationMatrix=function(e,n){s.typeOf.object("matrix",e);let t,o,i,r,a,u=e[B.COLUMN0ROW0],d=e[B.COLUMN1ROW1],m=e[B.COLUMN2ROW2],l=u+d+m;if(l>0)t=Math.sqrt(l+1),a=.5*t,t=.5/t,o=(e[B.COLUMN1ROW2]-e[B.COLUMN2ROW1])*t,i=(e[B.COLUMN2ROW0]-e[B.COLUMN0ROW2])*t,r=(e[B.COLUMN0ROW1]-e[B.COLUMN1ROW0])*t;else{let w=Lr,E=0;d>u&&(E=1),m>u&&m>d&&(E=2);let T=w[E],M=w[T];t=Math.sqrt(e[B.getElementIndex(E,E)]-e[B.getElementIndex(T,T)]-e[B.getElementIndex(M,M)]+1);let v=xr;v[E]=.5*t,t=.5/t,a=(e[B.getElementIndex(M,T)]-e[B.getElementIndex(T,M)])*t,v[T]=(e[B.getElementIndex(T,E)]+e[B.getElementIndex(E,T)])*t,v[M]=(e[B.getElementIndex(M,E)]+e[B.getElementIndex(E,M)])*t,o=-v[0],i=-v[1],r=-v[2]}return p(n)?(n.x=o,n.y=i,n.z=r,n.w=a,n):new S(o,i,r,a)};var go=new S,_o=new S,pt=new S,So=new S;S.fromHeadingPitchRoll=function(e,n){return s.typeOf.object("headingPitchRoll",e),So=S.fromAxisAngle(g.UNIT_X,e.roll,go),pt=S.fromAxisAngle(g.UNIT_Y,-e.pitch,n),n=S.multiply(pt,So,pt),_o=S.fromAxisAngle(g.UNIT_Z,-e.heading,go),S.multiply(_o,n,n)};var Hn=new g,ht=new g,Ee=new S,Ro=new S,$n=new S;S.packedLength=4;S.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=t??0,n[t++]=e.x,n[t++]=e.y,n[t++]=e.z,n[t]=e.w,n};S.unpack=function(e,n,t){return s.defined("array",e),n=n??0,p(t)||(t=new S),t.x=e[n],t.y=e[n+1],t.z=e[n+2],t.w=e[n+3],t};S.packedInterpolationLength=3;S.convertPackedArrayForInterpolation=function(e,n,t,o){S.unpack(e,t*4,$n),S.conjugate($n,$n);for(let i=0,r=t-n+1;i<r;i++){let a=i*3;S.unpack(e,(n+i)*4,Ee),S.multiply(Ee,$n,Ee),Ee.w<0&&S.negate(Ee,Ee),S.computeAxis(Ee,Hn);let u=S.computeAngle(Ee);p(o)||(o=[]),o[a]=Hn.x*u,o[a+1]=Hn.y*u,o[a+2]=Hn.z*u}};S.unpackInterpolationResult=function(e,n,t,o,i){p(i)||(i=new S),g.fromArray(e,0,ht);let r=g.magnitude(ht);return S.unpack(n,o*4,Ro),r===0?S.clone(S.IDENTITY,Ee):S.fromAxisAngle(ht,r,Ee),S.multiply(Ee,Ro,i)};S.clone=function(e,n){if(p(e))return p(n)?(n.x=e.x,n.y=e.y,n.z=e.z,n.w=e.w,n):new S(e.x,e.y,e.z,e.w)};S.conjugate=function(e,n){return s.typeOf.object("quaternion",e),s.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n.z=-e.z,n.w=e.w,n};S.magnitudeSquared=function(e){return s.typeOf.object("quaternion",e),e.x*e.x+e.y*e.y+e.z*e.z+e.w*e.w};S.magnitude=function(e){return Math.sqrt(S.magnitudeSquared(e))};S.normalize=function(e,n){s.typeOf.object("result",n);let t=1/S.magnitude(e),o=e.x*t,i=e.y*t,r=e.z*t,a=e.w*t;return n.x=o,n.y=i,n.z=r,n.w=a,n};S.inverse=function(e,n){s.typeOf.object("result",n);let t=S.magnitudeSquared(e);return n=S.conjugate(e,n),S.multiplyByScalar(n,1/t,n)};S.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x+n.x,t.y=e.y+n.y,t.z=e.z+n.z,t.w=e.w+n.w,t};S.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t.x=e.x-n.x,t.y=e.y-n.y,t.z=e.z-n.z,t.w=e.w-n.w,t};S.negate=function(e,n){return s.typeOf.object("quaternion",e),s.typeOf.object("result",n),n.x=-e.x,n.y=-e.y,n.z=-e.z,n.w=-e.w,n};S.dot=function(e,n){return s.typeOf.object("left",e),s.typeOf.object("right",n),e.x*n.x+e.y*n.y+e.z*n.z+e.w*n.w};S.multiply=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e.x,i=e.y,r=e.z,a=e.w,u=n.x,d=n.y,m=n.z,l=n.w,w=a*u+o*l+i*m-r*d,E=a*d-o*m+i*l+r*u,T=a*m+o*d-i*u+r*l,M=a*l-o*u-i*d-r*m;return t.x=w,t.y=E,t.z=T,t.w=M,t};S.multiplyByScalar=function(e,n,t){return s.typeOf.object("quaternion",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x*n,t.y=e.y*n,t.z=e.z*n,t.w=e.w*n,t};S.divideByScalar=function(e,n,t){return s.typeOf.object("quaternion",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t.x=e.x/n,t.y=e.y/n,t.z=e.z/n,t.w=e.w/n,t};S.computeAxis=function(e,n){s.typeOf.object("quaternion",e),s.typeOf.object("result",n);let t=e.w;if(Math.abs(t-1)<R.EPSILON6||Math.abs(t+1)<R.EPSILON6)return n.x=1,n.y=n.z=0,n;let o=1/Math.sqrt(1-t*t);return n.x=e.x*o,n.y=e.y*o,n.z=e.z*o,n};S.computeAngle=function(e){return s.typeOf.object("quaternion",e),Math.abs(e.w-1)<R.EPSILON6?0:2*Math.acos(e.w)};var dt=new S;S.lerp=function(e,n,t,o){return s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o),dt=S.multiplyByScalar(n,t,dt),o=S.multiplyByScalar(e,1-t,o),S.add(dt,o,o)};var Eo=new S,mt=new S,yt=new S;S.slerp=function(e,n,t,o){s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o);let i=S.dot(e,n),r=n;if(i<0&&(i=-i,r=Eo=S.negate(n,Eo)),1-i<R.EPSILON6)return S.lerp(e,r,t,o);let a=Math.acos(i);return mt=S.multiplyByScalar(e,Math.sin((1-t)*a),mt),yt=S.multiplyByScalar(r,Math.sin(t*a),yt),o=S.add(mt,yt,o),S.multiplyByScalar(o,1/Math.sin(a),o)};S.log=function(e,n){s.typeOf.object("quaternion",e),s.typeOf.object("result",n);let t=R.acosClamped(e.w),o=0;return t!==0&&(o=t/Math.sin(t)),g.multiplyByScalar(e,o,n)};S.exp=function(e,n){s.typeOf.object("cartesian",e),s.typeOf.object("result",n);let t=g.magnitude(e),o=0;return t!==0&&(o=Math.sin(t)/t),n.x=e.x*o,n.y=e.y*o,n.z=e.z*o,n.w=Math.cos(t),n};var Br=new g,Qr=new g,_n=new S,sn=new S;S.computeInnerQuadrangle=function(e,n,t,o){s.typeOf.object("q0",e),s.typeOf.object("q1",n),s.typeOf.object("q2",t),s.typeOf.object("result",o);let i=S.conjugate(n,_n);S.multiply(i,t,sn);let r=S.log(sn,Br);S.multiply(i,e,sn);let a=S.log(sn,Qr);return g.add(r,a,r),g.multiplyByScalar(r,.25,r),g.negate(r,r),S.exp(r,_n),S.multiply(n,_n,o)};S.squad=function(e,n,t,o,i,r){s.typeOf.object("q0",e),s.typeOf.object("q1",n),s.typeOf.object("s0",t),s.typeOf.object("s1",o),s.typeOf.number("t",i),s.typeOf.object("result",r);let a=S.slerp(e,n,i,_n),u=S.slerp(t,o,i,sn);return S.slerp(a,u,2*i*(1-i),r)};var Wr=new S,To=1.9011074535173003,Yn=un.supportsTypedArrays()?new Float32Array(8):[],Vn=un.supportsTypedArrays()?new Float32Array(8):[],Ue=un.supportsTypedArrays()?new Float32Array(8):[],ze=un.supportsTypedArrays()?new Float32Array(8):[];for(let e=0;e<7;++e){let n=e+1,t=2*n+1;Yn[e]=1/(n*t),Vn[e]=n/t}Yn[7]=To/136;Vn[7]=To*8/17;S.fastSlerp=function(e,n,t,o){s.typeOf.object("start",e),s.typeOf.object("end",n),s.typeOf.number("t",t),s.typeOf.object("result",o);let i=S.dot(e,n),r;i>=0?r=1:(r=-1,i=-i);let a=i-1,u=1-t,d=t*t,m=u*u;for(let T=7;T>=0;--T)Ue[T]=(Yn[T]*d-Vn[T])*a,ze[T]=(Yn[T]*m-Vn[T])*a;let l=r*t*(1+Ue[0]*(1+Ue[1]*(1+Ue[2]*(1+Ue[3]*(1+Ue[4]*(1+Ue[5]*(1+Ue[6]*(1+Ue[7])))))))),w=u*(1+ze[0]*(1+ze[1]*(1+ze[2]*(1+ze[3]*(1+ze[4]*(1+ze[5]*(1+ze[6]*(1+ze[7])))))))),E=S.multiplyByScalar(e,w,Wr);return S.multiplyByScalar(n,l,o),S.add(E,o,o)};S.fastSquad=function(e,n,t,o,i,r){s.typeOf.object("q0",e),s.typeOf.object("q1",n),s.typeOf.object("s0",t),s.typeOf.object("s1",o),s.typeOf.number("t",i),s.typeOf.object("result",r);let a=S.fastSlerp(e,n,i,_n),u=S.fastSlerp(t,o,i,sn);return S.fastSlerp(a,u,2*i*(1-i),r)};S.equals=function(e,n){return e===n||p(e)&&p(n)&&e.x===n.x&&e.y===n.y&&e.z===n.z&&e.w===n.w};S.equalsEpsilon=function(e,n,t){return t=t??0,e===n||p(e)&&p(n)&&Math.abs(e.x-n.x)<=t&&Math.abs(e.y-n.y)<=t&&Math.abs(e.z-n.z)<=t&&Math.abs(e.w-n.w)<=t};S.ZERO=Object.freeze(new S(0,0,0,0));S.IDENTITY=Object.freeze(new S(0,0,0,1));S.prototype.clone=function(e){return S.clone(this,e)};S.prototype.equals=function(e){return S.equals(this,e)};S.prototype.equalsEpsilon=function(e,n){return S.equalsEpsilon(this,e,n)};S.prototype.toString=function(){return`(${this.x}, ${this.y}, ${this.z}, ${this.w})`};var Xe=S;var $={},lt={up:{south:"east",north:"west",west:"south",east:"north"},down:{south:"west",north:"east",west:"north",east:"south"},south:{up:"west",down:"east",west:"down",east:"up"},north:{up:"east",down:"west",west:"up",east:"down"},west:{up:"north",down:"south",north:"down",south:"up"},east:{up:"south",down:"north",north:"up",south:"down"}},an={north:[-1,0,0],east:[0,1,0],up:[0,0,1],south:[1,0,0],west:[0,-1,0],down:[0,0,-1]},wt={},Oe={east:new g,north:new g,up:new g,west:new g,south:new g,down:new g},Fe=new g,Le=new g,xe=new g;$.localFrameToFixedFrameGenerator=function(e,n){if(!lt.hasOwnProperty(e)||!lt[e].hasOwnProperty(n))throw new A("firstAxis and secondAxis must be east, north, up, west, south or down.");let t=lt[e][n],o,i=e+n;return p(wt[i])?o=wt[i]:(o=function(r,a,u){if(!p(r))throw new A("origin is required.");if(isNaN(r.x)||isNaN(r.y)||isNaN(r.z))throw new A("origin has a NaN component");if(p(u)||(u=new J),g.equalsEpsilon(r,g.ZERO,R.EPSILON14))g.unpack(an[e],0,Fe),g.unpack(an[n],0,Le),g.unpack(an[t],0,xe);else if(R.equalsEpsilon(r.x,0,R.EPSILON14)&&R.equalsEpsilon(r.y,0,R.EPSILON14)){let d=R.sign(r.z);g.unpack(an[e],0,Fe),e!=="east"&&e!=="west"&&g.multiplyByScalar(Fe,d,Fe),g.unpack(an[n],0,Le),n!=="east"&&n!=="west"&&g.multiplyByScalar(Le,d,Le),g.unpack(an[t],0,xe),t!=="east"&&t!=="west"&&g.multiplyByScalar(xe,d,xe)}else{a=a??Ie.default,a.geodeticSurfaceNormal(r,Oe.up);let d=Oe.up,m=Oe.east;m.x=-r.y,m.y=r.x,m.z=0,g.normalize(m,Oe.east),g.cross(d,m,Oe.north),g.multiplyByScalar(Oe.up,-1,Oe.down),g.multiplyByScalar(Oe.east,-1,Oe.west),g.multiplyByScalar(Oe.north,-1,Oe.south),Fe=Oe[e],Le=Oe[n],xe=Oe[t]}return u[0]=Fe.x,u[1]=Fe.y,u[2]=Fe.z,u[3]=0,u[4]=Le.x,u[5]=Le.y,u[6]=Le.z,u[7]=0,u[8]=xe.x,u[9]=xe.y,u[10]=xe.z,u[11]=0,u[12]=r.x,u[13]=r.y,u[14]=r.z,u[15]=1,u},wt[i]=o),o};$.eastNorthUpToFixedFrame=$.localFrameToFixedFrameGenerator("east","north");$.northEastDownToFixedFrame=$.localFrameToFixedFrameGenerator("north","east");$.northUpEastToFixedFrame=$.localFrameToFixedFrameGenerator("north","up");$.northWestUpToFixedFrame=$.localFrameToFixedFrameGenerator("north","west");var Hr=new Xe,$r=new g(1,1,1),Yr=new J;$.headingPitchRollToFixedFrame=function(e,n,t,o,i){s.typeOf.object("HeadingPitchRoll",n),o=o??$.eastNorthUpToFixedFrame;let r=Xe.fromHeadingPitchRoll(n,Hr),a=J.fromTranslationQuaternionRotationScale(g.ZERO,r,$r,Yr);return i=o(e,t,i),J.multiply(i,a,i)};var Vr=new J,Xr=new B;$.headingPitchRollQuaternion=function(e,n,t,o,i){s.typeOf.object("HeadingPitchRoll",n);let r=$.headingPitchRollToFixedFrame(e,n,t,o,Vr),a=J.getMatrix3(r,Xr);return Xe.fromRotationMatrix(a,i)};var Zr=new g(1,1,1),Jr=new g,Co=new J,Gr=new J,Kr=new B,ei=new Xe;$.fixedFrameToHeadingPitchRoll=function(e,n,t,o){s.defined("transform",e),n=n??Ie.default,t=t??$.eastNorthUpToFixedFrame,p(o)||(o=new xn);let i=J.getTranslation(e,Jr);if(g.equals(i,g.ZERO))return o.heading=0,o.pitch=0,o.roll=0,o;let r=J.inverseTransformation(t(i,n,Co),Co),a=J.setScale(e,Zr,Gr);a=J.setTranslation(a,g.ZERO,a),r=J.multiply(r,a,r);let u=Xe.fromRotationMatrix(J.getMatrix3(r,Kr),ei);return u=Xe.normalize(u,u),xn.fromQuaternion(u,o)};var ni=6*3600+2460+50.54841,ti=8640184812866e-6,oi=.093104,ri=-62e-7,ii=11772758384668e-32,ci=72921158553e-15,si=R.TWO_PI/86400,Xn=new ue;$.computeIcrfToCentralBodyFixedMatrix=function(e,n){let t=$.computeIcrfToFixedMatrix(e,n);return p(t)||(t=$.computeTemeToPseudoFixedMatrix(e,n)),t};$.computeTemeToPseudoFixedMatrix=function(e,n){if(!p(e))throw new A("date is required.");Xn=ue.addSeconds(e,-ue.computeTaiMinusUtc(e),Xn);let t=Xn.dayNumber,o=Xn.secondsOfDay,i,r=t-2451545;o>=43200?i=(r+.5)/ie.DAYS_PER_JULIAN_CENTURY:i=(r-.5)/ie.DAYS_PER_JULIAN_CENTURY;let u=(ni+i*(ti+i*(oi+i*ri)))*si%R.TWO_PI,d=ci+ii*(t-24515455e-1),m=(o+ie.SECONDS_PER_DAY*.5)%ie.SECONDS_PER_DAY,l=u+d*m,w=Math.cos(l),E=Math.sin(l);return p(n)?(n[0]=w,n[1]=-E,n[2]=0,n[3]=E,n[4]=w,n[5]=0,n[6]=0,n[7]=0,n[8]=1,n):new B(w,E,0,-E,w,0,0,0,1)};$.iau2006XysData=new Oo;$.earthOrientationParameters=ho.NONE;var gt=32.184,ai=2451545;$.preloadIcrfFixed=function(e){let n=e.start.dayNumber,t=e.start.secondsOfDay+gt,o=e.stop.dayNumber,i=e.stop.secondsOfDay+gt;return $.iau2006XysData.preload(n,t,o,i)};$.computeIcrfToFixedMatrix=function(e,n){if(!p(e))throw new A("date is required.");p(n)||(n=new B);let t=$.computeFixedToIcrfMatrix(e,n);if(p(t))return B.transpose(t,n)};var fi=32.184,ui=2451545,Zn=new xn,pi=new B,hi=new ue;$.computeMoonFixedToIcrfMatrix=function(e,n){if(!p(e))throw new A("date is required.");p(n)||(n=new B);let t=ue.addSeconds(e,fi,hi),o=ue.totalDays(t)-ui,i=R.toRadians(12.112)-R.toRadians(.052992)*o,r=R.toRadians(24.224)-R.toRadians(.105984)*o,a=R.toRadians(227.645)+R.toRadians(13.012)*o,u=R.toRadians(261.105)+R.toRadians(13.340716)*o,d=R.toRadians(358)+R.toRadians(.9856)*o;return Zn.pitch=R.toRadians(180)-R.toRadians(3.878)*Math.sin(i)-R.toRadians(.12)*Math.sin(r)+R.toRadians(.07)*Math.sin(a)-R.toRadians(.017)*Math.sin(u),Zn.roll=R.toRadians(66.53-90)+R.toRadians(1.543)*Math.cos(i)+R.toRadians(.24)*Math.cos(r)-R.toRadians(.028)*Math.cos(a)+R.toRadians(.007)*Math.cos(u),Zn.heading=R.toRadians(244.375-90)+R.toRadians(13.17635831)*o+R.toRadians(3.558)*Math.sin(i)+R.toRadians(.121)*Math.sin(r)-R.toRadians(.064)*Math.sin(a)+R.toRadians(.016)*Math.sin(u)+R.toRadians(.025)*Math.sin(d),B.fromHeadingPitchRoll(Zn,pi)};$.computeIcrfToMoonFixedMatrix=function(e,n){if(!p(e))throw new A("date is required.");p(n)||(n=new B);let t=$.computeMoonFixedToIcrfMatrix(e,n);if(p(t))return B.transpose(t,n)};var di=new Wn(0,0,0),mi=new hn(0,0,0,0,0,0),bt=new B,Ot=new B;$.computeFixedToIcrfMatrix=function(e,n){if(!p(e))throw new A("date is required.");p(n)||(n=new B);let t=$.earthOrientationParameters.compute(e,mi);if(!p(t))return;let o=e.dayNumber,i=e.secondsOfDay+gt,r=$.iau2006XysData.computeXysRadians(o,i,di);if(!p(r))return;let a=r.x+t.xPoleOffset,u=r.y+t.yPoleOffset,d=1/(1+Math.sqrt(1-a*a-u*u)),m=bt;m[0]=1-d*a*a,m[3]=-d*a*u,m[6]=a,m[1]=-d*a*u,m[4]=1-d*u*u,m[7]=u,m[2]=-a,m[5]=-u,m[8]=1-d*(a*a+u*u);let l=B.fromRotationZ(-r.s,Ot),w=B.multiply(m,l,bt),E=e.dayNumber,T=e.secondsOfDay-ue.computeTaiMinusUtc(e)+t.ut1MinusUtc,M=E-2451545,v=T/ie.SECONDS_PER_DAY,I=.779057273264+v+.00273781191135448*(M+v);I=I%1*R.TWO_PI;let N=B.fromRotationZ(I,Ot),F=B.multiply(w,N,bt),k=Math.cos(t.xPoleWander),x=Math.cos(t.yPoleWander),Q=Math.sin(t.xPoleWander),W=Math.sin(t.yPoleWander),K=o-ai+i/ie.SECONDS_PER_DAY;K/=36525;let oe=-47e-6*K*R.RADIANS_PER_DEGREE/3600,X=Math.cos(oe),te=Math.sin(oe),Z=Ot;return Z[0]=k*X,Z[1]=k*te,Z[2]=Q,Z[3]=-x*te+W*Q*X,Z[4]=x*X+W*Q*te,Z[5]=-W*k,Z[6]=-W*te-x*Q*X,Z[7]=W*X-x*Q*te,Z[8]=x*k,B.multiply(F,Z,n)};var yi=new qe;$.pointToWindowCoordinates=function(e,n,t,o){return o=$.pointToGLWindowCoordinates(e,n,t,o),o.y=2*n[5]-o.y,o};$.pointToGLWindowCoordinates=function(e,n,t,o){if(!p(e))throw new A("modelViewProjectionMatrix is required.");if(!p(n))throw new A("viewportTransformation is required.");if(!p(t))throw new A("point is required.");p(o)||(o=new be);let i=yi;return J.multiplyByVector(e,qe.fromElements(t.x,t.y,t.z,1,i),i),qe.multiplyByScalar(i,1/i.w,i),J.multiplyByVector(n,i,i),be.fromCartesian4(i,o)};var li=new g,wi=new g,bi=new g;$.rotationMatrixFromPositionVelocity=function(e,n,t,o){if(!p(e))throw new A("position is required.");if(!p(n))throw new A("velocity is required.");let i=(t??Ie.default).geodeticSurfaceNormal(e,li),r=g.cross(n,i,wi);g.equalsEpsilon(r,g.ZERO,R.EPSILON6)&&(r=g.clone(g.UNIT_X,r));let a=g.cross(r,n,bi);return g.normalize(a,a),g.cross(n,a,r),g.negate(r,r),g.normalize(r,r),p(o)||(o=new B),o[0]=n.x,o[1]=n.y,o[2]=n.z,o[3]=r.x,o[4]=r.y,o[5]=r.z,o[6]=a.x,o[7]=a.y,o[8]=a.z,o};var vo=new J(0,0,1,0,1,0,0,0,0,1,0,0,0,0,0,1),Ao=new Me,_t=new g,Oi=new g,gi=new B,St=new J,jo=new J;$.basisTo2D=function(e,n,t){if(!p(e))throw new A("projection is required.");if(!p(n))throw new A("matrix is required.");if(!p(t))throw new A("result is required.");let o=J.getTranslation(n,Oi),i=e.ellipsoid,r;if(g.equals(o,g.ZERO))r=g.clone(g.ZERO,_t);else{let l=i.cartesianToCartographic(o,Ao);r=e.project(l,_t),g.fromElements(r.z,r.x,r.y,r)}let a=$.eastNorthUpToFixedFrame(o,i,St),u=J.inverseTransformation(a,jo),d=J.getMatrix3(n,gi),m=J.multiplyByMatrix3(u,d,t);return J.multiply(vo,m,t),J.setTranslation(t,r,t),t};$.ellipsoidTo2DModelMatrix=function(e,n,t){if(!p(e))throw new A("projection is required.");if(!p(n))throw new A("center is required.");if(!p(t))throw new A("result is required.");let o=e.ellipsoid,i=$.eastNorthUpToFixedFrame(n,o,St),r=J.inverseTransformation(i,jo),a=o.cartesianToCartographic(n,Ao),u=e.project(a,_t);g.fromElements(u.z,u.x,u.y,u);let d=J.fromTranslation(u,St);return J.multiply(vo,r,t),J.multiply(d,t,t),t};var Mo=$;function L(e,n,t,o){this.west=e??0,this.south=n??0,this.east=t??0,this.north=o??0}Object.defineProperties(L.prototype,{width:{get:function(){return L.computeWidth(this)}},height:{get:function(){return L.computeHeight(this)}}});L.packedLength=4;L.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=t??0,n[t++]=e.west,n[t++]=e.south,n[t++]=e.east,n[t]=e.north,n};L.unpack=function(e,n,t){return s.defined("array",e),n=n??0,p(t)||(t=new L),t.west=e[n++],t.south=e[n++],t.east=e[n++],t.north=e[n],t};L.computeWidth=function(e){s.typeOf.object("rectangle",e);let n=e.east,t=e.west;return n<t&&(n+=R.TWO_PI),n-t};L.computeHeight=function(e){return s.typeOf.object("rectangle",e),e.north-e.south};L.fromDegrees=function(e,n,t,o,i){return e=R.toRadians(e??0),n=R.toRadians(n??0),t=R.toRadians(t??0),o=R.toRadians(o??0),p(i)?(i.west=e,i.south=n,i.east=t,i.north=o,i):new L(e,n,t,o)};L.fromRadians=function(e,n,t,o,i){return p(i)?(i.west=e??0,i.south=n??0,i.east=t??0,i.north=o??0,i):new L(e,n,t,o)};L.fromCartographicArray=function(e,n){s.defined("cartographics",e);let t=Number.MAX_VALUE,o=-Number.MAX_VALUE,i=Number.MAX_VALUE,r=-Number.MAX_VALUE,a=Number.MAX_VALUE,u=-Number.MAX_VALUE;for(let d=0,m=e.length;d<m;d++){let l=e[d];t=Math.min(t,l.longitude),o=Math.max(o,l.longitude),a=Math.min(a,l.latitude),u=Math.max(u,l.latitude);let w=l.longitude>=0?l.longitude:l.longitude+R.TWO_PI;i=Math.min(i,w),r=Math.max(r,w)}return o-t>r-i&&(t=i,o=r,o>R.PI&&(o=o-R.TWO_PI),t>R.PI&&(t=t-R.TWO_PI)),p(n)?(n.west=t,n.south=a,n.east=o,n.north=u,n):new L(t,a,o,u)};L.fromCartesianArray=function(e,n,t){s.defined("cartesians",e),n=n??Ie.default;let o=Number.MAX_VALUE,i=-Number.MAX_VALUE,r=Number.MAX_VALUE,a=-Number.MAX_VALUE,u=Number.MAX_VALUE,d=-Number.MAX_VALUE;for(let m=0,l=e.length;m<l;m++){let w=n.cartesianToCartographic(e[m]);o=Math.min(o,w.longitude),i=Math.max(i,w.longitude),u=Math.min(u,w.latitude),d=Math.max(d,w.latitude);let E=w.longitude>=0?w.longitude:w.longitude+R.TWO_PI;r=Math.min(r,E),a=Math.max(a,E)}return i-o>a-r&&(o=r,i=a,i>R.PI&&(i=i-R.TWO_PI),o>R.PI&&(o=o-R.TWO_PI)),p(t)?(t.west=o,t.south=u,t.east=i,t.north=d,t):new L(o,u,i,d)};var _i=new g,Si=new g,Ri=new g,Ei=new g,Ti=new g,Rt=new Array(5);for(let e=0;e<Rt.length;++e)Rt[e]=new g;L.fromBoundingSphere=function(e,n,t){s.typeOf.object("boundingSphere",e);let o=e.center,i=e.radius;if(p(n)||(n=Ie.default),p(t)||(t=new L),g.equals(o,g.ZERO))return L.clone(L.MAX_VALUE,t),t;let r=Mo.eastNorthUpToFixedFrame(o,n,_i),a=J.multiplyByPointAsVector(r,g.UNIT_X,Si);g.normalize(a,a);let u=J.multiplyByPointAsVector(r,g.UNIT_Y,Ri);g.normalize(u,u),g.multiplyByScalar(u,i,u),g.multiplyByScalar(a,i,a);let d=g.negate(u,Ti),m=g.negate(a,Ei),l=Rt,w=l[0];return g.add(o,u,w),w=l[1],g.add(o,m,w),w=l[2],g.add(o,d,w),w=l[3],g.add(o,a,w),l[4]=o,L.fromCartesianArray(l,n,t)};L.clone=function(e,n){if(p(e))return p(n)?(n.west=e.west,n.south=e.south,n.east=e.east,n.north=e.north,n):new L(e.west,e.south,e.east,e.north)};L.equalsEpsilon=function(e,n,t){return t=t??0,e===n||p(e)&&p(n)&&Math.abs(e.west-n.west)<=t&&Math.abs(e.south-n.south)<=t&&Math.abs(e.east-n.east)<=t&&Math.abs(e.north-n.north)<=t};L.prototype.clone=function(e){return L.clone(this,e)};L.prototype.equals=function(e){return L.equals(this,e)};L.equals=function(e,n){return e===n||p(e)&&p(n)&&e.west===n.west&&e.south===n.south&&e.east===n.east&&e.north===n.north};L.prototype.equalsEpsilon=function(e,n){return L.equalsEpsilon(this,e,n)};L._validate=function(e){s.typeOf.object("rectangle",e);let n=e.north;s.typeOf.number.greaterThanOrEquals("north",n,-R.PI_OVER_TWO),s.typeOf.number.lessThanOrEquals("north",n,R.PI_OVER_TWO);let t=e.south;s.typeOf.number.greaterThanOrEquals("south",t,-R.PI_OVER_TWO),s.typeOf.number.lessThanOrEquals("south",t,R.PI_OVER_TWO);let o=e.west;s.typeOf.number.greaterThanOrEquals("west",o,-Math.PI),s.typeOf.number.lessThanOrEquals("west",o,Math.PI);let i=e.east;s.typeOf.number.greaterThanOrEquals("east",i,-Math.PI),s.typeOf.number.lessThanOrEquals("east",i,Math.PI)};L.southwest=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.west,n.latitude=e.south,n.height=0,n):new Me(e.west,e.south)};L.northwest=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.west,n.latitude=e.north,n.height=0,n):new Me(e.west,e.north)};L.northeast=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.east,n.latitude=e.north,n.height=0,n):new Me(e.east,e.north)};L.southeast=function(e,n){return s.typeOf.object("rectangle",e),p(n)?(n.longitude=e.east,n.latitude=e.south,n.height=0,n):new Me(e.east,e.south)};L.center=function(e,n){s.typeOf.object("rectangle",e);let t=e.east,o=e.west;t<o&&(t+=R.TWO_PI);let i=R.negativePiToPi((o+t)*.5),r=(e.south+e.north)*.5;return p(n)?(n.longitude=i,n.latitude=r,n.height=0,n):new Me(i,r)};L.intersection=function(e,n,t){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",n);let o=e.east,i=e.west,r=n.east,a=n.west;o<i&&r>0?o+=R.TWO_PI:r<a&&o>0&&(r+=R.TWO_PI),o<i&&a<0?a+=R.TWO_PI:r<a&&i<0&&(i+=R.TWO_PI);let u=R.negativePiToPi(Math.max(i,a)),d=R.negativePiToPi(Math.min(o,r));if((e.west<e.east||n.west<n.east)&&d<=u)return;let m=Math.max(e.south,n.south),l=Math.min(e.north,n.north);if(!(m>=l))return p(t)?(t.west=u,t.south=m,t.east=d,t.north=l,t):new L(u,m,d,l)};L.simpleIntersection=function(e,n,t){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",n);let o=Math.max(e.west,n.west),i=Math.max(e.south,n.south),r=Math.min(e.east,n.east),a=Math.min(e.north,n.north);if(!(i>=a||o>=r))return p(t)?(t.west=o,t.south=i,t.east=r,t.north=a,t):new L(o,i,r,a)};L.union=function(e,n,t){s.typeOf.object("rectangle",e),s.typeOf.object("otherRectangle",n),p(t)||(t=new L);let o=e.east,i=e.west,r=n.east,a=n.west;o<i&&r>0?o+=R.TWO_PI:r<a&&o>0&&(r+=R.TWO_PI),o<i&&a<0?a+=R.TWO_PI:r<a&&i<0&&(i+=R.TWO_PI);let u=R.negativePiToPi(Math.min(i,a)),d=R.negativePiToPi(Math.max(o,r));return t.west=u,t.south=Math.min(e.south,n.south),t.east=d,t.north=Math.max(e.north,n.north),t};L.expand=function(e,n,t){return s.typeOf.object("rectangle",e),s.typeOf.object("cartographic",n),p(t)||(t=new L),t.west=Math.min(e.west,n.longitude),t.south=Math.min(e.south,n.latitude),t.east=Math.max(e.east,n.longitude),t.north=Math.max(e.north,n.latitude),t};L.contains=function(e,n){s.typeOf.object("rectangle",e),s.typeOf.object("cartographic",n);let t=n.longitude,o=n.latitude,i=e.west,r=e.east;return r<i&&(r+=R.TWO_PI,t<0&&(t+=R.TWO_PI)),(t>i||R.equalsEpsilon(t,i,R.EPSILON14))&&(t<r||R.equalsEpsilon(t,r,R.EPSILON14))&&o>=e.south&&o<=e.north};var Ci=new Me;L.subsample=function(e,n,t,o){s.typeOf.object("rectangle",e),n=n??Ie.default,t=t??0,p(o)||(o=[]);let i=0,r=e.north,a=e.south,u=e.east,d=e.west,m=Ci;m.height=t,m.longitude=d,m.latitude=r,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.longitude=u,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.latitude=a,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.longitude=d,o[i]=n.cartographicToCartesian(m,o[i]),i++,r<0?m.latitude=r:a>0?m.latitude=a:m.latitude=0;for(let l=1;l<8;++l)m.longitude=-Math.PI+l*R.PI_OVER_TWO,L.contains(e,m)&&(o[i]=n.cartographicToCartesian(m,o[i]),i++);return m.latitude===0&&(m.longitude=d,o[i]=n.cartographicToCartesian(m,o[i]),i++,m.longitude=u,o[i]=n.cartographicToCartesian(m,o[i]),i++),o.length=i,o};L.subsection=function(e,n,t,o,i,r){if(s.typeOf.object("rectangle",e),s.typeOf.number.greaterThanOrEquals("westLerp",n,0),s.typeOf.number.lessThanOrEquals("westLerp",n,1),s.typeOf.number.greaterThanOrEquals("southLerp",t,0),s.typeOf.number.lessThanOrEquals("southLerp",t,1),s.typeOf.number.greaterThanOrEquals("eastLerp",o,0),s.typeOf.number.lessThanOrEquals("eastLerp",o,1),s.typeOf.number.greaterThanOrEquals("northLerp",i,0),s.typeOf.number.lessThanOrEquals("northLerp",i,1),s.typeOf.number.lessThanOrEquals("westLerp",n,o),s.typeOf.number.lessThanOrEquals("southLerp",t,i),p(r)||(r=new L),e.west<=e.east){let u=e.east-e.west;r.west=e.west+n*u,r.east=e.west+o*u}else{let u=R.TWO_PI+e.east-e.west;r.west=R.negativePiToPi(e.west+n*u),r.east=R.negativePiToPi(e.west+o*u)}let a=e.north-e.south;return r.south=e.south+t*a,r.north=e.south+i*a,n===1&&(r.west=e.east),o===1&&(r.east=e.east),t===1&&(r.south=e.north),i===1&&(r.north=e.north),r};L.MAX_VALUE=Object.freeze(new L(-Math.PI,-R.PI_OVER_TWO,Math.PI,R.PI_OVER_TWO));var nf=L;function q(e,n,t,o){this[0]=e??0,this[1]=t??0,this[2]=n??0,this[3]=o??0}q.packedLength=4;q.pack=function(e,n,t){return s.typeOf.object("value",e),s.defined("array",n),t=t??0,n[t++]=e[0],n[t++]=e[1],n[t++]=e[2],n[t++]=e[3],n};q.unpack=function(e,n,t){return s.defined("array",e),n=n??0,p(t)||(t=new q),t[0]=e[n++],t[1]=e[n++],t[2]=e[n++],t[3]=e[n++],t};q.packArray=function(e,n){s.defined("array",e);let t=e.length,o=t*4;if(!p(n))n=new Array(o);else{if(!Array.isArray(n)&&n.length!==o)throw new A("If result is a typed array, it must have exactly array.length * 4 elements");n.length!==o&&(n.length=o)}for(let i=0;i<t;++i)q.pack(e[i],n,i*4);return n};q.unpackArray=function(e,n){if(s.defined("array",e),s.typeOf.number.greaterThanOrEquals("array.length",e.length,4),e.length%4!==0)throw new A("array length must be a multiple of 4.");let t=e.length;p(n)?n.length=t/4:n=new Array(t/4);for(let o=0;o<t;o+=4){let i=o/4;n[i]=q.unpack(e,o,n[i])}return n};q.clone=function(e,n){if(p(e))return p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n):new q(e[0],e[2],e[1],e[3])};q.fromArray=q.unpack;q.fromColumnMajorArray=function(e,n){return s.defined("values",e),q.clone(e,n)};q.fromRowMajorArray=function(e,n){return s.defined("values",e),p(n)?(n[0]=e[0],n[1]=e[2],n[2]=e[1],n[3]=e[3],n):new q(e[0],e[1],e[2],e[3])};q.fromScale=function(e,n){return s.typeOf.object("scale",e),p(n)?(n[0]=e.x,n[1]=0,n[2]=0,n[3]=e.y,n):new q(e.x,0,0,e.y)};q.fromUniformScale=function(e,n){return s.typeOf.number("scale",e),p(n)?(n[0]=e,n[1]=0,n[2]=0,n[3]=e,n):new q(e,0,0,e)};q.fromRotation=function(e,n){s.typeOf.number("angle",e);let t=Math.cos(e),o=Math.sin(e);return p(n)?(n[0]=t,n[1]=o,n[2]=-o,n[3]=t,n):new q(t,-o,o,t)};q.toArray=function(e,n){return s.typeOf.object("matrix",e),p(n)?(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n):[e[0],e[1],e[2],e[3]]};q.getElementIndex=function(e,n){return s.typeOf.number.greaterThanOrEquals("row",n,0),s.typeOf.number.lessThanOrEquals("row",n,1),s.typeOf.number.greaterThanOrEquals("column",e,0),s.typeOf.number.lessThanOrEquals("column",e,1),e*2+n};q.getColumn=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("result",t);let o=n*2,i=e[o],r=e[o+1];return t.x=i,t.y=r,t};q.setColumn=function(e,n,t,o){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=q.clone(e,o);let i=n*2;return o[i]=t.x,o[i+1]=t.y,o};q.getRow=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("result",t);let o=e[n],i=e[n+2];return t.x=o,t.y=i,t};q.setRow=function(e,n,t,o){return s.typeOf.object("matrix",e),s.typeOf.number.greaterThanOrEquals("index",n,0),s.typeOf.number.lessThanOrEquals("index",n,1),s.typeOf.object("cartesian",t),s.typeOf.object("result",o),o=q.clone(e,o),o[n]=t.x,o[n+2]=t.y,o};var vi=new be;q.setScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t);let o=q.getScale(e,vi),i=n.x/o.x,r=n.y/o.y;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*r,t[3]=e[3]*r,t};var Ai=new be;q.setUniformScale=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t);let o=q.getScale(e,Ai),i=n/o.x,r=n/o.y;return t[0]=e[0]*i,t[1]=e[1]*i,t[2]=e[2]*r,t[3]=e[3]*r,t};var Po=new be;q.getScale=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n.x=be.magnitude(be.fromElements(e[0],e[1],Po)),n.y=be.magnitude(be.fromElements(e[2],e[3],Po)),n};var Uo=new be;q.getMaximumScale=function(e){return q.getScale(e,Uo),be.maximumComponent(Uo)};var ji=new be;q.setRotation=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("result",t);let o=q.getScale(e,ji);return t[0]=n[0]*o.x,t[1]=n[1]*o.x,t[2]=n[2]*o.y,t[3]=n[3]*o.y,t};var Mi=new be;q.getRotation=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=q.getScale(e,Mi);return n[0]=e[0]/t.x,n[1]=e[1]/t.x,n[2]=e[2]/t.y,n[3]=e[3]/t.y,n};q.multiply=function(e,n,t){s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t);let o=e[0]*n[0]+e[2]*n[1],i=e[0]*n[2]+e[2]*n[3],r=e[1]*n[0]+e[3]*n[1],a=e[1]*n[2]+e[3]*n[3];return t[0]=o,t[1]=r,t[2]=i,t[3]=a,t};q.add=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]+n[0],t[1]=e[1]+n[1],t[2]=e[2]+n[2],t[3]=e[3]+n[3],t};q.subtract=function(e,n,t){return s.typeOf.object("left",e),s.typeOf.object("right",n),s.typeOf.object("result",t),t[0]=e[0]-n[0],t[1]=e[1]-n[1],t[2]=e[2]-n[2],t[3]=e[3]-n[3],t};q.multiplyByVector=function(e,n,t){s.typeOf.object("matrix",e),s.typeOf.object("cartesian",n),s.typeOf.object("result",t);let o=e[0]*n.x+e[2]*n.y,i=e[1]*n.x+e[3]*n.y;return t.x=o,t.y=i,t};q.multiplyByScalar=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scalar",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t};q.multiplyByScale=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.object("scale",n),s.typeOf.object("result",t),t[0]=e[0]*n.x,t[1]=e[1]*n.x,t[2]=e[2]*n.y,t[3]=e[3]*n.y,t};q.multiplyByUniformScale=function(e,n,t){return s.typeOf.object("matrix",e),s.typeOf.number("scale",n),s.typeOf.object("result",t),t[0]=e[0]*n,t[1]=e[1]*n,t[2]=e[2]*n,t[3]=e[3]*n,t};q.negate=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n};q.transpose=function(e,n){s.typeOf.object("matrix",e),s.typeOf.object("result",n);let t=e[0],o=e[2],i=e[1],r=e[3];return n[0]=t,n[1]=o,n[2]=i,n[3]=r,n};q.abs=function(e,n){return s.typeOf.object("matrix",e),s.typeOf.object("result",n),n[0]=Math.abs(e[0]),n[1]=Math.abs(e[1]),n[2]=Math.abs(e[2]),n[3]=Math.abs(e[3]),n};q.equals=function(e,n){return e===n||p(e)&&p(n)&&e[0]===n[0]&&e[1]===n[1]&&e[2]===n[2]&&e[3]===n[3]};q.equalsArray=function(e,n,t){return e[0]===n[t]&&e[1]===n[t+1]&&e[2]===n[t+2]&&e[3]===n[t+3]};q.equalsEpsilon=function(e,n,t){return t=t??0,e===n||p(e)&&p(n)&&Math.abs(e[0]-n[0])<=t&&Math.abs(e[1]-n[1])<=t&&Math.abs(e[2]-n[2])<=t&&Math.abs(e[3]-n[3])<=t};q.IDENTITY=Object.freeze(new q(1,0,0,1));q.ZERO=Object.freeze(new q(0,0,0,0));q.COLUMN0ROW0=0;q.COLUMN0ROW1=1;q.COLUMN1ROW0=2;q.COLUMN1ROW1=3;Object.defineProperties(q.prototype,{length:{get:function(){return q.packedLength}}});q.prototype.clone=function(e){return q.clone(this,e)};q.prototype.equals=function(e){return q.equals(this,e)};q.prototype.equalsEpsilon=function(e,n){return q.equalsEpsilon(this,e,n)};q.prototype.toString=function(){return`(${this[0]}, ${this[2]})
(${this[1]}, ${this[3]})`};var af=q;export{qe as a,J as b,De as c,ke as d,bo as e,Xe as f,Mo as g,nf as h,af as i};
