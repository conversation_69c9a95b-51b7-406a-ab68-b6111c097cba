const fs = require('fs');
const csv = require('csv-parser');

console.log('🔍 检查CSV文件内容...');

// 检查文件是否存在
const filePath = './data/news_translate.csv';
if (!fs.existsSync(filePath)) {
  console.error('❌ 文件不存在:', filePath);
  console.log('请确认文件路径是否正确');
  process.exit(1);
}

const results = [];
fs.createReadStream(filePath)
  .pipe(csv({mapHeaders: ({ header }) => header.trim().replace(/^\uFEFF/, '')}))  // 去掉BOM和空格
  .on('data', (data) => {
    results.push(data);
    if (results.length <= 2) { // 只显示前2条
      console.log(`\n=== 第 ${results.length} 条记录 ===`);
      
      // 显示所有字段名
      console.log('所有字段名:', Object.keys(data));
      
      // 显示每个字段的内容
      console.log('title:', data.title ? data.title.substring(0, 100) : '空或无');
      console.log('original_link:', data.original_link ? data.original_link.substring(0, 100) : '空或无');
      console.log('published:', data.published);
      console.log('source:', data.source);
      console.log('source_translated:', data.source_translated);
      console.log('country:', data.country);
      console.log('country_translated:', data.country_translated);
      console.log('title_translated:', data.title_translated ? data.title_translated.substring(0, 100) : '空或无');
      console.log('text:', data.text ? data.text.substring(0, 200) : '空或无');
      console.log('text_translated:', data.text_translated ? data.text_translated.substring(0, 200) : '空或无');
    }
  })
  .on('end', () => {
    console.log(`\n📊 CSV文件总共有 ${results.length} 条记录`);
  })
  .on('error', (error) => {
    console.error('❌ 读取失败:', error.message);
  });