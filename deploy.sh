#!/bin/bash

# =============================================
# 新闻可视化系统 - 一键部署脚本
# =============================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查系统依赖..."

    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi

    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi

    log_success "依赖检查完成"
}

# 检查环境配置
check_env() {
    log_info "检查环境配置..."

    if [ ! -f ".env" ]; then
        log_warning ".env 文件不存在，从模板创建..."
        cp .env.example .env
        log_warning "请编辑 .env 文件，配置数据库密码和百度翻译API密钥"
        log_warning "配置完成后重新运行此脚本"
        exit 1
    fi

    # 检查关键配置
    source .env
    if [ "$BAIDU_APP_ID" = "your_baidu_app_id" ] || [ "$BAIDU_SECRET_KEY" = "your_baidu_secret_key" ]; then
        log_warning "请在 .env 文件中配置百度翻译API密钥"
        log_warning "配置完成后重新运行此脚本"
        exit 1
    fi

    log_success "环境配置检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."

    mkdir -p backend/logs
    mkdir -p backend/data
    mkdir -p docker/ssl

    log_success "目录创建完成"
}

# 构建和启动服务
deploy_services() {
    log_info "开始构建和部署服务..."

    # 停止现有服务
    log_info "停止现有服务..."
    docker-compose down --remove-orphans

    # 构建镜像
    log_info "构建 Docker 镜像..."
    docker-compose build --no-cache

    # 启动服务
    log_info "启动服务..."
    docker-compose up -d

    log_success "服务部署完成"
}

# 等待服务启动
wait_for_services() {
    log_info "等待服务启动..."

    # 等待数据库启动
    log_info "等待数据库启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if docker-compose exec -T mysql mysqladmin ping -h localhost --silent; then
            log_success "数据库已启动"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done

    if [ $timeout -le 0 ]; then
        log_error "数据库启动超时"
        exit 1
    fi

    # 等待后端API启动
    log_info "等待后端API启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:3001/api/status &> /dev/null; then
            log_success "后端API已启动"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done

    if [ $timeout -le 0 ]; then
        log_error "后端API启动超时"
        exit 1
    fi

    # 等待前端启动
    log_info "等待前端启动..."
    timeout=60
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost/health &> /dev/null; then
            log_success "前端已启动"
            break
        fi
        sleep 2
        timeout=$((timeout-2))
    done

    if [ $timeout -le 0 ]; then
        log_error "前端启动超时"
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo ""
    echo "=========================================="
    echo "  新闻可视化系统部署信息"
    echo "=========================================="
    echo "前端地址: http://localhost"
    echo "后端API: http://localhost:3001"
    echo "数据库: localhost:3306"
    echo ""
    echo "管理命令:"
    echo "  查看日志: docker-compose logs -f [service_name]"
    echo "  重启服务: docker-compose restart [service_name]"
    echo "  停止服务: docker-compose down"
    echo "  查看状态: docker-compose ps"
    echo ""
    echo "服务状态:"
    docker-compose ps
    echo "=========================================="
}

# 主函数
main() {
    echo "=========================================="
    echo "  新闻可视化系统 - 一键部署脚本"
    echo "=========================================="

    check_dependencies
    check_env
    create_directories
    deploy_services
    wait_for_services
    show_deployment_info
}

# 运行主函数
main "$@"