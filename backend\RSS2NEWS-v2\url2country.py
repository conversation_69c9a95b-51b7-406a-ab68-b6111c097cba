# url2country.py
import pandas as pd
from urllib.parse import urlparse
import os
import mysql.connector
from functools import lru_cache

# ====== 1. 数据库连接配置 ======
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',  # 替换为你的数据库密码
    'database': 'news_system',
    'charset': 'utf8mb4'
}

# ====== 2. 域名映射缓存 ======
domain_cache = {}

def load_domain_mappings():
    """从数据库加载域名映射到缓存"""
    global domain_cache
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 修正SQL查询，使用正确的表结构
        cursor.execute("""
            SELECT dm.domain, c.name_zh, c.name_en 
            FROM domain_mappings dm
            JOIN countries c ON dm.country_code = c.code
        """)
        
        domain_cache.clear()
        for domain, country_zh, country_en in cursor.fetchall():
            domain_cache[domain] = {
                'zh': country_zh,
                'en': country_en
            }
        
        print(f"✅ 从数据库加载了 {len(domain_cache)} 个域名映射")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"⚠️ 数据库连接失败，使用备用映射: {e}")
        # 数据库连接失败时使用原来的硬编码映射作为备用方案
        load_fallback_mappings()

def load_fallback_mappings():
    """备用的硬编码域名映射"""
    global domain_cache
def load_fallback_mappings():
    """备用的硬编码域名映射"""
    global domain_cache
    fallback_mappings = {
        # 中国
        "scmp.com": {"zh": "中国", "en": "China"},
        "globaltimes.cn": {"zh": "中国", "en": "China"},
        "xinhuanet.com": {"zh": "中国", "en": "China"},
        "english.news.cn": {"zh": "中国", "en": "China"},
        "cgtn.com": {"zh": "中国", "en": "China"},
        "chinausfocus.com": {"zh": "中国", "en": "China"},
        "pekingnology.com": {"zh": "中国", "en": "China"},
        "asiatimes.com": {"zh": "中国", "en": "China"},
        "ecns.cn": {"zh": "中国", "en": "China"},
        # 美国
        "cnn.com": {"zh": "美国", "en": "United States of America"},
        "nytimes.com": {"zh": "美国", "en": "United States of America"},
        "wsj.com": {"zh": "美国", "en": "United States of America"},
        "businessinsider.com": {"zh": "美国", "en": "United States of America"},
        "apnews.com": {"zh": "美国", "en": "United States of America"},
        "axios.com": {"zh": "美国", "en": "United States of America"},
        "newsweek.com": {"zh": "美国", "en": "United States of America"},
        "bloomberg.com": {"zh": "美国", "en": "United States of America"},
        "cbsnews.com": {"zh": "美国", "en": "United States of America"},
        "nbcnews.com": {"zh": "美国", "en": "United States of America"},
        "pbs.org": {"zh": "美国", "en": "United States of America"},
        "militarytimes.com": {"zh": "美国", "en": "United States of America"},
        "defensenews.com": {"zh": "美国", "en": "United States of America"},
        "usni.org": {"zh": "美国", "en": "United States of America"},
        "realcleardefense.com": {"zh": "美国", "en": "United States of America"},
        "warontherocks.com": {"zh": "美国", "en": "United States of America"},
        "bushcenter.org": {"zh": "美国", "en": "United States of America"},
        "csis.org": {"zh": "美国", "en": "United States of America"},
        "legion.org": {"zh": "美国", "en": "United States of America"},
        "stimson.org": {"zh": "美国", "en": "United States of America"},
        "state.gov": {"zh": "美国", "en": "United States of America"},
        "cpf.navy.mil": {"zh": "美国", "en": "United States of America"},
        "airuniversity.af.edu": {"zh": "美国", "en": "United States of America"},
        "asiasociety.org": {"zh": "美国", "en": "United States of America"},
        "oilprice.com": {"zh": "美国", "en": "United States of America"},
        "ipdefenseforum.com": {"zh": "美国", "en": "United States of America"},
        "thediplomat.com": {"zh": "美国", "en": "United States of America"},
        # 英国
        "bbc.com": {"zh": "英国", "en": "United Kingdom"},
        "theguardian.com": {"zh": "英国", "en": "United Kingdom"},
        "telegraph.co.uk": {"zh": "英国", "en": "United Kingdom"},
        "breakingdefense.com": {"zh": "英国", "en": "United Kingdom"},
        "reuters.com": {"zh": "英国", "en": "United Kingdom"},
        "nature.com": {"zh": "英国", "en": "United Kingdom"},
        # 欧洲/其他
        "dw.com": {"zh": "德国", "en": "Germany"},
        "gcaptain.com": {"zh": "德国", "en": "Germany"},
        "eurasiareview.com": {"zh": "西班牙", "en": "Spain"},
        "crisisgroup.org": {"zh": "比利时", "en": "Belgium"},
        "navalnews.com": {"zh": "法国", "en": "France"},
        "sciencedirect.com": {"zh": "荷兰", "en": "Netherlands"},
        # 亚洲
        "globaltaiwan.org": {"zh": "台湾", "en": "Taiwan"},
        "chosun.com": {"zh": "韩国", "en": "South Korea"},
        "kyodonews.net": {"zh": "日本", "en": "Japan"},
        "asahi.com": {"zh": "日本", "en": "Japan"},
        "nikkei.com": {"zh": "日本", "en": "Japan"},
        "japantimes.co.jp": {"zh": "日本", "en": "Japan"},
        "nationthailand.com": {"zh": "泰国", "en": "Thailand"},
        "inquirer.net": {"zh": "菲律宾", "en": "Philippines"},
        "malaymail.com": {"zh": "马来西亚", "en": "Malaysia"},
        "thestar.com.my": {"zh": "马来西亚", "en": "Malaysia"},
        "straitstimes.com": {"zh": "新加坡", "en": "Singapore"},
        "editorji.com": {"zh": "印度", "en": "India"},
        "economictimes.com": {"zh": "印度", "en": "India"},
        "devdiscourse.com": {"zh": "印度", "en": "India"},
        "eurasiantimes.com": {"zh": "印度", "en": "India"},
        "aljazeera.com": {"zh": "卡塔尔", "en": "Qatar"},
        # 其他
        "aa.com.tr": {"zh": "土耳其", "en": "Turkey"},
        "anadoluajansi.com.tr": {"zh": "土耳其", "en": "Turkey"},
        "bairdmaritime.com": {"zh": "澳大利亚", "en": "Australia"},
        "aspistrategist.org.au": {"zh": "澳大利亚", "en": "Australia"},
        "abc.net.au": {"zh": "澳大利亚", "en": "Australia"},
        "internationalaffairs.org.au": {"zh": "澳大利亚", "en": "Australia"},
        "lowyinstitute.org": {"zh": "澳大利亚", "en": "Australia"},
        "eastasiaforum.org": {"zh": "澳大利亚", "en": "Australia"},
        "opportunitydesk.org": {"zh": "尼日利亚", "en": "Nigeria"},
        "thespec.com": {"zh": "加拿大", "en": "Canada"},
        "geopoliticalmonitor.com": {"zh": "加拿大", "en": "Canada"},
    }
    domain_cache.update(fallback_mappings)
    print(f"✅ 加载了 {len(fallback_mappings)} 个备用域名映射")

# ====== 3. 映射函数 ======
def map_url_to_country(url, language='zh'):
    """将 URL 映射为国家（支持中英文）"""
    try:
        domain = urlparse(str(url)).netloc.lower()
        if domain.startswith("www."):
            domain = domain[4:]
        
        # 遍历缓存中的域名
        for cached_domain, country_info in domain_cache.items():
            if cached_domain in domain:
                return country_info.get(language, country_info.get('zh', '未知'))
        
        return "未知" if language == 'zh' else "Unknown"
    except Exception:
        return "无效URL" if language == 'zh' else "Invalid URL"

def add_country_column(csv_file="south_china_sea_news.csv"):
    """在 CSV 中添加 country 和 country_translated 列，并保存回原文件"""
    if not os.path.exists(csv_file):
        raise FileNotFoundError(f"文件不存在: {csv_file}")

    # 确保域名映射已加载
    if not domain_cache:
        load_domain_mappings()

    df = pd.read_csv(csv_file)

    if "original_link" not in df.columns:
        raise ValueError("CSV 中没有 'original_link' 列，请先运行 getOriginalLinks.py")

    # 添加列（如果不存在）
    if "country" not in df.columns:
        df["country"] = ""
    if "country_translated" not in df.columns:
        df["country_translated"] = ""

    # 只更新 original_link 非空且 country 为空的行（增量更新）
    mask = df["original_link"].notna() & (df["original_link"] != "") & ((df["country"].isna()) | (df["country"] == ""))
    
    # 映射英文和中文国家名
    df.loc[mask, "country"] = df.loc[mask, "original_link"].apply(lambda url: map_url_to_country(url, 'en'))
    df.loc[mask, "country_translated"] = df.loc[mask, "original_link"].apply(lambda url: map_url_to_country(url, 'zh'))

    # 保存回原 CSV
    df.to_csv(csv_file, index=False, encoding="utf-8-sig")
    print(f"✅ 已完成国家映射并保存回 {csv_file}")
    # print(f"   - country: 英文国家名")
    # print(f"   - country_translated: 中文国家名")

# ====== 4. API 函数（供后端调用） ======
def get_country_from_url(url, language='zh'):
    """供后端 API 调用的函数"""
    if not domain_cache:
        load_domain_mappings()
    return map_url_to_country(url, language)

# ====== 5. 支持单独运行 ====== 
if __name__ == "__main__":
    input_file = "south_china_sea_news.csv"
    add_country_column(input_file)
