import pandas as pd
import os
import datetime
import subprocess

def import_to_database_incremental(csv_file):
    """增量导入数据到数据库"""
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        
        # 筛选需要导入的数据（db_status为pending的记录）
        pending_data = df[df['db_status'] == 'pending'].copy()
        
        if pending_data.empty:
            print("没有新数据需要导入数据库")
            return True
        
        print(f"发现 {len(pending_data)} 条待导入数据")
        
        # 创建临时CSV文件，只包含待导入的数据
        temp_csv = "temp_import.csv"
        pending_data.to_csv(temp_csv, index=False, encoding="utf-8")
        
        # 移动临时文件到backend/data目录
        backend_data_dir = "/app/data"  # Docker容器内的路径
        os.makedirs(backend_data_dir, exist_ok=True)

        target_path = os.path.join(backend_data_dir, "news_translate.csv")

        import shutil
        shutil.copy2(temp_csv, target_path)
        print(f"临时数据文件已复制到: {target_path}")

        # 运行数据库导入脚本
        result = subprocess.run([
            "node",
            "/app/import-news.js"  # Docker容器内的路径
        ], capture_output=True, text=True, cwd="/app")
        
        if result.returncode == 0:
            print("数据库导入成功")
            
            # 更新CSV文件中的db_status
            success_count = update_db_status_in_csv(csv_file, pending_data.index.tolist(), "imported")
            print(f"已更新 {success_count} 条记录的数据库状态")
            
            # 清理临时文件
            if os.path.exists(temp_csv):
                os.remove(temp_csv)
            
            return True
        else:
            print("数据库导入失败")
            print("错误输出:", result.stderr)
            
            # 标记为失败状态
            update_db_status_in_csv(csv_file, pending_data.index.tolist(), "failed")
            return False
            
    except Exception as e:
        print(f"数据库导入异常: {e}")
        return False

def update_db_status_in_csv(csv_file, indices, status):
    """更新CSV文件中指定行的数据库状态"""
    try:
        df = pd.read_csv(csv_file)
        
        # 更新指定索引的db_status
        df.loc[indices, 'db_status'] = status
        
        # 保存回CSV
        df.to_csv(csv_file, index=False, encoding="utf-8")
        
        return len(indices)
    except Exception as e:
        print(f"更新CSV状态失败: {e}")
        return 0

def check_database_status(csv_file):
    """检查数据库导入状态统计"""
    try:
        df = pd.read_csv(csv_file)
        
        if 'db_status' not in df.columns:
            print("CSV文件中没有db_status列")
            return
        
        status_counts = df['db_status'].value_counts()
        print("\n数据库导入状态统计:")
        for status, count in status_counts.items():
            print(f"  {status}: {count} 条")
        
        return status_counts
        
    except Exception as e:
        print(f"检查状态失败: {e}")