/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.132
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as Q}from"./chunk-ARG42DC4.js";import{a as N}from"./chunk-GBYLG25F.js";import{a as _}from"./chunk-CYCB63OH.js";import{a as K}from"./chunk-OFUUQVMR.js";import{a as H}from"./chunk-A56XVLQR.js";import{b as J,c as W,d as L}from"./chunk-RCV6KWXS.js";import{d as j}from"./chunk-4IW2T6GF.js";import{a as S}from"./chunk-AU7IKHOH.js";import{a as b,c as E,f as Z}from"./chunk-64RSHJUE.js";import{a as Y}from"./chunk-3SSKC3VN.js";import{a as F}from"./chunk-LEYMRMBK.js";import{e as g}from"./chunk-VTAIKJXX.js";var k=new E,ot=new b,nt=new b,it=new b,rt=new b;function O(t){t=t??Z.EMPTY_OBJECT;let n=t.length,e=t.topRadius,h=t.bottomRadius,o=t.vertexFormat??_.DEFAULT,s=t.slices??128;if(!g(n))throw new F("options.length must be defined.");if(!g(e))throw new F("options.topRadius must be defined.");if(!g(h))throw new F("options.bottomRadius must be defined.");if(s<3)throw new F("options.slices must be greater than or equal to 3.");if(g(t.offsetAttribute)&&t.offsetAttribute===N.TOP)throw new F("GeometryOffsetAttribute.TOP is not a supported options.offsetAttribute for this geometry.");this._length=n,this._topRadius=e,this._bottomRadius=h,this._vertexFormat=_.clone(o),this._slices=s,this._offsetAttribute=t.offsetAttribute,this._workerName="createCylinderGeometry"}O.packedLength=_.packedLength+5;O.pack=function(t,n,e){if(!g(t))throw new F("value is required");if(!g(n))throw new F("array is required");return e=e??0,_.pack(t._vertexFormat,n,e),e+=_.packedLength,n[e++]=t._length,n[e++]=t._topRadius,n[e++]=t._bottomRadius,n[e++]=t._slices,n[e]=t._offsetAttribute??-1,n};var X=new _,D={vertexFormat:X,length:void 0,topRadius:void 0,bottomRadius:void 0,slices:void 0,offsetAttribute:void 0};O.unpack=function(t,n,e){if(!g(t))throw new F("array is required");n=n??0;let h=_.unpack(t,n,X);n+=_.packedLength;let o=t[n++],s=t[n++],p=t[n++],P=t[n++],w=t[n];return g(e)?(e._vertexFormat=_.clone(h,e._vertexFormat),e._length=o,e._topRadius=s,e._bottomRadius=p,e._slices=P,e._offsetAttribute=w===-1?void 0:w,e):(D.length=o,D.topRadius=s,D.bottomRadius=p,D.slices=P,D.offsetAttribute=w===-1?void 0:w,new O(D))};O.createGeometry=function(t){let n=t._length,e=t._topRadius,h=t._bottomRadius,o=t._vertexFormat,s=t._slices;if(n<=0||e<0||h<0||e===0&&h===0)return;let p=s+s,P=s+p,w=p+p,C=Q.computePositions(n,e,h,s,!0),z=o.st?new Float32Array(w*2):void 0,c=o.normal?new Float32Array(w*3):void 0,m=o.tangent?new Float32Array(w*3):void 0,u=o.bitangent?new Float32Array(w*3):void 0,i,G=o.normal||o.tangent||o.bitangent;if(G){let T=o.tangent||o.bitangent,f=0,d=0,l=0,q=Math.atan2(h-e,n),A=ot;A.z=Math.sin(q);let B=Math.cos(q),R=it,y=nt;for(i=0;i<s;i++){let V=i/s*Y.TWO_PI,tt=B*Math.cos(V),et=B*Math.sin(V);G&&(A.x=tt,A.y=et,T&&(R=b.normalize(b.cross(b.UNIT_Z,A,R),R)),o.normal&&(c[f++]=A.x,c[f++]=A.y,c[f++]=A.z,c[f++]=A.x,c[f++]=A.y,c[f++]=A.z),o.tangent&&(m[d++]=R.x,m[d++]=R.y,m[d++]=R.z,m[d++]=R.x,m[d++]=R.y,m[d++]=R.z),o.bitangent&&(y=b.normalize(b.cross(A,R,y),y),u[l++]=y.x,u[l++]=y.y,u[l++]=y.z,u[l++]=y.x,u[l++]=y.y,u[l++]=y.z))}for(i=0;i<s;i++)o.normal&&(c[f++]=0,c[f++]=0,c[f++]=-1),o.tangent&&(m[d++]=1,m[d++]=0,m[d++]=0),o.bitangent&&(u[l++]=0,u[l++]=-1,u[l++]=0);for(i=0;i<s;i++)o.normal&&(c[f++]=0,c[f++]=0,c[f++]=1),o.tangent&&(m[d++]=1,m[d++]=0,m[d++]=0),o.bitangent&&(u[l++]=0,u[l++]=1,u[l++]=0)}let $=12*s-12,r=K.createTypedArray(w,$),a=0,x=0;for(i=0;i<s-1;i++)r[a++]=x,r[a++]=x+2,r[a++]=x+3,r[a++]=x,r[a++]=x+3,r[a++]=x+1,x+=2;for(r[a++]=p-2,r[a++]=0,r[a++]=1,r[a++]=p-2,r[a++]=1,r[a++]=p-1,i=1;i<s-1;i++)r[a++]=p+i+1,r[a++]=p+i,r[a++]=p;for(i=1;i<s-1;i++)r[a++]=P,r[a++]=P+i,r[a++]=P+i+1;let U=0;if(o.st){let T=Math.max(e,h);for(i=0;i<w;i++){let f=b.fromArray(C,i*3,rt);z[U++]=(f.x+T)/(2*T),z[U++]=(f.y+T)/(2*T)}}let v=new H;o.position&&(v.position=new L({componentDatatype:S.DOUBLE,componentsPerAttribute:3,values:C})),o.normal&&(v.normal=new L({componentDatatype:S.FLOAT,componentsPerAttribute:3,values:c})),o.tangent&&(v.tangent=new L({componentDatatype:S.FLOAT,componentsPerAttribute:3,values:m})),o.bitangent&&(v.bitangent=new L({componentDatatype:S.FLOAT,componentsPerAttribute:3,values:u})),o.st&&(v.st=new L({componentDatatype:S.FLOAT,componentsPerAttribute:2,values:z})),k.x=n*.5,k.y=Math.max(h,e);let I=new j(b.ZERO,E.magnitude(k));if(g(t._offsetAttribute)){n=C.length;let T=t._offsetAttribute===N.NONE?0:1,f=new Uint8Array(n/3).fill(T);v.applyOffset=new L({componentDatatype:S.UNSIGNED_BYTE,componentsPerAttribute:1,values:f})}return new W({attributes:v,indices:r,primitiveType:J.TRIANGLES,boundingSphere:I,offsetAttribute:t._offsetAttribute})};var M;O.getUnitCylinder=function(){return g(M)||(M=O.createGeometry(new O({topRadius:1,bottomRadius:1,length:1,vertexFormat:_.POSITION_ONLY}))),M};var yt=O;export{yt as a};
