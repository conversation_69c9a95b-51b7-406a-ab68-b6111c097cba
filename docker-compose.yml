version: '3.8'

services:
  # MySQL 数据库服务
  mysql:
    image: mysql:8.0
    container_name: news_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf.d:/etc/mysql/conf.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - news_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Node.js 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: news_backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: mysql
      DB_USER: ${DB_USER}
      DB_PASSWORD: ${DB_PASSWORD}
      DB_NAME: ${DB_NAME}
      TZ: Asia/Shanghai
    ports:
      - "3001:3001"
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/data:/app/data
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - news_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Python 新闻爬虫服务
  crawler:
    build:
      context: ./backend/RSS2NEWS-v2
      dockerfile: Dockerfile
    container_name: news_crawler
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
      # 百度翻译API配置（可选，代码中有默认值）
      BAIDU_APP_ID: ${BAIDU_APP_ID:-}
      BAIDU_SECRET_KEY: ${BAIDU_SECRET_KEY:-}
      # 代理配置（如果需要）
      HTTP_PROXY: ${HTTP_PROXY:-}
      HTTPS_PROXY: ${HTTPS_PROXY:-}
    volumes:
      - ./backend/data:/app/data
      - ./backend/logs:/app/logs
      - crawler_data:/app/output
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - news_network
    # 启动命令：立即运行一次，然后设置定时任务
    command: >
      sh -c "
        echo 'Starting crawler service...' &&
        python main.py &&
        echo '0 * * * * cd /app && python main.py >> /app/logs/crawler.log 2>&1' | crontab - &&
        crond -f
      "

  # Vue.js 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_BASE_URL: ${VITE_API_BASE_URL:-http://localhost:3001}
    container_name: news_frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    networks:
      - news_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存服务（可选，用于提升性能）
  redis:
    image: redis:7-alpine
    container_name: news_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - news_network
    command: redis-server --appendonly yes

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  crawler_data:
    driver: local

networks:
  news_network:
    driver: bridge