const mysql = require('mysql2/promise')
require('dotenv').config()

async function initDatabase() {
  let connection
  
  try {
    console.log('🔍 连接数据库...')
    
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    })
    
    console.log('✅ 数据库连接成功！')
    
    // 🆕 创建主表：新闻基本信息
    const createNewsTable = `
      CREATE TABLE IF NOT EXISTS news (
        id INT AUTO_INCREMENT PRIMARY KEY COMMENT '新闻主键ID',
        publish_time DATETIME NOT NULL COMMENT '发布时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻主表'
    `
    
    await connection.execute(createNewsTable)
    console.log('✅ 新闻主表创建成功')
    
    // 🆕 创建子表：新闻多语言信息
    const createNewsTranslationsTable = `
      CREATE TABLE IF NOT EXISTS news_translations (
        id INT AUTO_INCREMENT PRIMARY KEY COMMENT '翻译主键ID',
        news_id INT NOT NULL COMMENT '关联的新闻ID',
        lang ENUM('zh', 'en') NOT NULL COMMENT '语言：zh=中文，en=英文',
        title VARCHAR(255) NOT NULL COMMENT '新闻标题',
        content TEXT COMMENT '新闻内容',
        source VARCHAR(255) COMMENT '新闻来源',
        country VARCHAR(100) COMMENT '国家名称',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
        FOREIGN KEY (news_id) REFERENCES news(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻翻译表'
    `
    
    await connection.execute(createNewsTranslationsTable)
    console.log('✅ 新闻翻译表创建成功')
    
    // 🔍 检查是否已有数据
    const [newsRows] = await connection.execute('SELECT COUNT(*) as count FROM news')
    const newsCount = newsRows[0].count
    
    // 🔍 显示表结构信息
    console.log('\n📊 数据库表信息:')
    
    const [newsTables] = await connection.execute(`
      SELECT TABLE_NAME, TABLE_COMMENT 
      FROM information_schema.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME IN ('news', 'news_translations')
      ORDER BY TABLE_NAME
    `, [process.env.DB_NAME])
    
    newsTables.forEach(table => {
      console.log(`   - ${table.TABLE_NAME}: ${table.TABLE_COMMENT}`)
    })
    
    // 显示数据统计
    const [newsCount2] = await connection.execute('SELECT COUNT(*) as count FROM news')
    const [translationsCount] = await connection.execute('SELECT COUNT(*) as count FROM news_translations')
    
    console.log('\n📈 数据统计:')
    console.log(`   - 新闻总数: ${newsCount2[0].count}`)
    console.log(`   - 翻译记录: ${translationsCount[0].count}`)
    
    console.log('\n🎉 数据库初始化完成!')
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message)
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n🔧 可能的解决方案:')
      console.log('1. 检查MySQL服务是否启动')
      console.log('2. 确认数据库连接配置是否正确')
      console.log('3. 检查网络连接是否正常')
    }
    
    process.exit(1)
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔐 数据库连接已关闭')
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase()
}

module.exports = { initDatabase }