-- =============================================
-- 初始化基础数据
-- =============================================

-- 插入国家配置数据
INSERT IGNORE INTO `countries` (`code`, `name_en`, `name_zh`, `label_latitude`, `label_longitude`) VALUES
('CHN', 'China', '中国', 35.0000000, 105.0000000),
('USA', 'United States', '美国', 40.0000000, -100.0000000),
('JPN', 'Japan', '日本', 36.0000000, 138.0000000),
('KOR', 'South Korea', '韩国', 37.0000000, 127.5000000),
('VNM', 'Vietnam', '越南', 16.0000000, 108.0000000),
('PHL', 'Philippines', '菲律宾', 13.0000000, 122.0000000),
('MYS', 'Malaysia', '马来西亚', 2.5000000, 112.5000000),
('SGP', 'Singapore', '新加坡', 1.3521000, 103.8198000),
('THA', 'Thailand', '泰国', 15.0000000, 100.0000000),
('IDN', 'Indonesia', '印度尼西亚', -5.0000000, 120.0000000),
('BRN', 'Brunei', '文莱', 4.5353000, 114.7277000),
('TWN', 'Taiwan', '台湾', 23.8000000, 121.0000000),
('HKG', 'Hong Kong', '香港', 22.3193000, 114.1694000),
('MAC', 'Macau', '澳门', 22.1987000, 113.5439000),
('AUS', 'Australia', '澳大利亚', -25.0000000, 133.0000000),
('IND', 'India', '印度', 20.0000000, 77.0000000),
('GBR', 'United Kingdom', '英国', 54.0000000, -2.0000000),
('FRA', 'France', '法国', 46.0000000, 2.0000000),
('DEU', 'Germany', '德国', 51.0000000, 9.0000000),
('RUS', 'Russia', '俄罗斯', 60.0000000, 100.0000000),
('UNK', 'Unknown', '未知', 12.0000000, 114.0000000);

-- 插入域名映射数据
INSERT IGNORE INTO `domain_mappings` (`domain`, `country_code`, `media_type`) VALUES
('xinhuanet.com', 'CHN', 'news'),
('people.com.cn', 'CHN', 'news'),
('chinadaily.com.cn', 'CHN', 'news'),
('cnn.com', 'USA', 'news'),
('bbc.com', 'GBR', 'news'),
('reuters.com', 'GBR', 'news'),
('ap.org', 'USA', 'news'),
('nytimes.com', 'USA', 'news'),
('washingtonpost.com', 'USA', 'news'),
('wsj.com', 'USA', 'news'),
('bloomberg.com', 'USA', 'news'),
('ft.com', 'GBR', 'news'),
('guardian.com', 'GBR', 'news'),
('nhk.or.jp', 'JPN', 'news'),
('asahi.com', 'JPN', 'news'),
('yonhapnews.co.kr', 'KOR', 'news'),
('channelnewsasia.com', 'SGP', 'news'),
('straitstimes.com', 'SGP', 'news'),
('thestar.com.my', 'MYS', 'news'),
('bangkokpost.com', 'THA', 'news'),
('nationthailand.com', 'THA', 'news'),
('vietnamnews.vn', 'VNM', 'news'),
('tuoitrenews.vn', 'VNM', 'news'),
('philstar.com', 'PHL', 'news'),
('inquirer.net', 'PHL', 'news'),
('abc.net.au', 'AUS', 'news'),
('smh.com.au', 'AUS', 'news'),
('theage.com.au', 'AUS', 'news'),
('scmp.com', 'HKG', 'news'),
('rthk.hk', 'HKG', 'news'),
('chinatimes.com', 'TWN', 'news'),
('udn.com', 'TWN', 'news'),
('rt.com', 'RUS', 'news'),
('sputniknews.com', 'RUS', 'news'),
('timesofindia.indiatimes.com', 'IND', 'news'),
('hindustantimes.com', 'IND', 'news'),
('thehindu.com', 'IND', 'news'),
('dw.com', 'DEU', 'news'),
('france24.com', 'FRA', 'news'),
('rfi.fr', 'FRA', 'news');

SET FOREIGN_KEY_CHECKS = 1;