# main.py
from getRSS import collect_google_news, save_news_to_csv
from getOriginalLinks import process_csv as process_original_links
from url2country import add_country_column
from getNewsText import add_news_text_columns
from baidu_translate import add_translation_column   # 新增导入
from import_to_db import import_to_database_incremental
import os

def run_getRSS(csv_file):
    """获取 Google News RSS 并保存到 CSV"""
    keyword = "south china sea"
    news_list = collect_google_news(keyword)
    save_news_to_csv(news_list, filename=csv_file)


def run_getOriginalLinks(csv_file):
    """解析 RSS 链接，得到 original_link"""
    process_original_links(csv_file)


def run_url2country(csv_file):
    """根据 original_link 映射国家，并更新 CSV"""
    add_country_column(csv_file)


def run_getNewsText(csv_file):
    """解析 original_link，提取 authors、keywords、text，并更新 CSV"""
    add_news_text_columns(csv_file)


def run_baidu_translate(csv_file):
    """翻译新闻标题并写回 CSV"""
    add_translation_column(csv_file)


def run_pipeline(csv_file="south_china_sea_news.csv"):
    """完整流水线执行"""
    print("=== [1] 获取 RSS 新闻 ===")
    run_getRSS(csv_file)

    print("=== [2] 解析原始链接 ===")
    run_getOriginalLinks(csv_file)

    print("=== [3] URL -> 国家映射 ===")
    run_url2country(csv_file)

    print("=== [4] 提取新闻正文、关键词、作者 ===")
    run_getNewsText(csv_file)

    print("=== [5] 翻译新闻标题 ===")
    run_baidu_translate(csv_file)
    
    print("=== [6] 增量导入数据库 ===")
    import_to_database_incremental(csv_file)

    print(f"\n✅ 当前流程完成，结果已保存到 {csv_file}")


if __name__ == "__main__":
    # 使用相对路径，适配Docker容器环境
    # Docker容器内：/app/data/south_china_sea_news.csv
    # 本地开发：./data/south_china_sea_news.csv
    data_dir = "/app/data" if os.path.exists("/app/data") else "./data"
    os.makedirs(data_dir, exist_ok=True)
    csv_file = os.path.join(data_dir, "south_china_sea_news.csv")
    run_pipeline(csv_file)
