import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      'cesium': 'cesium'
    },
  },
  define: {
    CESIUM_BASE_URL: JSON.stringify('/cesium')
  },
  publicDir: 'public',
  server: {
    host: '0.0.0.0',   // 允许局域网和公网访问
    port: 5173,        // 你想监听的端口（默认 5173）
    proxy: {
      '/api': {
        target: 'http://*************:3001',
        changeOrigin: true,
        secure: false
      }
    }
  }
})
