/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.132
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as on}from"./chunk-WZDE3RYP.js";import{a as f}from"./chunk-OSW76XDF.js";import{a as re}from"./chunk-ED5JPB3S.js";import{b as sn}from"./chunk-LEYMRMBK.js";import{a as Vt,c as rr,d as sr,e as Le}from"./chunk-VTAIKJXX.js";var cn=rr((ot,pt)=>{var Pt=function(){var s=typeof document<"u"&&document.currentScript?document.currentScript.src:void 0;return typeof __filename<"u"&&(s=s||__filename),function(T){T=T||{};var r=typeof T<"u"?T:{},M,B;r.ready=new Promise(function(e,t){M=e,B=t});var E={},F;for(F in r)r.hasOwnProperty(F)&&(E[F]=r[F]);var L=[],D="./this.program",b=function(e,t){throw t},N=!1,d=!1,P=!1,Q=!1;N=typeof window=="object",d=typeof importScripts=="function",P=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",Q=!N&&!P&&!d;var O="";function W(e){return r.locateFile?r.locateFile(e,O):O+e}var J,ae,j,Ke,Ce,Fe;P?(d?O=Vt("path").dirname(O)+"/":O=__dirname+"/",J=function(t,n){return Ce||(Ce=Vt("fs")),Fe||(Fe=Vt("path")),t=Fe.normalize(t),Ce.readFileSync(t,n?null:"utf8")},j=function(t){var n=J(t,!0);return n.buffer||(n=new Uint8Array(n)),I(n.buffer),n},process.argv.length>1&&(D=process.argv[1].replace(/\\/g,"/")),L=process.argv.slice(2),process.on("uncaughtException",function(e){if(!(e instanceof nr))throw e}),process.on("unhandledRejection",Oe),b=function(e){process.exit(e)},r.inspect=function(){return"[Emscripten Module object]"}):Q?(typeof read<"u"&&(J=function(t){return read(t)}),j=function(t){var n;return typeof readbuffer=="function"?new Uint8Array(readbuffer(t)):(n=read(t,"binary"),I(typeof n=="object"),n)},typeof scriptArgs<"u"?L=scriptArgs:typeof arguments<"u"&&(L=arguments),typeof quit=="function"&&(b=function(e){quit(e)}),typeof print<"u"&&(typeof console>"u"&&(console={}),console.log=print,console.warn=console.error=typeof printErr<"u"?printErr:print)):(N||d)&&(d?O=self.location.href:typeof document<"u"&&document.currentScript&&(O=document.currentScript.src),s&&(O=s),O.indexOf("blob:")!==0?O=O.substr(0,O.lastIndexOf("/")+1):O="",J=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.send(null),t.responseText},d&&(j=function(e){var t=new XMLHttpRequest;return t.open("GET",e,!1),t.responseType="arraybuffer",t.send(null),new Uint8Array(t.response)}),ae=function(e,t,n){var _=new XMLHttpRequest;_.open("GET",e,!0),_.responseType="arraybuffer",_.onload=function(){if(_.status==200||_.status==0&&_.response){t(_.response);return}n()},_.onerror=n,_.send(null)},Ke=function(e){document.title=e});var it=r.print||console.log.bind(console),se=r.printErr||console.warn.bind(console);for(F in E)E.hasOwnProperty(F)&&(r[F]=E[F]);E=null,r.arguments&&(L=r.arguments),r.thisProgram&&(D=r.thisProgram),r.quit&&(b=r.quit);var Rt=0,Ut=function(e){Rt=e},oe;r.wasmBinary&&(oe=r.wasmBinary);var On=r.noExitRuntime||!0;typeof WebAssembly!="object"&&Oe("no native wasm support detected");var de,Ie=!1,ce;function I(e,t){e||Oe("Assertion failed: "+t)}var be=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function we(e,t,n){for(var _=t+n,o=t;e[o]&&!(o>=_);)++o;if(o-t>16&&e.subarray&&be)return be.decode(e.subarray(t,o));for(var c="";t<o;){var A=e[t++];if(!(A&128)){c+=String.fromCharCode(A);continue}var R=e[t++]&63;if((A&224)==192){c+=String.fromCharCode((A&31)<<6|R);continue}var a=e[t++]&63;if((A&240)==224?A=(A&15)<<12|R<<6|a:A=(A&7)<<18|R<<12|a<<6|e[t++]&63,A<65536)c+=String.fromCharCode(A);else{var u=A-65536;c+=String.fromCharCode(55296|u>>10,56320|u&1023)}}return c}function Ge(e,t){return e?we(p,e,t):""}function He(e,t,n,_){if(!(_>0))return 0;for(var o=n,c=n+_-1,A=0;A<e.length;++A){var R=e.charCodeAt(A);if(R>=55296&&R<=57343){var a=e.charCodeAt(++A);R=65536+((R&1023)<<10)|a&1023}if(R<=127){if(n>=c)break;t[n++]=R}else if(R<=2047){if(n+1>=c)break;t[n++]=192|R>>6,t[n++]=128|R&63}else if(R<=65535){if(n+2>=c)break;t[n++]=224|R>>12,t[n++]=128|R>>6&63,t[n++]=128|R&63}else{if(n+3>=c)break;t[n++]=240|R>>18,t[n++]=128|R>>12&63,t[n++]=128|R>>6&63,t[n++]=128|R&63}}return t[n]=0,n-o}function Xe(e,t,n){return He(e,p,t,n)}function at(e){for(var t=0,n=0;n<e.length;++n){var _=e.charCodeAt(n);_>=55296&&_<=57343&&(_=65536+((_&1023)<<10)|e.charCodeAt(++n)&1023),_<=127?++t:_<=2047?t+=2:_<=65535?t+=3:t+=4}return t}var Se=typeof TextDecoder<"u"?new TextDecoder("utf-16le"):void 0;function xe(e,t){for(var n=e,_=n>>1,o=_+t/2;!(_>=o)&&Ae[_];)++_;if(n=_<<1,n-e>32&&Se)return Se.decode(p.subarray(e,n));for(var c="",A=0;!(A>=t/2);++A){var R=k[e+A*2>>1];if(R==0)break;c+=String.fromCharCode(R)}return c}function ke(e,t,n){if(n===void 0&&(n=2147483647),n<2)return 0;n-=2;for(var _=t,o=n<e.length*2?n/2:e.length,c=0;c<o;++c){var A=e.charCodeAt(c);k[t>>1]=A,t+=2}return k[t>>1]=0,t-_}function $e(e){return e.length*2}function ct(e,t){for(var n=0,_="";!(n>=t/4);){var o=m[e+n*4>>2];if(o==0)break;if(++n,o>=65536){var c=o-65536;_+=String.fromCharCode(55296|c>>10,56320|c&1023)}else _+=String.fromCharCode(o)}return _}function At(e,t,n){if(n===void 0&&(n=2147483647),n<4)return 0;for(var _=t,o=_+n-4,c=0;c<e.length;++c){var A=e.charCodeAt(c);if(A>=55296&&A<=57343){var R=e.charCodeAt(++c);A=65536+((A&1023)<<10)|R&1023}if(m[t>>2]=A,t+=4,t+4>o)break}return m[t>>2]=0,t-_}function Tt(e){for(var t=0,n=0;n<e.length;++n){var _=e.charCodeAt(n);_>=55296&&_<=57343&&++n,t+=4}return t}function ut(e,t){return e%t>0&&(e+=t-e%t),e}var Ne,v,p,k,Ae,m,z,vt,ht;function yt(e){Ne=e,r.HEAP8=v=new Int8Array(e),r.HEAP16=k=new Int16Array(e),r.HEAP32=m=new Int32Array(e),r.HEAPU8=p=new Uint8Array(e),r.HEAPU16=Ae=new Uint16Array(e),r.HEAPU32=z=new Uint32Array(e),r.HEAPF32=vt=new Float32Array(e),r.HEAPF64=ht=new Float64Array(e)}var ur=r.INITIAL_MEMORY||16777216,Ee,gt=[],Lt=[],ln=[],mt=[],Bn=!1;function Mn(){if(r.preRun)for(typeof r.preRun=="function"&&(r.preRun=[r.preRun]);r.preRun.length;)dn(r.preRun.shift());We(gt)}function Kn(){Bn=!0,We(Lt)}function Cn(){We(ln)}function Fn(){if(r.postRun)for(typeof r.postRun=="function"&&(r.postRun=[r.postRun]);r.postRun.length;)Sn(r.postRun.shift());We(mt)}function dn(e){gt.unshift(e)}function Gn(e){Lt.unshift(e)}function Sn(e){mt.unshift(e)}var Te=0,ft=null,Ve=null;function Nn(e){Te++,r.monitorRunDependencies&&r.monitorRunDependencies(Te)}function En(e){if(Te--,r.monitorRunDependencies&&r.monitorRunDependencies(Te),Te==0&&(ft!==null&&(clearInterval(ft),ft=null),Ve)){var t=Ve;Ve=null,t()}}r.preloadedImages={},r.preloadedAudios={};function Oe(e){r.onAbort&&r.onAbort(e),e+="",se(e),Ie=!0,ce=1,e="abort("+e+"). Build with -s ASSERTIONS=1 for more info.";var t=new WebAssembly.RuntimeError(e);throw B(t),t}function Dt(e,t){return String.prototype.startsWith?e.startsWith(t):e.indexOf(t)===0}var Vn="data:application/octet-stream;base64,";function It(e){return Dt(e,Vn)}var Pn="file://";function bt(e){return Dt(e,Pn)}var X="basis_transcoder.wasm";It(X)||(X=W(X));function wt(e){try{if(e==X&&oe)return new Uint8Array(oe);if(j)return j(e);throw"both async and sync fetching of the wasm failed"}catch(t){Oe(t)}}function pn(){if(!oe&&(N||d)){if(typeof fetch=="function"&&!bt(X))return fetch(X,{credentials:"same-origin"}).then(function(e){if(!e.ok)throw"failed to load wasm binary file at '"+X+"'";return e.arrayBuffer()}).catch(function(){return wt(X)});if(ae)return new Promise(function(e,t){ae(X,function(n){e(new Uint8Array(n))},t)})}return Promise.resolve().then(function(){return wt(X)})}function Un(){var e={a:Q_};function t(A,R){var a=A.exports;r.asm=a,de=r.asm.K,yt(de.buffer),Ee=r.asm.O,Gn(r.asm.L),En("wasm-instantiate")}Nn("wasm-instantiate");function n(A){t(A.instance)}function _(A){return pn().then(function(R){var a=WebAssembly.instantiate(R,e);return a}).then(A,function(R){se("failed to asynchronously prepare wasm: "+R),Oe(R)})}function o(){return!oe&&typeof WebAssembly.instantiateStreaming=="function"&&!It(X)&&!bt(X)&&typeof fetch=="function"?fetch(X,{credentials:"same-origin"}).then(function(A){var R=WebAssembly.instantiateStreaming(A,e);return R.then(n,function(a){return se("wasm streaming compile failed: "+a),se("falling back to ArrayBuffer instantiation"),_(n)})}):_(n)}if(r.instantiateWasm)try{var c=r.instantiateWasm(e,t);return c}catch(A){return se("Module.instantiateWasm callback failed with error: "+A),!1}return o().catch(B),{}}function We(e){for(;e.length>0;){var t=e.shift();if(typeof t=="function"){t(r);continue}var n=t.func;typeof n=="number"?t.arg===void 0?Ee.get(n)():Ee.get(n)(t.arg):n(t.arg===void 0?null:t.arg)}}var je={};function Ye(e){for(;e.length;){var t=e.pop(),n=e.pop();n(t)}}function Pe(e){return this.fromWireType(z[e>>2])}var le={},ue={},ze={},vn=48,hn=57;function qe(e){if(e===void 0)return"_unknown";e=e.replace(/[^a-zA-Z0-9_]/g,"$");var t=e.charCodeAt(0);return t>=vn&&t<=hn?"_"+e:e}function Ze(e,t){return e=qe(e),new Function("body","return function "+e+`() {
    "use strict";    return body.apply(this, arguments);
};
`)(t)}function Ot(e,t){var n=Ze(t,function(_){this.name=t,this.message=_;var o=new Error(_).stack;o!==void 0&&(this.stack=this.toString()+`
`+o.replace(/^Error(:[^\n]*)?\n/,""))});return n.prototype=Object.create(e.prototype),n.prototype.constructor=n,n.prototype.toString=function(){return this.message===void 0?this.name:this.name+": "+this.message},n}var Ht=void 0;function Qe(e){throw new Ht(e)}function ie(e,t,n){e.forEach(function(R){ze[R]=t});function _(R){var a=n(R);a.length!==e.length&&Qe("Mismatched type converter count");for(var u=0;u<e.length;++u)q(e[u],a[u])}var o=new Array(t.length),c=[],A=0;t.forEach(function(R,a){ue.hasOwnProperty(R)?o[a]=ue[R]:(c.push(R),le.hasOwnProperty(R)||(le[R]=[]),le[R].push(function(){o[a]=ue[R],++A,A===c.length&&_(o)}))}),c.length===0&&_(o)}function yn(e){var t=je[e];delete je[e];var n=t.rawConstructor,_=t.rawDestructor,o=t.fields,c=o.map(function(A){return A.getterReturnType}).concat(o.map(function(A){return A.setterArgumentType}));ie([e],c,function(A){var R={};return o.forEach(function(a,u){var l=a.fieldName,K=A[u],S=a.getter,V=a.getterContext,g=A[u+o.length],w=a.setter,$=a.setterContext;R[l]={read:function(Z){return K.fromWireType(S(V,Z))},write:function(Z,ge){var _e=[];w($,Z,g.toWireType(_e,ge)),Ye(_e)}}}),[{name:t.name,fromWireType:function(a){var u={};for(var l in R)u[l]=R[l].read(a);return _(a),u},toWireType:function(a,u){for(var l in R)if(!(l in u))throw new TypeError('Missing field:  "'+l+'"');var K=n();for(l in R)R[l].write(K,u[l]);return a!==null&&a.push(_,K),K},argPackAdvance:8,readValueFromPointer:Pe,destructorFunction:_}]})}function Je(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError("Unknown type size: "+e)}}function gn(){for(var e=new Array(256),t=0;t<256;++t)e[t]=String.fromCharCode(t);Xt=e}var Xt=void 0;function h(e){for(var t="",n=e;p[n];)t+=Xt[p[n++]];return t}var Be=void 0;function G(e){throw new Be(e)}function q(e,t,n){if(n=n||{},!("argPackAdvance"in t))throw new TypeError("registerType registeredInstance requires argPackAdvance");var _=t.name;if(e||G('type "'+_+'" must have a positive integer typeid pointer'),ue.hasOwnProperty(e)){if(n.ignoreDuplicateRegistrations)return;G("Cannot register type '"+_+"' twice")}if(ue[e]=t,delete ze[e],le.hasOwnProperty(e)){var o=le[e];delete le[e],o.forEach(function(c){c()})}}function Ln(e,t,n,_,o){var c=Je(n);t=h(t),q(e,{name:t,fromWireType:function(A){return!!A},toWireType:function(A,R){return R?_:o},argPackAdvance:8,readValueFromPointer:function(A){var R;if(n===1)R=v;else if(n===2)R=k;else if(n===4)R=m;else throw new TypeError("Unknown boolean type size: "+t);return this.fromWireType(R[A>>c])},destructorFunction:null})}function mn(e){if(!(this instanceof Re)||!(e instanceof Re))return!1;for(var t=this.$$.ptrType.registeredClass,n=this.$$.ptr,_=e.$$.ptrType.registeredClass,o=e.$$.ptr;t.baseClass;)n=t.upcast(n),t=t.baseClass;for(;_.baseClass;)o=_.upcast(o),_=_.baseClass;return t===_&&n===o}function Dn(e){return{count:e.count,deleteScheduled:e.deleteScheduled,preservePointerOnDelete:e.preservePointerOnDelete,ptr:e.ptr,ptrType:e.ptrType,smartPtr:e.smartPtr,smartPtrType:e.smartPtrType}}function lt(e){function t(n){return n.$$.ptrType.registeredClass.name}G(t(e)+" instance already deleted")}var Bt=!1;function xt(e){}function In(e){e.smartPtr?e.smartPtrType.rawDestructor(e.smartPtr):e.ptrType.registeredClass.rawDestructor(e.ptr)}function kt(e){e.count.value-=1;var t=e.count.value===0;t&&In(e)}function pe(e){return typeof FinalizationGroup>"u"?(pe=function(t){return t},e):(Bt=new FinalizationGroup(function(t){for(var n=t.next();!n.done;n=t.next()){var _=n.value;_.ptr?kt(_):console.warn("object already deleted: "+_.ptr)}}),pe=function(t){return Bt.register(t,t.$$,t.$$),t},xt=function(t){Bt.unregister(t.$$)},pe(e))}function bn(){if(this.$$.ptr||lt(this),this.$$.preservePointerOnDelete)return this.$$.count.value+=1,this;var e=pe(Object.create(Object.getPrototypeOf(this),{$$:{value:Dn(this.$$)}}));return e.$$.count.value+=1,e.$$.deleteScheduled=!1,e}function wn(){this.$$.ptr||lt(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&G("Object already scheduled for deletion"),xt(this),kt(this.$$),this.$$.preservePointerOnDelete||(this.$$.smartPtr=void 0,this.$$.ptr=void 0)}function Hn(){return!this.$$.ptr}var Ue=void 0,ve=[];function Mt(){for(;ve.length;){var e=ve.pop();e.$$.deleteScheduled=!1,e.delete()}}function Xn(){return this.$$.ptr||lt(this),this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete&&G("Object already scheduled for deletion"),ve.push(this),ve.length===1&&Ue&&Ue(Mt),this.$$.deleteScheduled=!0,this}function xn(){Re.prototype.isAliasOf=mn,Re.prototype.clone=bn,Re.prototype.delete=wn,Re.prototype.isDeleted=Hn,Re.prototype.deleteLater=Xn}function Re(){}var $t={};function Wt(e,t,n){if(e[t].overloadTable===void 0){var _=e[t];e[t]=function(){return e[t].overloadTable.hasOwnProperty(arguments.length)||G("Function '"+n+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].overloadTable+")!"),e[t].overloadTable[arguments.length].apply(this,arguments)},e[t].overloadTable=[],e[t].overloadTable[_.argCount]=_}}function Kt(e,t,n){r.hasOwnProperty(e)?((n===void 0||r[e].overloadTable!==void 0&&r[e].overloadTable[n]!==void 0)&&G("Cannot register public name '"+e+"' twice"),Wt(r,e,e),r.hasOwnProperty(n)&&G("Cannot register multiple overloads of a function with the same number of arguments ("+n+")!"),r[e].overloadTable[n]=t):(r[e]=t,n!==void 0&&(r[e].numArguments=n))}function kn(e,t,n,_,o,c,A,R){this.name=e,this.constructor=t,this.instancePrototype=n,this.rawDestructor=_,this.baseClass=o,this.getActualType=c,this.upcast=A,this.downcast=R,this.pureVirtualFunctions=[]}function Ct(e,t,n){for(;t!==n;)t.upcast||G("Expected null or instance of "+n.name+", got an instance of "+t.name),e=t.upcast(e),t=t.baseClass;return e}function $n(e,t){if(t===null)return this.isReference&&G("null is not a valid "+this.name),0;t.$$||G('Cannot pass "'+Me(t)+'" as a '+this.name),t.$$.ptr||G("Cannot pass deleted object as a pointer of type "+this.name);var n=t.$$.ptrType.registeredClass,_=Ct(t.$$.ptr,n,this.registeredClass);return _}function Wn(e,t){var n;if(t===null)return this.isReference&&G("null is not a valid "+this.name),this.isSmartPointer?(n=this.rawConstructor(),e!==null&&e.push(this.rawDestructor,n),n):0;t.$$||G('Cannot pass "'+Me(t)+'" as a '+this.name),t.$$.ptr||G("Cannot pass deleted object as a pointer of type "+this.name),!this.isConst&&t.$$.ptrType.isConst&&G("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);var _=t.$$.ptrType.registeredClass;if(n=Ct(t.$$.ptr,_,this.registeredClass),this.isSmartPointer)switch(t.$$.smartPtr===void 0&&G("Passing raw pointer to smart pointer is illegal"),this.sharingPolicy){case 0:t.$$.smartPtrType===this?n=t.$$.smartPtr:G("Cannot convert argument of type "+(t.$$.smartPtrType?t.$$.smartPtrType.name:t.$$.ptrType.name)+" to parameter type "+this.name);break;case 1:n=t.$$.smartPtr;break;case 2:if(t.$$.smartPtrType===this)n=t.$$.smartPtr;else{var o=t.clone();n=this.rawShare(n,te(function(){o.delete()})),e!==null&&e.push(this.rawDestructor,n)}break;default:G("Unsupporting sharing policy")}return n}function jn(e,t){if(t===null)return this.isReference&&G("null is not a valid "+this.name),0;t.$$||G('Cannot pass "'+Me(t)+'" as a '+this.name),t.$$.ptr||G("Cannot pass deleted object as a pointer of type "+this.name),t.$$.ptrType.isConst&&G("Cannot convert argument of type "+t.$$.ptrType.name+" to parameter type "+this.name);var n=t.$$.ptrType.registeredClass,_=Ct(t.$$.ptr,n,this.registeredClass);return _}function Yn(e){return this.rawGetPointee&&(e=this.rawGetPointee(e)),e}function zn(e){this.rawDestructor&&this.rawDestructor(e)}function qn(e){e!==null&&e.delete()}function jt(e,t,n){if(t===n)return e;if(n.baseClass===void 0)return null;var _=jt(e,t,n.baseClass);return _===null?null:n.downcast(_)}function Zn(){return Object.keys(he).length}function Qn(){var e=[];for(var t in he)he.hasOwnProperty(t)&&e.push(he[t]);return e}function Jn(e){Ue=e,ve.length&&Ue&&Ue(Mt)}function e_(){r.getInheritedInstanceCount=Zn,r.getLiveInheritedInstances=Qn,r.flushPendingDeletes=Mt,r.setDelayFunction=Jn}var he={};function t_(e,t){for(t===void 0&&G("ptr should not be undefined");e.baseClass;)t=e.upcast(t),e=e.baseClass;return t}function n_(e,t){return t=t_(e,t),he[t]}function et(e,t){(!t.ptrType||!t.ptr)&&Qe("makeClassHandle requires ptr and ptrType");var n=!!t.smartPtrType,_=!!t.smartPtr;return n!==_&&Qe("Both smartPtrType and smartPtr must be specified"),t.count={value:1},pe(Object.create(e,{$$:{value:t}}))}function __(e){var t=this.getPointee(e);if(!t)return this.destructor(e),null;var n=n_(this.registeredClass,t);if(n!==void 0){if(n.$$.count.value===0)return n.$$.ptr=t,n.$$.smartPtr=e,n.clone();var _=n.clone();return this.destructor(e),_}function o(){return this.isSmartPointer?et(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:t,smartPtrType:this,smartPtr:e}):et(this.registeredClass.instancePrototype,{ptrType:this,ptr:e})}var c=this.registeredClass.getActualType(t),A=$t[c];if(!A)return o.call(this);var R;this.isConst?R=A.constPointerType:R=A.pointerType;var a=jt(t,this.registeredClass,R.registeredClass);return a===null?o.call(this):this.isSmartPointer?et(R.registeredClass.instancePrototype,{ptrType:R,ptr:a,smartPtrType:this,smartPtr:e}):et(R.registeredClass.instancePrototype,{ptrType:R,ptr:a})}function r_(){ee.prototype.getPointee=Yn,ee.prototype.destructor=zn,ee.prototype.argPackAdvance=8,ee.prototype.readValueFromPointer=Pe,ee.prototype.deleteObject=qn,ee.prototype.fromWireType=__}function ee(e,t,n,_,o,c,A,R,a,u,l){this.name=e,this.registeredClass=t,this.isReference=n,this.isConst=_,this.isSmartPointer=o,this.pointeeType=c,this.sharingPolicy=A,this.rawGetPointee=R,this.rawConstructor=a,this.rawShare=u,this.rawDestructor=l,!o&&t.baseClass===void 0?_?(this.toWireType=$n,this.destructorFunction=null):(this.toWireType=jn,this.destructorFunction=null):this.toWireType=Wn}function Yt(e,t,n){r.hasOwnProperty(e)||Qe("Replacing nonexistant public symbol"),r[e].overloadTable!==void 0&&n!==void 0?r[e].overloadTable[n]=t:(r[e]=t,r[e].argCount=n)}function s_(e,t,n){var _=r["dynCall_"+e];return n&&n.length?_.apply(null,[t].concat(n)):_.call(null,t)}function o_(e,t,n){return e.indexOf("j")!=-1?s_(e,t,n):Ee.get(t).apply(null,n)}function i_(e,t){var n=[];return function(){n.length=arguments.length;for(var _=0;_<arguments.length;_++)n[_]=arguments[_];return o_(e,t,n)}}function Y(e,t){e=h(e);function n(){return e.indexOf("j")!=-1?i_(e,t):Ee.get(t)}var _=n();return typeof _!="function"&&G("unknown function pointer with signature "+e+": "+t),_}var zt=void 0;function qt(e){var t=tn(e),n=h(t);return ne(t),n}function tt(e,t){var n=[],_={};function o(c){if(!_[c]&&!ue[c]){if(ze[c]){ze[c].forEach(o);return}n.push(c),_[c]=!0}}throw t.forEach(o),new zt(e+": "+n.map(qt).join([", "]))}function R_(e,t,n,_,o,c,A,R,a,u,l,K,S){l=h(l),c=Y(o,c),R&&(R=Y(A,R)),u&&(u=Y(a,u)),S=Y(K,S);var V=qe(l);Kt(V,function(){tt("Cannot construct "+l+" due to unbound types",[_])}),ie([e,t,n],_?[_]:[],function(g){g=g[0];var w,$;_?(w=g.registeredClass,$=w.instancePrototype):$=Re.prototype;var Z=Ze(V,function(){if(Object.getPrototypeOf(this)!==ge)throw new Be("Use 'new' to construct "+l);if(_e.constructor_body===void 0)throw new Be(l+" has no accessible constructor");var rn=_e.constructor_body[arguments.length];if(rn===void 0)throw new Be("Tried to invoke ctor of "+l+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(_e.constructor_body).toString()+") parameters instead!");return rn.apply(this,arguments)}),ge=Object.create($,{constructor:{value:Z}});Z.prototype=ge;var _e=new kn(l,Z,ge,S,w,c,R,u),_r=new ee(l,_e,!0,!1,!1),nn=new ee(l+"*",_e,!1,!1,!1),_n=new ee(l+" const*",_e,!1,!0,!1);return $t[e]={pointerType:nn,constPointerType:_n},Yt(V,Z),[_r,nn,_n]})}function Ft(e,t){for(var n=[],_=0;_<e;_++)n.push(m[(t>>2)+_]);return n}function a_(e,t,n,_,o,c){I(t>0);var A=Ft(t,n);o=Y(_,o);var R=[c],a=[];ie([],[e],function(u){u=u[0];var l="constructor "+u.name;if(u.registeredClass.constructor_body===void 0&&(u.registeredClass.constructor_body=[]),u.registeredClass.constructor_body[t-1]!==void 0)throw new Be("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+u.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return u.registeredClass.constructor_body[t-1]=function(){tt("Cannot construct "+u.name+" due to unbound types",A)},ie([],A,function(K){return u.registeredClass.constructor_body[t-1]=function(){arguments.length!==t-1&&G(l+" called with "+arguments.length+" arguments, expected "+(t-1)),a.length=0,R.length=t;for(var V=1;V<t;++V)R[V]=K[V].toWireType(a,arguments[V-1]);var g=o.apply(null,R);return Ye(a),K[0].fromWireType(g)},[]}),[]})}function Zt(e,t){if(!(e instanceof Function))throw new TypeError("new_ called with constructor type "+typeof e+" which is not a function");var n=Ze(e.name||"unknownFunctionName",function(){});n.prototype=e.prototype;var _=new n,o=e.apply(_,t);return o instanceof Object?o:_}function Qt(e,t,n,_,o){var c=t.length;c<2&&G("argTypes array size mismatch! Must at least get return value and 'this' types!");for(var A=t[1]!==null&&n!==null,R=!1,a=1;a<t.length;++a)if(t[a]!==null&&t[a].destructorFunction===void 0){R=!0;break}for(var u=t[0].name!=="void",l="",K="",a=0;a<c-2;++a)l+=(a!==0?", ":"")+"arg"+a,K+=(a!==0?", ":"")+"arg"+a+"Wired";var S="return function "+qe(e)+"("+l+`) {
if (arguments.length !== `+(c-2)+`) {
throwBindingError('function `+e+" called with ' + arguments.length + ' arguments, expected "+(c-2)+` args!');
}
`;R&&(S+=`var destructors = [];
`);var V=R?"destructors":"null",g=["throwBindingError","invoker","fn","runDestructors","retType","classParam"],w=[G,_,o,Ye,t[0],t[1]];A&&(S+="var thisWired = classParam.toWireType("+V+`, this);
`);for(var a=0;a<c-2;++a)S+="var arg"+a+"Wired = argType"+a+".toWireType("+V+", arg"+a+"); // "+t[a+2].name+`
`,g.push("argType"+a),w.push(t[a+2]);if(A&&(K="thisWired"+(K.length>0?", ":"")+K),S+=(u?"var rv = ":"")+"invoker(fn"+(K.length>0?", ":"")+K+`);
`,R)S+=`runDestructors(destructors);
`;else for(var a=A?1:2;a<t.length;++a){var $=a===1?"thisWired":"arg"+(a-2)+"Wired";t[a].destructorFunction!==null&&(S+=$+"_dtor("+$+"); // "+t[a].name+`
`,g.push($+"_dtor"),w.push(t[a].destructorFunction))}u&&(S+=`var ret = retType.fromWireType(rv);
return ret;
`),S+=`}
`,g.push(S);var Z=Zt(Function,g).apply(null,w);return Z}function c_(e,t,n,_,o,c,A,R){var a=Ft(n,_);t=h(t),c=Y(o,c),ie([],[e],function(u){u=u[0];var l=u.name+"."+t;R&&u.registeredClass.pureVirtualFunctions.push(t);function K(){tt("Cannot call "+l+" due to unbound types",a)}var S=u.registeredClass.instancePrototype,V=S[t];return V===void 0||V.overloadTable===void 0&&V.className!==u.name&&V.argCount===n-2?(K.argCount=n-2,K.className=u.name,S[t]=K):(Wt(S,t,l),S[t].overloadTable[n-2]=K),ie([],a,function(g){var w=Qt(l,g,u,c,A);return S[t].overloadTable===void 0?(w.argCount=n-2,S[t]=w):S[t].overloadTable[n-2]=w,[]}),[]})}function A_(e,t,n){e=h(e),ie([],[t],function(_){return _=_[0],r[e]=_.fromWireType(n),[]})}var dt=[],x=[{},{value:void 0},{value:null},{value:!0},{value:!1}];function Gt(e){e>4&&--x[e].refcount===0&&(x[e]=void 0,dt.push(e))}function T_(){for(var e=0,t=5;t<x.length;++t)x[t]!==void 0&&++e;return e}function u_(){for(var e=5;e<x.length;++e)if(x[e]!==void 0)return x[e];return null}function f_(){r.count_emval_handles=T_,r.get_first_emval=u_}function te(e){switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:{var t=dt.length?dt.pop():x.length;return x[t]={refcount:1,value:e},t}}}function O_(e,t){t=h(t),q(e,{name:t,fromWireType:function(n){var _=x[n].value;return Gt(n),_},toWireType:function(n,_){return te(_)},argPackAdvance:8,readValueFromPointer:Pe,destructorFunction:null})}function l_(e,t,n){switch(t){case 0:return function(_){var o=n?v:p;return this.fromWireType(o[_])};case 1:return function(_){var o=n?k:Ae;return this.fromWireType(o[_>>1])};case 2:return function(_){var o=n?m:z;return this.fromWireType(o[_>>2])};default:throw new TypeError("Unknown integer type: "+e)}}function B_(e,t,n,_){var o=Je(n);t=h(t);function c(){}c.values={},q(e,{name:t,constructor:c,fromWireType:function(A){return this.constructor.values[A]},toWireType:function(A,R){return R.value},argPackAdvance:8,readValueFromPointer:l_(t,o,_),destructorFunction:null}),Kt(t,c)}function nt(e,t){var n=ue[e];return n===void 0&&G(t+" has unknown type "+qt(e)),n}function M_(e,t,n){var _=nt(e,"enum");t=h(t);var o=_.constructor,c=Object.create(_.constructor.prototype,{value:{value:n},constructor:{value:Ze(_.name+"_"+t,function(){})}});o.values[n]=c,o[t]=c}function Me(e){if(e===null)return"null";var t=typeof e;return t==="object"||t==="array"||t==="function"?e.toString():""+e}function K_(e,t){switch(t){case 2:return function(n){return this.fromWireType(vt[n>>2])};case 3:return function(n){return this.fromWireType(ht[n>>3])};default:throw new TypeError("Unknown float type: "+e)}}function C_(e,t,n){var _=Je(n);t=h(t),q(e,{name:t,fromWireType:function(o){return o},toWireType:function(o,c){if(typeof c!="number"&&typeof c!="boolean")throw new TypeError('Cannot convert "'+Me(c)+'" to '+this.name);return c},argPackAdvance:8,readValueFromPointer:K_(t,_),destructorFunction:null})}function F_(e,t,n,_,o,c){var A=Ft(t,n);e=h(e),o=Y(_,o),Kt(e,function(){tt("Cannot call "+e+" due to unbound types",A)},t-1),ie([],A,function(R){var a=[R[0],null].concat(R.slice(1));return Yt(e,Qt(e,a,null,o,c),t-1),[]})}function d_(e,t,n){switch(t){case 0:return n?function(o){return v[o]}:function(o){return p[o]};case 1:return n?function(o){return k[o>>1]}:function(o){return Ae[o>>1]};case 2:return n?function(o){return m[o>>2]}:function(o){return z[o>>2]};default:throw new TypeError("Unknown integer type: "+e)}}function G_(e,t,n,_,o){t=h(t),o===-1&&(o=4294967295);var c=Je(n),A=function(u){return u};if(_===0){var R=32-8*n;A=function(u){return u<<R>>>R}}var a=t.indexOf("unsigned")!=-1;q(e,{name:t,fromWireType:A,toWireType:function(u,l){if(typeof l!="number"&&typeof l!="boolean")throw new TypeError('Cannot convert "'+Me(l)+'" to '+this.name);if(l<_||l>o)throw new TypeError('Passing a number "'+Me(l)+'" from JS side to C/C++ side to an argument of type "'+t+'", which is outside the valid range ['+_+", "+o+"]!");return a?l>>>0:l|0},argPackAdvance:8,readValueFromPointer:d_(t,c,_!==0),destructorFunction:null})}function S_(e,t,n){var _=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array],o=_[t];function c(A){A=A>>2;var R=z,a=R[A],u=R[A+1];return new o(Ne,u,a)}n=h(n),q(e,{name:n,fromWireType:c,argPackAdvance:8,readValueFromPointer:c},{ignoreDuplicateRegistrations:!0})}function N_(e,t){t=h(t);var n=t==="std::string";q(e,{name:t,fromWireType:function(_){var o=z[_>>2],c;if(n)for(var A=_+4,R=0;R<=o;++R){var a=_+4+R;if(R==o||p[a]==0){var u=a-A,l=Ge(A,u);c===void 0?c=l:(c+="\0",c+=l),A=a+1}}else{for(var K=new Array(o),R=0;R<o;++R)K[R]=String.fromCharCode(p[_+4+R]);c=K.join("")}return ne(_),c},toWireType:function(_,o){o instanceof ArrayBuffer&&(o=new Uint8Array(o));var c,A=typeof o=="string";A||o instanceof Uint8Array||o instanceof Uint8ClampedArray||o instanceof Int8Array||G("Cannot pass non-string to std::string"),n&&A?c=function(){return at(o)}:c=function(){return o.length};var R=c(),a=Nt(4+R+1);if(z[a>>2]=R,n&&A)Xe(o,a+4,R+1);else if(A)for(var u=0;u<R;++u){var l=o.charCodeAt(u);l>255&&(ne(a),G("String has UTF-16 code units that do not fit in 8 bits")),p[a+4+u]=l}else for(var u=0;u<R;++u)p[a+4+u]=o[u];return _!==null&&_.push(ne,a),a},argPackAdvance:8,readValueFromPointer:Pe,destructorFunction:function(_){ne(_)}})}function E_(e,t,n){n=h(n);var _,o,c,A,R;t===2?(_=xe,o=ke,A=$e,c=function(){return Ae},R=1):t===4&&(_=ct,o=At,A=Tt,c=function(){return z},R=2),q(e,{name:n,fromWireType:function(a){for(var u=z[a>>2],l=c(),K,S=a+4,V=0;V<=u;++V){var g=a+4+V*t;if(V==u||l[g>>R]==0){var w=g-S,$=_(S,w);K===void 0?K=$:(K+="\0",K+=$),S=g+t}}return ne(a),K},toWireType:function(a,u){typeof u!="string"&&G("Cannot pass non-string to C++ string type "+n);var l=A(u),K=Nt(4+l+t);return z[K>>2]=l>>R,o(u,K+4,l+t),a!==null&&a.push(ne,K),K},argPackAdvance:8,readValueFromPointer:Pe,destructorFunction:function(a){ne(a)}})}function V_(e,t,n,_,o,c){je[e]={name:h(t),rawConstructor:Y(n,_),rawDestructor:Y(o,c),fields:[]}}function P_(e,t,n,_,o,c,A,R,a,u){je[e].fields.push({fieldName:h(t),getterReturnType:n,getter:Y(_,o),getterContext:c,setterArgumentType:A,setter:Y(R,a),setterContext:u})}function p_(e,t){t=h(t),q(e,{isVoid:!0,name:t,argPackAdvance:0,fromWireType:function(){},toWireType:function(n,_){}})}function ye(e){return e||G("Cannot use deleted val. handle = "+e),x[e].value}function U_(e,t,n){e=ye(e),t=nt(t,"emval::as");var _=[],o=te(_);return m[n>>2]=o,t.toWireType(_,e)}var v_={};function _t(e){var t=v_[e];return t===void 0?h(e):t}var St=[];function h_(e,t,n,_){e=St[e],t=ye(t),n=_t(n),e(t,n,null,_)}function Jt(){return typeof globalThis=="object"?globalThis:function(){return Function}()("return this")()}function y_(e){return e===0?te(Jt()):(e=_t(e),te(Jt()[e]))}function g_(e){var t=St.length;return St.push(e),t}function L_(e,t){for(var n=new Array(e),_=0;_<e;++_)n[_]=nt(m[(t>>2)+_],"parameter "+_);return n}function m_(e,t){for(var n=L_(e,t),_=n[0],o=_.name+"_$"+n.slice(1).map(function(V){return V.name}).join("_")+"$",c=["retType"],A=[_],R="",a=0;a<e-1;++a)R+=(a!==0?", ":"")+"arg"+a,c.push("argType"+a),A.push(n[1+a]);for(var u=qe("methodCaller_"+o),l="return function "+u+`(handle, name, destructors, args) {
`,K=0,a=0;a<e-1;++a)l+="    var arg"+a+" = argType"+a+".readValueFromPointer(args"+(K?"+"+K:"")+`);
`,K+=n[a+1].argPackAdvance;l+="    var rv = handle[name]("+R+`);
`;for(var a=0;a<e-1;++a)n[a+1].deleteObject&&(l+="    argType"+a+".deleteObject(arg"+a+`);
`);_.isVoid||(l+=`    return retType.toWireType(destructors, rv);
`),l+=`};
`,c.push(l);var S=Zt(Function,c).apply(null,A);return g_(S)}function D_(e){return e=_t(e),te(r[e])}function I_(e,t){return e=ye(e),t=ye(t),te(e[t])}function b_(e){e>4&&(x[e].refcount+=1)}function w_(e){for(var t="",n=0;n<e;++n)t+=(n!==0?", ":"")+"arg"+n;for(var _="return function emval_allocator_"+e+`(constructor, argTypes, args) {
`,n=0;n<e;++n)_+="var argType"+n+" = requireRegisteredType(Module['HEAP32'][(argTypes >>> 2) + "+n+'], "parameter '+n+`");
var arg`+n+" = argType"+n+`.readValueFromPointer(args);
args += argType`+n+`['argPackAdvance'];
`;return _+="var obj = new constructor("+t+`);
return __emval_register(obj);
}
`,new Function("requireRegisteredType","Module","__emval_register",_)(nt,r,te)}var en={};function H_(e,t,n,_){e=ye(e);var o=en[t];return o||(o=w_(t),en[t]=o),o(e,n,_)}function X_(e){return te(_t(e))}function x_(e){var t=x[e].value;Ye(t),Gt(e)}function k_(){Oe()}function $_(e,t,n){p.copyWithin(e,t,t+n)}function W_(e){try{return de.grow(e-Ne.byteLength+65535>>>16),yt(de.buffer),1}catch{}}function j_(e){var t=p.length;e=e>>>0;var n=2147483648;if(e>n)return!1;for(var _=1;_<=4;_*=2){var o=t*(1+.2/_);o=Math.min(o,e+100663296);var c=Math.min(n,ut(Math.max(e,o),65536)),A=W_(c);if(A)return!0}return!1}var rt={mappings:{},buffers:[null,[],[]],printChar:function(e,t){var n=rt.buffers[e];t===0||t===10?((e===1?it:se)(we(n,0)),n.length=0):n.push(t)},varargs:void 0,get:function(){rt.varargs+=4;var e=m[rt.varargs-4>>2];return e},getStr:function(e){var t=Ge(e);return t},get64:function(e,t){return e}};function Y_(e){return 0}function z_(e,t,n,_,o){}function q_(e,t,n,_){for(var o=0,c=0;c<n;c++){for(var A=m[t+c*8>>2],R=m[t+(c*8+4)>>2],a=0;a<R;a++)rt.printChar(e,p[A+a]);o+=R}return m[_>>2]=o,0}function Z_(e){Ut(e|0)}Ht=r.InternalError=Ot(Error,"InternalError"),gn(),Be=r.BindingError=Ot(Error,"BindingError"),xn(),r_(),e_(),zt=r.UnboundTypeError=Ot(Error,"UnboundTypeError"),f_();var Q_={t:yn,I:Ln,x:R_,w:a_,d:c_,k:A_,H:O_,n:B_,a:M_,A:C_,i:F_,j:G_,h:S_,B:N_,v:E_,u:V_,c:P_,J:p_,m:U_,s:h_,b:Gt,y:y_,p:m_,r:D_,e:I_,g:b_,q:H_,f:X_,l:x_,o:k_,E:$_,F:j_,G:Y_,C:z_,z:q_,D:Z_},fr=Un(),J_=r.___wasm_call_ctors=function(){return(J_=r.___wasm_call_ctors=r.asm.L).apply(null,arguments)},Nt=r._malloc=function(){return(Nt=r._malloc=r.asm.M).apply(null,arguments)},ne=r._free=function(){return(ne=r._free=r.asm.N).apply(null,arguments)},tn=r.___getTypeName=function(){return(tn=r.___getTypeName=r.asm.P).apply(null,arguments)},er=r.___embind_register_native_and_builtin_types=function(){return(er=r.___embind_register_native_and_builtin_types=r.asm.Q).apply(null,arguments)},tr=r.dynCall_jiji=function(){return(tr=r.dynCall_jiji=r.asm.R).apply(null,arguments)},st;function nr(e){this.name="ExitStatus",this.message="Program terminated with exit("+e+")",this.status=e}Ve=function e(){st||Et(),st||(Ve=e)};function Et(e){if(e=e||L,Te>0||(Mn(),Te>0))return;function t(){st||(st=!0,r.calledRun=!0,!Ie&&(Kn(),Cn(),M(r),r.onRuntimeInitialized&&r.onRuntimeInitialized(),Fn()))}r.setStatus?(r.setStatus("Running..."),setTimeout(function(){setTimeout(function(){r.setStatus("")},1),t()},1)):t()}if(r.run=Et,r.preInit)for(typeof r.preInit=="function"&&(r.preInit=[r.preInit]);r.preInit.length>0;)r.preInit.pop()();return Et(),T.ready}}();typeof ot=="object"&&typeof pt=="object"?pt.exports=Pt:typeof define=="function"&&define.amd?define([],function(){return Pt}):typeof ot=="object"&&(ot.BASIS=Pt)});var C={UNSIGNED_BYTE:f.UNSIGNED_BYTE,UNSIGNED_SHORT:f.UNSIGNED_SHORT,UNSIGNED_INT:f.UNSIGNED_INT,FLOAT:f.FLOAT,HALF_FLOAT:f.HALF_FLOAT_OES,UNSIGNED_INT_24_8:f.UNSIGNED_INT_24_8,UNSIGNED_SHORT_4_4_4_4:f.UNSIGNED_SHORT_4_4_4_4,UNSIGNED_SHORT_5_5_5_1:f.UNSIGNED_SHORT_5_5_5_1,UNSIGNED_SHORT_5_6_5:f.UNSIGNED_SHORT_5_6_5};C.toWebGLConstant=function(s,T){switch(s){case C.UNSIGNED_BYTE:return f.UNSIGNED_BYTE;case C.UNSIGNED_SHORT:return f.UNSIGNED_SHORT;case C.UNSIGNED_INT:return f.UNSIGNED_INT;case C.FLOAT:return f.FLOAT;case C.HALF_FLOAT:return T.webgl2?f.HALF_FLOAT:f.HALF_FLOAT_OES;case C.UNSIGNED_INT_24_8:return f.UNSIGNED_INT_24_8;case C.UNSIGNED_SHORT_4_4_4_4:return f.UNSIGNED_SHORT_4_4_4_4;case C.UNSIGNED_SHORT_5_5_5_1:return f.UNSIGNED_SHORT_5_5_5_1;case C.UNSIGNED_SHORT_5_6_5:return C.UNSIGNED_SHORT_5_6_5}};C.isPacked=function(s){return s===C.UNSIGNED_INT_24_8||s===C.UNSIGNED_SHORT_4_4_4_4||s===C.UNSIGNED_SHORT_5_5_5_1||s===C.UNSIGNED_SHORT_5_6_5};C.sizeInBytes=function(s){switch(s){case C.UNSIGNED_BYTE:return 1;case C.UNSIGNED_SHORT:case C.UNSIGNED_SHORT_4_4_4_4:case C.UNSIGNED_SHORT_5_5_5_1:case C.UNSIGNED_SHORT_5_6_5:case C.HALF_FLOAT:return 2;case C.UNSIGNED_INT:case C.FLOAT:case C.UNSIGNED_INT_24_8:return 4}};C.validate=function(s){return s===C.UNSIGNED_BYTE||s===C.UNSIGNED_SHORT||s===C.UNSIGNED_INT||s===C.FLOAT||s===C.HALF_FLOAT||s===C.UNSIGNED_INT_24_8||s===C.UNSIGNED_SHORT_4_4_4_4||s===C.UNSIGNED_SHORT_5_5_5_1||s===C.UNSIGNED_SHORT_5_6_5};C.getTypedArrayConstructor=function(s){let T=C.sizeInBytes(s);return T===Uint8Array.BYTES_PER_ELEMENT?Uint8Array:T===Uint16Array.BYTES_PER_ELEMENT?Uint16Array:T===Float32Array.BYTES_PER_ELEMENT&&s===C.FLOAT?Float32Array:Uint32Array};var y=Object.freeze(C);var i={DEPTH_COMPONENT:f.DEPTH_COMPONENT,DEPTH_STENCIL:f.DEPTH_STENCIL,ALPHA:f.ALPHA,RED:f.RED,RG:f.RG,RGB:f.RGB,RGBA:f.RGBA,RED_INTEGER:f.RED_INTEGER,RG_INTEGER:f.RG_INTEGER,RGB_INTEGER:f.RGB_INTEGER,RGBA_INTEGER:f.RGBA_INTEGER,LUMINANCE:f.LUMINANCE,LUMINANCE_ALPHA:f.LUMINANCE_ALPHA,RGB_DXT1:f.COMPRESSED_RGB_S3TC_DXT1_EXT,RGBA_DXT1:f.COMPRESSED_RGBA_S3TC_DXT1_EXT,RGBA_DXT3:f.COMPRESSED_RGBA_S3TC_DXT3_EXT,RGBA_DXT5:f.COMPRESSED_RGBA_S3TC_DXT5_EXT,RGB_PVRTC_4BPPV1:f.COMPRESSED_RGB_PVRTC_4BPPV1_IMG,RGB_PVRTC_2BPPV1:f.COMPRESSED_RGB_PVRTC_2BPPV1_IMG,RGBA_PVRTC_4BPPV1:f.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG,RGBA_PVRTC_2BPPV1:f.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG,RGBA_ASTC:f.COMPRESSED_RGBA_ASTC_4x4_WEBGL,RGB_ETC1:f.COMPRESSED_RGB_ETC1_WEBGL,RGB8_ETC2:f.COMPRESSED_RGB8_ETC2,RGBA8_ETC2_EAC:f.COMPRESSED_RGBA8_ETC2_EAC,RGBA_BC7:f.COMPRESSED_RGBA_BPTC_UNORM};i.componentsLength=function(s){switch(s){case i.RGB:case i.RGB_INTEGER:return 3;case i.RGBA:case i.RGBA_INTEGER:return 4;case i.LUMINANCE_ALPHA:case i.RG:case i.RG_INTEGER:return 2;case i.ALPHA:case i.RED:case i.RED_INTEGER:case i.LUMINANCE:return 1;default:return 1}};i.validate=function(s){return s===i.DEPTH_COMPONENT||s===i.DEPTH_STENCIL||s===i.ALPHA||s===i.RED||s===i.RG||s===i.RGB||s===i.RGBA||s===i.RED_INTEGER||s===i.RG_INTEGER||s===i.RGB_INTEGER||s===i.RGBA_INTEGER||s===i.LUMINANCE||s===i.LUMINANCE_ALPHA||s===i.RGB_DXT1||s===i.RGBA_DXT1||s===i.RGBA_DXT3||s===i.RGBA_DXT5||s===i.RGB_PVRTC_4BPPV1||s===i.RGB_PVRTC_2BPPV1||s===i.RGBA_PVRTC_4BPPV1||s===i.RGBA_PVRTC_2BPPV1||s===i.RGBA_ASTC||s===i.RGB_ETC1||s===i.RGB8_ETC2||s===i.RGBA8_ETC2_EAC||s===i.RGBA_BC7};i.isColorFormat=function(s){return s===i.RED||s===i.ALPHA||s===i.RGB||s===i.RGBA||s===i.LUMINANCE||s===i.LUMINANCE_ALPHA};i.isDepthFormat=function(s){return s===i.DEPTH_COMPONENT||s===i.DEPTH_STENCIL};i.isCompressedFormat=function(s){return s===i.RGB_DXT1||s===i.RGBA_DXT1||s===i.RGBA_DXT3||s===i.RGBA_DXT5||s===i.RGB_PVRTC_4BPPV1||s===i.RGB_PVRTC_2BPPV1||s===i.RGBA_PVRTC_4BPPV1||s===i.RGBA_PVRTC_2BPPV1||s===i.RGBA_ASTC||s===i.RGB_ETC1||s===i.RGB8_ETC2||s===i.RGBA8_ETC2_EAC||s===i.RGBA_BC7};i.isDXTFormat=function(s){return s===i.RGB_DXT1||s===i.RGBA_DXT1||s===i.RGBA_DXT3||s===i.RGBA_DXT5};i.isPVRTCFormat=function(s){return s===i.RGB_PVRTC_4BPPV1||s===i.RGB_PVRTC_2BPPV1||s===i.RGBA_PVRTC_4BPPV1||s===i.RGBA_PVRTC_2BPPV1};i.isASTCFormat=function(s){return s===i.RGBA_ASTC};i.isETC1Format=function(s){return s===i.RGB_ETC1};i.isETC2Format=function(s){return s===i.RGB8_ETC2||s===i.RGBA8_ETC2_EAC};i.isBC7Format=function(s){return s===i.RGBA_BC7};i.compressedTextureSizeInBytes=function(s,T,r){switch(s){case i.RGB_DXT1:case i.RGBA_DXT1:case i.RGB_ETC1:case i.RGB8_ETC2:return Math.floor((T+3)/4)*Math.floor((r+3)/4)*8;case i.RGBA_DXT3:case i.RGBA_DXT5:case i.RGBA_ASTC:case i.RGBA8_ETC2_EAC:return Math.floor((T+3)/4)*Math.floor((r+3)/4)*16;case i.RGB_PVRTC_4BPPV1:case i.RGBA_PVRTC_4BPPV1:return Math.floor((Math.max(T,8)*Math.max(r,8)*4+7)/8);case i.RGB_PVRTC_2BPPV1:case i.RGBA_PVRTC_2BPPV1:return Math.floor((Math.max(T,16)*Math.max(r,8)*2+7)/8);case i.RGBA_BC7:return Math.ceil(T/4)*Math.ceil(r/4)*16;default:return 0}};i.textureSizeInBytes=function(s,T,r,M){let B=i.componentsLength(s);return y.isPacked(T)&&(B=1),B*y.sizeInBytes(T)*r*M};i.texture3DSizeInBytes=function(s,T,r,M,B){let E=i.componentsLength(s);return y.isPacked(T)&&(E=1),E*y.sizeInBytes(T)*r*M*B};i.alignmentInBytes=function(s,T,r){let M=i.textureSizeInBytes(s,T,r,1)%4;return M===0?4:M===2?2:1};i.createTypedArray=function(s,T,r,M){let B=y.getTypedArrayConstructor(T),E=i.componentsLength(s)*r*M;return new B(E)};i.flipY=function(s,T,r,M,B){if(B===1)return s;let E=i.createTypedArray(T,r,M,B),F=i.componentsLength(T),L=M*F;for(let D=0;D<B;++D){let b=D*M*F,N=(B-D-1)*M*F;for(let d=0;d<L;++d)E[N+d]=s[b+d]}return E};i.toInternalFormat=function(s,T,r){if(!r.webgl2)return s;if(s===i.DEPTH_STENCIL)return f.DEPTH24_STENCIL8;if(s===i.DEPTH_COMPONENT){if(T===y.UNSIGNED_SHORT)return f.DEPTH_COMPONENT16;if(T===y.UNSIGNED_INT)return f.DEPTH_COMPONENT24}if(T===y.FLOAT)switch(s){case i.RGBA:return f.RGBA32F;case i.RGB:return f.RGB32F;case i.RG:return f.RG32F;case i.RED:return f.R32F}if(T===y.HALF_FLOAT)switch(s){case i.RGBA:return f.RGBA16F;case i.RGB:return f.RGB16F;case i.RG:return f.RG16F;case i.RED:return f.R16F}if(T===y.UNSIGNED_BYTE)switch(s){case i.RGBA:return f.RGBA8;case i.RGB:return f.RGB8;case i.RG:return f.RG8;case i.RED:return f.R8}if(T===y.INT)switch(s){case i.RGBA_INTEGER:return f.RGBA32I;case i.RGB_INTEGER:return f.RGB32I;case i.RG_INTEGER:return f.RG32I;case i.RED_INTEGER:return f.R32I}if(T===y.UNSIGNED_INT)switch(s){case i.RGBA_INTEGER:return f.RGBA32UI;case i.RGB_INTEGER:return f.RGB32UI;case i.RG_INTEGER:return f.RG32UI;case i.RED_INTEGER:return f.R32UI}return s};var U=Object.freeze(i);var or={VK_FORMAT_UNDEFINED:0,VK_FORMAT_R4G4_UNORM_PACK8:1,VK_FORMAT_R4G4B4A4_UNORM_PACK16:2,VK_FORMAT_B4G4R4A4_UNORM_PACK16:3,VK_FORMAT_R5G6B5_UNORM_PACK16:4,VK_FORMAT_B5G6R5_UNORM_PACK16:5,VK_FORMAT_R5G5B5A1_UNORM_PACK16:6,VK_FORMAT_B5G5R5A1_UNORM_PACK16:7,VK_FORMAT_A1R5G5B5_UNORM_PACK16:8,VK_FORMAT_R8_UNORM:9,VK_FORMAT_R8_SNORM:10,VK_FORMAT_R8_USCALED:11,VK_FORMAT_R8_SSCALED:12,VK_FORMAT_R8_UINT:13,VK_FORMAT_R8_SINT:14,VK_FORMAT_R8_SRGB:15,VK_FORMAT_R8G8_UNORM:16,VK_FORMAT_R8G8_SNORM:17,VK_FORMAT_R8G8_USCALED:18,VK_FORMAT_R8G8_SSCALED:19,VK_FORMAT_R8G8_UINT:20,VK_FORMAT_R8G8_SINT:21,VK_FORMAT_R8G8_SRGB:22,VK_FORMAT_R8G8B8_UNORM:23,VK_FORMAT_R8G8B8_SNORM:24,VK_FORMAT_R8G8B8_USCALED:25,VK_FORMAT_R8G8B8_SSCALED:26,VK_FORMAT_R8G8B8_UINT:27,VK_FORMAT_R8G8B8_SINT:28,VK_FORMAT_R8G8B8_SRGB:29,VK_FORMAT_B8G8R8_UNORM:30,VK_FORMAT_B8G8R8_SNORM:31,VK_FORMAT_B8G8R8_USCALED:32,VK_FORMAT_B8G8R8_SSCALED:33,VK_FORMAT_B8G8R8_UINT:34,VK_FORMAT_B8G8R8_SINT:35,VK_FORMAT_B8G8R8_SRGB:36,VK_FORMAT_R8G8B8A8_UNORM:37,VK_FORMAT_R8G8B8A8_SNORM:38,VK_FORMAT_R8G8B8A8_USCALED:39,VK_FORMAT_R8G8B8A8_SSCALED:40,VK_FORMAT_R8G8B8A8_UINT:41,VK_FORMAT_R8G8B8A8_SINT:42,VK_FORMAT_R8G8B8A8_SRGB:43,VK_FORMAT_B8G8R8A8_UNORM:44,VK_FORMAT_B8G8R8A8_SNORM:45,VK_FORMAT_B8G8R8A8_USCALED:46,VK_FORMAT_B8G8R8A8_SSCALED:47,VK_FORMAT_B8G8R8A8_UINT:48,VK_FORMAT_B8G8R8A8_SINT:49,VK_FORMAT_B8G8R8A8_SRGB:50,VK_FORMAT_A8B8G8R8_UNORM_PACK32:51,VK_FORMAT_A8B8G8R8_SNORM_PACK32:52,VK_FORMAT_A8B8G8R8_USCALED_PACK32:53,VK_FORMAT_A8B8G8R8_SSCALED_PACK32:54,VK_FORMAT_A8B8G8R8_UINT_PACK32:55,VK_FORMAT_A8B8G8R8_SINT_PACK32:56,VK_FORMAT_A8B8G8R8_SRGB_PACK32:57,VK_FORMAT_A2R10G10B10_UNORM_PACK32:58,VK_FORMAT_A2R10G10B10_SNORM_PACK32:59,VK_FORMAT_A2R10G10B10_USCALED_PACK32:60,VK_FORMAT_A2R10G10B10_SSCALED_PACK32:61,VK_FORMAT_A2R10G10B10_UINT_PACK32:62,VK_FORMAT_A2R10G10B10_SINT_PACK32:63,VK_FORMAT_A2B10G10R10_UNORM_PACK32:64,VK_FORMAT_A2B10G10R10_SNORM_PACK32:65,VK_FORMAT_A2B10G10R10_USCALED_PACK32:66,VK_FORMAT_A2B10G10R10_SSCALED_PACK32:67,VK_FORMAT_A2B10G10R10_UINT_PACK32:68,VK_FORMAT_A2B10G10R10_SINT_PACK32:69,VK_FORMAT_R16_UNORM:70,VK_FORMAT_R16_SNORM:71,VK_FORMAT_R16_USCALED:72,VK_FORMAT_R16_SSCALED:73,VK_FORMAT_R16_UINT:74,VK_FORMAT_R16_SINT:75,VK_FORMAT_R16_SFLOAT:76,VK_FORMAT_R16G16_UNORM:77,VK_FORMAT_R16G16_SNORM:78,VK_FORMAT_R16G16_USCALED:79,VK_FORMAT_R16G16_SSCALED:80,VK_FORMAT_R16G16_UINT:81,VK_FORMAT_R16G16_SINT:82,VK_FORMAT_R16G16_SFLOAT:83,VK_FORMAT_R16G16B16_UNORM:84,VK_FORMAT_R16G16B16_SNORM:85,VK_FORMAT_R16G16B16_USCALED:86,VK_FORMAT_R16G16B16_SSCALED:87,VK_FORMAT_R16G16B16_UINT:88,VK_FORMAT_R16G16B16_SINT:89,VK_FORMAT_R16G16B16_SFLOAT:90,VK_FORMAT_R16G16B16A16_UNORM:91,VK_FORMAT_R16G16B16A16_SNORM:92,VK_FORMAT_R16G16B16A16_USCALED:93,VK_FORMAT_R16G16B16A16_SSCALED:94,VK_FORMAT_R16G16B16A16_UINT:95,VK_FORMAT_R16G16B16A16_SINT:96,VK_FORMAT_R16G16B16A16_SFLOAT:97,VK_FORMAT_R32_UINT:98,VK_FORMAT_R32_SINT:99,VK_FORMAT_R32_SFLOAT:100,VK_FORMAT_R32G32_UINT:101,VK_FORMAT_R32G32_SINT:102,VK_FORMAT_R32G32_SFLOAT:103,VK_FORMAT_R32G32B32_UINT:104,VK_FORMAT_R32G32B32_SINT:105,VK_FORMAT_R32G32B32_SFLOAT:106,VK_FORMAT_R32G32B32A32_UINT:107,VK_FORMAT_R32G32B32A32_SINT:108,VK_FORMAT_R32G32B32A32_SFLOAT:109,VK_FORMAT_R64_UINT:110,VK_FORMAT_R64_SINT:111,VK_FORMAT_R64_SFLOAT:112,VK_FORMAT_R64G64_UINT:113,VK_FORMAT_R64G64_SINT:114,VK_FORMAT_R64G64_SFLOAT:115,VK_FORMAT_R64G64B64_UINT:116,VK_FORMAT_R64G64B64_SINT:117,VK_FORMAT_R64G64B64_SFLOAT:118,VK_FORMAT_R64G64B64A64_UINT:119,VK_FORMAT_R64G64B64A64_SINT:120,VK_FORMAT_R64G64B64A64_SFLOAT:121,VK_FORMAT_B10G11R11_UFLOAT_PACK32:122,VK_FORMAT_E5B9G9R9_UFLOAT_PACK32:123,VK_FORMAT_D16_UNORM:124,VK_FORMAT_X8_D24_UNORM_PACK32:125,VK_FORMAT_D32_SFLOAT:126,VK_FORMAT_S8_UINT:127,VK_FORMAT_D16_UNORM_S8_UINT:128,VK_FORMAT_D24_UNORM_S8_UINT:129,VK_FORMAT_D32_SFLOAT_S8_UINT:130,VK_FORMAT_BC1_RGB_UNORM_BLOCK:131,VK_FORMAT_BC1_RGB_SRGB_BLOCK:132,VK_FORMAT_BC1_RGBA_UNORM_BLOCK:133,VK_FORMAT_BC1_RGBA_SRGB_BLOCK:134,VK_FORMAT_BC2_UNORM_BLOCK:135,VK_FORMAT_BC2_SRGB_BLOCK:136,VK_FORMAT_BC3_UNORM_BLOCK:137,VK_FORMAT_BC3_SRGB_BLOCK:138,VK_FORMAT_BC4_UNORM_BLOCK:139,VK_FORMAT_BC4_SNORM_BLOCK:140,VK_FORMAT_BC5_UNORM_BLOCK:141,VK_FORMAT_BC5_SNORM_BLOCK:142,VK_FORMAT_BC6H_UFLOAT_BLOCK:143,VK_FORMAT_BC6H_SFLOAT_BLOCK:144,VK_FORMAT_BC7_UNORM_BLOCK:145,VK_FORMAT_BC7_SRGB_BLOCK:146,VK_FORMAT_ETC2_R8G8B8_UNORM_BLOCK:147,VK_FORMAT_ETC2_R8G8B8_SRGB_BLOCK:148,VK_FORMAT_ETC2_R8G8B8A1_UNORM_BLOCK:149,VK_FORMAT_ETC2_R8G8B8A1_SRGB_BLOCK:150,VK_FORMAT_ETC2_R8G8B8A8_UNORM_BLOCK:151,VK_FORMAT_ETC2_R8G8B8A8_SRGB_BLOCK:152,VK_FORMAT_EAC_R11_UNORM_BLOCK:153,VK_FORMAT_EAC_R11_SNORM_BLOCK:154,VK_FORMAT_EAC_R11G11_UNORM_BLOCK:155,VK_FORMAT_EAC_R11G11_SNORM_BLOCK:156,VK_FORMAT_ASTC_4x4_UNORM_BLOCK:157,VK_FORMAT_ASTC_4x4_SRGB_BLOCK:158,VK_FORMAT_ASTC_5x4_UNORM_BLOCK:159,VK_FORMAT_ASTC_5x4_SRGB_BLOCK:160,VK_FORMAT_ASTC_5x5_UNORM_BLOCK:161,VK_FORMAT_ASTC_5x5_SRGB_BLOCK:162,VK_FORMAT_ASTC_6x5_UNORM_BLOCK:163,VK_FORMAT_ASTC_6x5_SRGB_BLOCK:164,VK_FORMAT_ASTC_6x6_UNORM_BLOCK:165,VK_FORMAT_ASTC_6x6_SRGB_BLOCK:166,VK_FORMAT_ASTC_8x5_UNORM_BLOCK:167,VK_FORMAT_ASTC_8x5_SRGB_BLOCK:168,VK_FORMAT_ASTC_8x6_UNORM_BLOCK:169,VK_FORMAT_ASTC_8x6_SRGB_BLOCK:170,VK_FORMAT_ASTC_8x8_UNORM_BLOCK:171,VK_FORMAT_ASTC_8x8_SRGB_BLOCK:172,VK_FORMAT_ASTC_10x5_UNORM_BLOCK:173,VK_FORMAT_ASTC_10x5_SRGB_BLOCK:174,VK_FORMAT_ASTC_10x6_UNORM_BLOCK:175,VK_FORMAT_ASTC_10x6_SRGB_BLOCK:176,VK_FORMAT_ASTC_10x8_UNORM_BLOCK:177,VK_FORMAT_ASTC_10x8_SRGB_BLOCK:178,VK_FORMAT_ASTC_10x10_UNORM_BLOCK:179,VK_FORMAT_ASTC_10x10_SRGB_BLOCK:180,VK_FORMAT_ASTC_12x10_UNORM_BLOCK:181,VK_FORMAT_ASTC_12x10_SRGB_BLOCK:182,VK_FORMAT_ASTC_12x12_UNORM_BLOCK:183,VK_FORMAT_ASTC_12x12_SRGB_BLOCK:184,VK_FORMAT_G8B8G8R8_422_UNORM:1000156e3,VK_FORMAT_B8G8R8G8_422_UNORM:1000156001,VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM:1000156002,VK_FORMAT_G8_B8R8_2PLANE_420_UNORM:1000156003,VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM:1000156004,VK_FORMAT_G8_B8R8_2PLANE_422_UNORM:1000156005,VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM:1000156006,VK_FORMAT_R10X6_UNORM_PACK16:1000156007,VK_FORMAT_R10X6G10X6_UNORM_2PACK16:1000156008,VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16:1000156009,VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16:1000156010,VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16:1000156011,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16:1000156012,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16:1000156013,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16:1000156014,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16:1000156015,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16:1000156016,VK_FORMAT_R12X4_UNORM_PACK16:1000156017,VK_FORMAT_R12X4G12X4_UNORM_2PACK16:1000156018,VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16:1000156019,VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16:1000156020,VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16:1000156021,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16:1000156022,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16:1000156023,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16:1000156024,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16:1000156025,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16:1000156026,VK_FORMAT_G16B16G16R16_422_UNORM:1000156027,VK_FORMAT_B16G16R16G16_422_UNORM:1000156028,VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM:1000156029,VK_FORMAT_G16_B16R16_2PLANE_420_UNORM:1000156030,VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM:1000156031,VK_FORMAT_G16_B16R16_2PLANE_422_UNORM:1000156032,VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM:1000156033,VK_FORMAT_PVRTC1_2BPP_UNORM_BLOCK_IMG:1000054e3,VK_FORMAT_PVRTC1_4BPP_UNORM_BLOCK_IMG:1000054001,VK_FORMAT_PVRTC2_2BPP_UNORM_BLOCK_IMG:1000054002,VK_FORMAT_PVRTC2_4BPP_UNORM_BLOCK_IMG:1000054003,VK_FORMAT_PVRTC1_2BPP_SRGB_BLOCK_IMG:1000054004,VK_FORMAT_PVRTC1_4BPP_SRGB_BLOCK_IMG:1000054005,VK_FORMAT_PVRTC2_2BPP_SRGB_BLOCK_IMG:1000054006,VK_FORMAT_PVRTC2_4BPP_SRGB_BLOCK_IMG:1000054007,VK_FORMAT_ASTC_4x4_SFLOAT_BLOCK_EXT:1000066e3,VK_FORMAT_ASTC_5x4_SFLOAT_BLOCK_EXT:1000066001,VK_FORMAT_ASTC_5x5_SFLOAT_BLOCK_EXT:1000066002,VK_FORMAT_ASTC_6x5_SFLOAT_BLOCK_EXT:1000066003,VK_FORMAT_ASTC_6x6_SFLOAT_BLOCK_EXT:1000066004,VK_FORMAT_ASTC_8x5_SFLOAT_BLOCK_EXT:1000066005,VK_FORMAT_ASTC_8x6_SFLOAT_BLOCK_EXT:1000066006,VK_FORMAT_ASTC_8x8_SFLOAT_BLOCK_EXT:1000066007,VK_FORMAT_ASTC_10x5_SFLOAT_BLOCK_EXT:1000066008,VK_FORMAT_ASTC_10x6_SFLOAT_BLOCK_EXT:1000066009,VK_FORMAT_ASTC_10x8_SFLOAT_BLOCK_EXT:1000066010,VK_FORMAT_ASTC_10x10_SFLOAT_BLOCK_EXT:1000066011,VK_FORMAT_ASTC_12x10_SFLOAT_BLOCK_EXT:1000066012,VK_FORMAT_ASTC_12x12_SFLOAT_BLOCK_EXT:1000066013,VK_FORMAT_G8B8G8R8_422_UNORM_KHR:1000156e3,VK_FORMAT_B8G8R8G8_422_UNORM_KHR:1000156001,VK_FORMAT_G8_B8_R8_3PLANE_420_UNORM_KHR:1000156002,VK_FORMAT_G8_B8R8_2PLANE_420_UNORM_KHR:1000156003,VK_FORMAT_G8_B8_R8_3PLANE_422_UNORM_KHR:1000156004,VK_FORMAT_G8_B8R8_2PLANE_422_UNORM_KHR:1000156005,VK_FORMAT_G8_B8_R8_3PLANE_444_UNORM_KHR:1000156006,VK_FORMAT_R10X6_UNORM_PACK16_KHR:1000156007,VK_FORMAT_R10X6G10X6_UNORM_2PACK16_KHR:1000156008,VK_FORMAT_R10X6G10X6B10X6A10X6_UNORM_4PACK16_KHR:1000156009,VK_FORMAT_G10X6B10X6G10X6R10X6_422_UNORM_4PACK16_KHR:1000156010,VK_FORMAT_B10X6G10X6R10X6G10X6_422_UNORM_4PACK16_KHR:1000156011,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_420_UNORM_3PACK16_KHR:1000156012,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_420_UNORM_3PACK16_KHR:1000156013,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_422_UNORM_3PACK16_KHR:1000156014,VK_FORMAT_G10X6_B10X6R10X6_2PLANE_422_UNORM_3PACK16_KHR:1000156015,VK_FORMAT_G10X6_B10X6_R10X6_3PLANE_444_UNORM_3PACK16_KHR:1000156016,VK_FORMAT_R12X4_UNORM_PACK16_KHR:1000156017,VK_FORMAT_R12X4G12X4_UNORM_2PACK16_KHR:1000156018,VK_FORMAT_R12X4G12X4B12X4A12X4_UNORM_4PACK16_KHR:1000156019,VK_FORMAT_G12X4B12X4G12X4R12X4_422_UNORM_4PACK16_KHR:1000156020,VK_FORMAT_B12X4G12X4R12X4G12X4_422_UNORM_4PACK16_KHR:1000156021,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_420_UNORM_3PACK16_KHR:1000156022,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_420_UNORM_3PACK16_KHR:1000156023,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_422_UNORM_3PACK16_KHR:1000156024,VK_FORMAT_G12X4_B12X4R12X4_2PLANE_422_UNORM_3PACK16_KHR:1000156025,VK_FORMAT_G12X4_B12X4_R12X4_3PLANE_444_UNORM_3PACK16_KHR:1000156026,VK_FORMAT_G16B16G16R16_422_UNORM_KHR:1000156027,VK_FORMAT_B16G16R16G16_422_UNORM_KHR:1000156028,VK_FORMAT_G16_B16_R16_3PLANE_420_UNORM_KHR:1000156029,VK_FORMAT_G16_B16R16_2PLANE_420_UNORM_KHR:1000156030,VK_FORMAT_G16_B16_R16_3PLANE_422_UNORM_KHR:1000156031,VK_FORMAT_G16_B16R16_2PLANE_422_UNORM_KHR:1000156032,VK_FORMAT_G16_B16_R16_3PLANE_444_UNORM_KHR:1000156033},me=Object.freeze(or);function ir(){return{vkFormat:0,typeSize:1,pixelWidth:0,pixelHeight:0,pixelDepth:0,layerCount:0,faceCount:1,supercompressionScheme:0,levels:[],dataFormatDescriptor:[{vendorId:0,descriptorType:0,versionNumber:2,colorModel:0,colorPrimaries:1,transferFunction:2,flags:0,texelBlockDimension:[0,0,0,0],bytesPlane:[0,0,0,0,0,0,0,0],samples:[]}],keyValue:{},globalData:null}}var fe=class{constructor(T,r,M,B){this._dataView=void 0,this._littleEndian=void 0,this._offset=void 0,this._dataView=new DataView(T.buffer,T.byteOffset+r,M),this._littleEndian=B,this._offset=0}_nextUint8(){let T=this._dataView.getUint8(this._offset);return this._offset+=1,T}_nextUint16(){let T=this._dataView.getUint16(this._offset,this._littleEndian);return this._offset+=2,T}_nextUint32(){let T=this._dataView.getUint32(this._offset,this._littleEndian);return this._offset+=4,T}_nextUint64(){let T=this._dataView.getUint32(this._offset,this._littleEndian),r=this._dataView.getUint32(this._offset+4,this._littleEndian),M=T+2**32*r;return this._offset+=8,M}_nextInt32(){let T=this._dataView.getInt32(this._offset,this._littleEndian);return this._offset+=4,T}_nextUint8Array(T){let r=new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+this._offset,T);return this._offset+=T,r}_skip(T){return this._offset+=T,this}_scan(T,r=0){let M=this._offset,B=0;for(;this._dataView.getUint8(this._offset)!==r&&B<T;)B++,this._offset++;return B<T&&this._offset++,new Uint8Array(this._dataView.buffer,this._dataView.byteOffset+M,B)}};var Fr=new Uint8Array([0]),H=[171,75,84,88,32,50,48,187,13,10,26,10];function Rn(s){return new TextDecoder().decode(s)}function an(s){let T=new Uint8Array(s.buffer,s.byteOffset,H.length);if(T[0]!==H[0]||T[1]!==H[1]||T[2]!==H[2]||T[3]!==H[3]||T[4]!==H[4]||T[5]!==H[5]||T[6]!==H[6]||T[7]!==H[7]||T[8]!==H[8]||T[9]!==H[9]||T[10]!==H[10]||T[11]!==H[11])throw new Error("Missing KTX 2.0 identifier.");let r=ir(),M=17*Uint32Array.BYTES_PER_ELEMENT,B=new fe(s,H.length,M,!0);r.vkFormat=B._nextUint32(),r.typeSize=B._nextUint32(),r.pixelWidth=B._nextUint32(),r.pixelHeight=B._nextUint32(),r.pixelDepth=B._nextUint32(),r.layerCount=B._nextUint32(),r.faceCount=B._nextUint32();let E=B._nextUint32();r.supercompressionScheme=B._nextUint32();let F=B._nextUint32(),L=B._nextUint32(),D=B._nextUint32(),b=B._nextUint32(),N=B._nextUint64(),d=B._nextUint64(),P=E*3*8,Q=new fe(s,H.length+M,P,!0);for(let v=0;v<E;v++)r.levels.push({levelData:new Uint8Array(s.buffer,s.byteOffset+Q._nextUint64(),Q._nextUint64()),uncompressedByteLength:Q._nextUint64()});let O=new fe(s,F,L,!0);O._skip(4);let W=O._nextUint16(),J=O._nextUint16(),ae=O._nextUint16(),j=O._nextUint16(),Ke=O._nextUint8(),Ce=O._nextUint8(),Fe=O._nextUint8(),it=O._nextUint8(),se=[O._nextUint8(),O._nextUint8(),O._nextUint8(),O._nextUint8()],Rt=[O._nextUint8(),O._nextUint8(),O._nextUint8(),O._nextUint8(),O._nextUint8(),O._nextUint8(),O._nextUint8(),O._nextUint8()],oe={vendorId:W,descriptorType:J,versionNumber:ae,colorModel:Ke,colorPrimaries:Ce,transferFunction:Fe,flags:it,texelBlockDimension:se,bytesPlane:Rt,samples:[]},Ie=(j/4-6)/4;for(let v=0;v<Ie;v++){let p={bitOffset:O._nextUint16(),bitLength:O._nextUint8(),channelType:O._nextUint8(),samplePosition:[O._nextUint8(),O._nextUint8(),O._nextUint8(),O._nextUint8()],sampleLower:Number.NEGATIVE_INFINITY,sampleUpper:Number.POSITIVE_INFINITY};p.channelType&64?(p.sampleLower=O._nextInt32(),p.sampleUpper=O._nextInt32()):(p.sampleLower=O._nextUint32(),p.sampleUpper=O._nextUint32()),oe.samples[v]=p}r.dataFormatDescriptor.length=0,r.dataFormatDescriptor.push(oe);let ce=new fe(s,D,b,!0);for(;ce._offset<b;){let v=ce._nextUint32(),p=ce._scan(v),k=Rn(p);if(r.keyValue[k]=ce._nextUint8Array(v-p.byteLength-1),k.match(/^ktx/i)){let m=Rn(r.keyValue[k]);r.keyValue[k]=m.substring(0,m.lastIndexOf("\0"))}let Ae=v%4?4-v%4:0;ce._skip(Ae)}if(d<=0)return r;let I=new fe(s,N,d,!0),be=I._nextUint16(),we=I._nextUint16(),Ge=I._nextUint32(),He=I._nextUint32(),Xe=I._nextUint32(),at=I._nextUint32(),Se=[];for(let v=0;v<E;v++)Se.push({imageFlags:I._nextUint32(),rgbSliceByteOffset:I._nextUint32(),rgbSliceByteLength:I._nextUint32(),alphaSliceByteOffset:I._nextUint32(),alphaSliceByteLength:I._nextUint32()});let xe=N+I._offset,ke=xe+Ge,$e=ke+He,ct=$e+Xe,At=new Uint8Array(s.buffer,s.byteOffset+xe,Ge),Tt=new Uint8Array(s.buffer,s.byteOffset+ke,He),ut=new Uint8Array(s.buffer,s.byteOffset+$e,Xe),Ne=new Uint8Array(s.buffer,s.byteOffset+ct,at);return r.globalData={endpointCount:be,selectorCount:we,imageDescs:Se,endpointsData:At,selectorsData:Tt,tablesData:ut,extendedData:Ne},r}var An=sr(cn(),1),Tn=["positiveX","negativeX","positiveY","negativeY","positiveZ","negativeZ"],un=163,fn=166,De;function Rr(s,T){sn.typeOf.object("transcoderModule",De);let r=s.ktx2Buffer,M=s.supportedTargetFormats,B;try{B=an(r)}catch{throw new re("Invalid KTX2 file.")}if(B.layerCount!==0)throw new re("KTX2 texture arrays are not supported.");if(B.pixelDepth!==0)throw new re("KTX2 3D textures are unsupported.");let E=B.dataFormatDescriptor[0],F=new Array(B.levelCount);return B.vkFormat===0&&(E.colorModel===un||E.colorModel===fn)?cr(r,B,M,De,T,F):(T.push(r.buffer),ar(B,F)),F}function ar(s,T){let r=s.vkFormat===me.VK_FORMAT_R8G8B8_SRGB?U.RGB:U.RGBA,M;s.vkFormat===me.VK_FORMAT_R8G8B8A8_UNORM?M=y.UNSIGNED_BYTE:s.vkFormat===me.VK_FORMAT_R16G16B16A16_SFLOAT?M=y.HALF_FLOAT:s.vkFormat===me.VK_FORMAT_R32G32B32A32_SFLOAT&&(M=y.FLOAT);for(let B=0;B<s.levels.length;++B){let E={};T[B]=E;let F=s.levels[B].levelData,L=s.pixelWidth>>B,D=s.pixelHeight>>B,b=L*D*U.componentsLength(r);for(let N=0;N<s.faceCount;++N){let d=F.byteOffset+b*s.typeSize*N,P;!Le(M)||y.sizeInBytes(M)===1?P=new Uint8Array(F.buffer,d,b):y.sizeInBytes(M)===2?P=new Uint16Array(F.buffer,d,b):P=new Float32Array(F.buffer,d,b),E[Tn[N]]={internalFormat:r,datatype:M,width:L,height:D,levelBuffer:P}}}}function cr(s,T,r,M,B,E){let F=new M.KTX2File(s),L=F.getWidth(),D=F.getHeight(),b=F.getLevels(),N=F.getHasAlpha();if(!(L>0)||!(D>0)||!(b>0))throw F.close(),F.delete(),new re("Invalid KTX2 file");let d,P,Q=T.dataFormatDescriptor[0],O=M.transcoder_texture_format;if(Q.colorModel===un)if(r.etc)d=N?U.RGBA8_ETC2_EAC:U.RGB8_ETC2,P=N?O.cTFETC2_RGBA:O.cTFETC1_RGB;else if(r.etc1&&!N)d=U.RGB_ETC1,P=O.cTFETC1_RGB;else if(r.s3tc)d=N?U.RGBA_DXT5:U.RGB_DXT1,P=N?O.cTFBC3_RGBA:O.cTFBC1_RGB;else if(r.pvrtc)d=N?U.RGBA_PVRTC_4BPPV1:U.RGB_PVRTC_4BPPV1,P=N?O.cTFPVRTC1_4_RGBA:O.cTFPVRTC1_4_RGB;else if(r.astc)d=U.RGBA_ASTC,P=O.cTFASTC_4x4_RGBA;else if(r.bc7)d=U.RGBA_BC7,P=O.cTFBC7_RGBA;else throw new re("No transcoding format target available for ETC1S compressed ktx2.");else if(Q.colorModel===fn)if(r.astc)d=U.RGBA_ASTC,P=O.cTFASTC_4x4_RGBA;else if(r.bc7)d=U.RGBA_BC7,P=O.cTFBC7_RGBA;else if(r.s3tc)d=N?U.RGBA_DXT5:U.RGB_DXT1,P=N?O.cTFBC3_RGBA:O.cTFBC1_RGB;else if(r.etc)d=N?U.RGBA8_ETC2_EAC:U.RGB8_ETC2,P=N?O.cTFETC2_RGBA:O.cTFETC1_RGB;else if(r.etc1&&!N)d=U.RGB_ETC1,P=O.cTFETC1_RGB;else if(r.pvrtc)d=N?U.RGBA_PVRTC_4BPPV1:U.RGB_PVRTC_4BPPV1,P=N?O.cTFPVRTC1_4_RGBA:O.cTFPVRTC1_4_RGB;else throw new re("No transcoding format target available for UASTC compressed ktx2.");if(!F.startTranscoding())throw F.close(),F.delete(),new re("startTranscoding() failed");for(let W=0;W<T.levels.length;++W){let J={};E[W]=J,L=T.pixelWidth>>W,D=T.pixelHeight>>W;let ae=F.getImageTranscodedSizeInBytes(W,0,0,P.value),j=new Uint8Array(ae),Ke=F.transcodeImage(j,W,0,0,P.value,0,-1,-1);if(!Le(Ke))throw new re("transcodeImage() failed.");B.push(j.buffer),J[Tn[0]]={internalFormat:d,width:L,height:D,levelBuffer:j}}return F.close(),F.delete(),E}async function Ar(s,T){let r=s.webAssemblyConfig,M=An.default??self.BASIS;return Le(r.wasmBinaryFile)?De=await M(r):De=await M(),De.initializeBasis(),!0}function Tr(s,T){let r=s.webAssemblyConfig;return Le(r)?Ar(s,T):Rr(s,T)}var vr=on(Tr);export{vr as default};
