#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络连接测试脚本
用于测试容器内的网络连接和代理配置
"""

import requests
import os
import sys

def test_direct_connection():
    """测试直接连接"""
    print("🔍 测试直接连接...")
    try:
        response = requests.get('https://www.google.com', timeout=10)
        print(f"✅ 直接连接成功: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 直接连接失败: {e}")
        return False

def test_proxy_connection():
    """测试代理连接"""
    print("🔍 测试代理连接...")
    
    # 从环境变量或默认值获取代理
    http_proxy = os.getenv('HTTP_PROXY', 'http://127.0.0.1:7890')
    https_proxy = os.getenv('HTTPS_PROXY', 'http://127.0.0.1:7890')
    
    proxies = {
        'http': http_proxy,
        'https': https_proxy
    }
    
    print(f"代理配置: {proxies}")
    
    try:
        response = requests.get('https://www.google.com', proxies=proxies, timeout=10)
        print(f"✅ 代理连接成功: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 代理连接失败: {e}")
        return False

def test_google_news():
    """测试Google News RSS"""
    print("🔍 测试Google News RSS...")
    
    # 从环境变量获取代理配置
    http_proxy = os.getenv('HTTP_PROXY', 'http://127.0.0.1:7890')
    https_proxy = os.getenv('HTTPS_PROXY', 'http://127.0.0.1:7890')
    
    proxies = {
        'http': http_proxy,
        'https': https_proxy
    } if http_proxy and https_proxy else None
    
    url = "https://news.google.com/rss/search?q=south+china+sea&hl=en-US&gl=US&ceid=US:en"
    
    try:
        response = requests.get(url, proxies=proxies, timeout=15)
        print(f"✅ Google News RSS访问成功: {response.status_code}")
        print(f"响应长度: {len(response.text)} 字符")
        return True
    except Exception as e:
        print(f"❌ Google News RSS访问失败: {e}")
        return False

def test_baidu_translate():
    """测试百度翻译API"""
    print("🔍 测试百度翻译API...")
    
    url = "http://api.fanyi.baidu.com/api/trans/vip/translate"
    
    try:
        # 只测试连接，不发送实际翻译请求
        response = requests.get(url, timeout=10)
        print(f"✅ 百度翻译API连接成功: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ 百度翻译API连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始网络连接测试...")
    print("=" * 50)
    
    # 显示环境信息
    print(f"当前工作目录: {os.getcwd()}")
    print(f"HTTP_PROXY: {os.getenv('HTTP_PROXY', '未设置')}")
    print(f"HTTPS_PROXY: {os.getenv('HTTPS_PROXY', '未设置')}")
    print("=" * 50)
    
    tests = [
        ("直接连接", test_direct_connection),
        ("代理连接", test_proxy_connection),
        ("Google News RSS", test_google_news),
        ("百度翻译API", test_baidu_translate)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n📋 {name} 测试:")
        result = test_func()
        results.append((name, result))
        print("-" * 30)
    
    # 总结
    print("\n📊 测试结果总结:")
    print("=" * 50)
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
    
    # 建议
    print("\n💡 建议:")
    if not any(result for _, result in results):
        print("❌ 所有测试都失败了，请检查网络配置")
    elif results[0][1]:  # 直接连接成功
        print("✅ 直接连接可用，可能不需要代理")
    elif results[1][1]:  # 代理连接成功
        print("✅ 代理连接可用，建议使用代理配置")
    else:
        print("⚠️ 网络连接有问题，请检查代理设置")

if __name__ == "__main__":
    main()
