# 新闻可视化系统 🌍

一个基于 Docker 的新闻获取和 3D 地球可视化系统，实时展示南海相关新闻。

## ✨ 特性

- 🌐 **3D地球可视化**: 基于 Cesium 的交互式地球展示
- 📰 **实时新闻获取**: 自动从 Google News RSS 获取最新新闻
- 🔄 **自动翻译**: 集成百度翻译API，支持中英文双语
- 🗺️ **地理定位**: 根据新闻来源自动标注地理位置
- 🐳 **Docker部署**: 一键部署，开箱即用
- 📊 **数据持久化**: MySQL数据库存储，支持数据备份

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue.js)  │    │  后端API (Node) │    │ 爬虫 (Python)   │
│   Port: 80      │────│   Port: 3001    │────│   定时任务       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  MySQL 数据库    │
                    │   Port: 3306    │
                    └─────────────────┘
```

## 🚀 快速开始

### 前置要求

- Docker 20.10+
- Docker Compose 2.0+
- 百度翻译API账号

### 一键部署

#### Linux/macOS
```bash
git clone <your-repo-url>
cd s_cn_sea
chmod +x deploy.sh
./deploy.sh
```

#### Windows
```cmd
git clone <your-repo-url>
cd s_cn_sea
deploy.bat
```

### 本地测试部署

1. **配置环境变量（可选）**
```bash
# 如果需要自定义配置，复制环境配置
cp .env.example .env
# 编辑 .env 文件，配置数据库密码和百度翻译API
# 注意：代码中已有默认配置，可以直接跳过此步骤
```

2. **启动服务**
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志（可选）
docker-compose logs -f
```

3. **访问系统**
- 前端: http://localhost:8084
- 后端API: http://localhost:3001
- 数据库: localhost:3306

4. **测试验证**
```bash
# 检查后端API
curl http://localhost:3001/api/status

# 检查前端
curl http://localhost:8084/health

# 检查数据库初始化
docker-compose logs backend | grep "数据库初始化"

# 手动运行爬虫测试
docker-compose exec crawler python main.py

# 检查数据库数据
docker-compose exec mysql mysql -u root -p news_system
# 然后执行: SHOW TABLES; SELECT COUNT(*) FROM news;
```

## 📁 项目结构

```
s_cn_sea/
├── backend/                 # 后端服务
│   ├── RSS2NEWS-v2/        # Python爬虫
│   ├── server.js           # Node.js API服务
│   ├── Dockerfile          # 后端Docker配置
│   └── package.json        # Node.js依赖
├── frontend/               # 前端服务
│   ├── src/               # Vue.js源码
│   ├── Dockerfile         # 前端Docker配置
│   └── package.json       # 前端依赖
├── docker/                # Docker配置
│   ├── mysql/             # MySQL配置和初始化脚本
│   └── nginx/             # Nginx配置
├── docker-compose.yml     # Docker Compose配置
├── .env.example          # 环境变量模板
├── deploy.sh             # Linux部署脚本
├── deploy.bat            # Windows部署脚本
└── DEPLOYMENT.md         # 详细部署文档
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 必填 |
|--------|------|------|
| `DB_PASSWORD` | 数据库密码 | ✅ |
| `BAIDU_APP_ID` | 百度翻译APP ID | ✅ |
| `BAIDU_SECRET_KEY` | 百度翻译密钥 | ✅ |
| `HTTP_PROXY` | HTTP代理 | ❌ |
| `HTTPS_PROXY` | HTTPS代理 | ❌ |

### 百度翻译API配置

1. 访问 [百度翻译开放平台](https://fanyi-api.baidu.com/)
2. 注册账号并创建应用
3. 获取 APP ID 和密钥
4. 在 `.env` 文件中配置

## 📊 功能模块

### 1. 新闻爬虫 (Python)
- 定时获取 Google News RSS
- 解析新闻链接和内容
- 自动翻译标题和内容
- 地理位置标注

### 2. 后端API (Node.js)
- RESTful API接口
- 数据库操作
- 新闻数据管理
- 国家和域名映射

### 3. 前端展示 (Vue.js + Cesium)
- 3D地球可视化
- 新闻点位标注
- 交互式地图操作
- 响应式设计

### 4. 数据存储 (MySQL)
- 新闻数据存储
- 多语言支持
- 地理信息管理
- 数据关系维护

## 🛠️ 管理命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f [service_name]

# 重启服务
docker-compose restart [service_name]

# 停止服务
docker-compose down

# 手动运行爬虫
docker-compose exec crawler python main.py

# 数据库备份
docker-compose exec mysql mysqldump -u root -p news_system > backup.sql
```

## 📈 监控和维护

- **健康检查**: 内置服务健康检查
- **日志管理**: 集中化日志收集
- **数据备份**: 自动化备份方案
- **性能监控**: 资源使用监控

## 🔒 安全建议

- 修改默认数据库密码
- 配置防火墙规则
- 使用HTTPS证书
- 定期更新依赖包

## 📚 文档

- [详细部署文档](DEPLOYMENT.md)
- [API接口文档](backend/README.md)
- [前端开发文档](frontend/README.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🆘 技术支持

如遇问题，请：
1. 查看 [部署文档](DEPLOYMENT.md)
2. 检查服务日志
3. 提交 Issue

---

**注意**: 请确保在生产环境中使用强密码，并定期备份数据。