(function(){"use strict";const{Array:te,Object:m,Number:ne,Math:I,Error:S,Uint8Array:w,Uint16Array:et,Uint32Array:se,Int32Array:tt,Map:re,DataView:W,Promise:M,TextEncoder:ae,crypto:B,postMessage:K,TransformStream:v,ReadableStream:Ke,WritableStream:Te,CompressionStream:nt,DecompressionStream:st}=self,j=void 0,N="undefined",O="function";class oe{constructor(e){return class extends v{constructor(n,s){const r=new e(s);super({transform(a,o){o.enqueue(r.append(a))},flush(a){const o=r.flush();o&&a.enqueue(o)}})}}}}const ie=[];for(let t=0;256>t;t++){let e=t;for(let n=0;8>n;n++)1&e?e=e>>>1^3988292384:e>>>=1;ie[t]=e}class G{constructor(e){this.crc=e||-1}append(e){let n=0|this.crc;for(let s=0,r=0|e.length;r>s;s++)n=n>>>8^ie[255&(n^e[s])];this.crc=n}get(){return~this.crc}}class ce extends v{constructor(){let e;const n=new G;super({transform(s,r){n.append(s),r.enqueue(s)},flush(){const s=new w(4);new W(s.buffer).setUint32(0,n.get()),e.value=s}}),e=this}}const _={concat(t,e){if(t.length===0||e.length===0)return t.concat(e);const n=t[t.length-1],s=_.getPartial(n);return s===32?t.concat(e):_._shiftRight(e,s,0|n,t.slice(0,t.length-1))},bitLength(t){const e=t.length;if(e===0)return 0;const n=t[e-1];return 32*(e-1)+_.getPartial(n)},clamp(t,e){if(32*t.length<e)return t;const n=(t=t.slice(0,I.ceil(e/32))).length;return e&=31,n>0&&e&&(t[n-1]=_.partial(e,t[n-1]&2147483648>>e-1,1)),t},partial:(t,e,n)=>t===32?e:(n?0|e:e<<32-t)+1099511627776*t,getPartial:t=>I.round(t/1099511627776)||32,_shiftRight(t,e,n,s){for(s===void 0&&(s=[]);e>=32;e-=32)s.push(n),n=0;if(e===0)return s.concat(t);for(let o=0;o<t.length;o++)s.push(n|t[o]>>>e),n=t[o]<<32-e;const r=t.length?t[t.length-1]:0,a=_.getPartial(r);return s.push(_.partial(e+a&31,e+a>32?n:s.pop(),1)),s}},L={bytes:{fromBits(t){const e=_.bitLength(t)/8,n=new w(e);let s;for(let r=0;e>r;r++)3&r||(s=t[r/4]),n[r]=s>>>24,s<<=8;return n},toBits(t){const e=[];let n,s=0;for(n=0;n<t.length;n++)s=s<<8|t[n],3&~n||(e.push(s),s=0);return 3&n&&e.push(_.partial(8*(3&n),s)),e}}},Ve=class{constructor(t){const e=this;e.blockSize=512,e._init=[1732584193,4023233417,2562383102,271733878,3285377520],e._key=[1518500249,1859775393,2400959708,3395469782],t?(e._h=t._h.slice(0),e._buffer=t._buffer.slice(0),e._length=t._length):e.reset()}reset(){const t=this;return t._h=t._init.slice(0),t._buffer=[],t._length=0,t}update(t){const e=this;typeof t=="string"&&(t=L.utf8String.toBits(t));const n=e._buffer=_.concat(e._buffer,t),s=e._length,r=e._length=s+_.bitLength(t);if(r>9007199254740991)throw new S("Cannot hash more than 2^53 - 1 bits");const a=new se(n);let o=0;for(let l=e.blockSize+s-(e.blockSize+s&e.blockSize-1);r>=l;l+=e.blockSize)e._block(a.subarray(16*o,16*(o+1))),o+=1;return n.splice(0,16*o),e}finalize(){const t=this;let e=t._buffer;const n=t._h;e=_.concat(e,[_.partial(1,1)]);for(let s=e.length+2;15&s;s++)e.push(0);for(e.push(I.floor(t._length/4294967296)),e.push(0|t._length);e.length;)t._block(e.splice(0,16));return t.reset(),n}_f(t,e,n,s){return t>19?t>39?t>59?t>79?void 0:e^n^s:e&n|e&s|n&s:e^n^s:e&n|~e&s}_S(t,e){return e<<t|e>>>32-t}_block(t){const e=this,n=e._h,s=te(80);for(let c=0;16>c;c++)s[c]=t[c];let r=n[0],a=n[1],o=n[2],l=n[3],h=n[4];for(let c=0;79>=c;c++){16>c||(s[c]=e._S(1,s[c-3]^s[c-8]^s[c-14]^s[c-16]));const i=e._S(5,r)+e._f(c,a,o,l)+h+s[c]+e._key[I.floor(c/20)]|0;h=l,l=o,o=e._S(30,a),a=r,r=i}n[0]=n[0]+r|0,n[1]=n[1]+a|0,n[2]=n[2]+o|0,n[3]=n[3]+l|0,n[4]=n[4]+h|0}},xe={getRandomValues(t){const e=new se(t.buffer),n=s=>{let r=987654321;const a=4294967295;return()=>(r=36969*(65535&r)+(r>>16)&a,(((r<<16)+(s=18e3*(65535&s)+(s>>16)&a)&a)/4294967296+.5)*(I.random()>.5?1:-1))};for(let s,r=0;r<t.length;r+=4){const a=n(4294967296*(s||I.random()));s=987654071*a(),e[r/4]=4294967296*a()|0}return t}},H={importKey:t=>new H.hmacSha1(L.bytes.toBits(t)),pbkdf2(t,e,n,s){if(n=n||1e4,0>s||0>n)throw new S("invalid params to pbkdf2");const r=1+(s>>5)<<2;let a,o,l,h,c;const i=new ArrayBuffer(r),u=new W(i);let p=0;const f=_;for(e=L.bytes.toBits(e),c=1;(r||1)>p;c++){for(a=o=t.encrypt(f.concat(e,[c])),l=1;n>l;l++)for(o=t.encrypt(o),h=0;h<o.length;h++)a[h]^=o[h];for(l=0;(r||1)>p&&l<a.length;l++)u.setInt32(p,a[l]),p+=4}return i.slice(0,s/8)},hmacSha1:class{constructor(t){const e=this,n=e._hash=Ve,s=[[],[]];e._baseHash=[new n,new n];const r=e._baseHash[0].blockSize/32;t.length>r&&(t=new n().update(t).finalize());for(let a=0;r>a;a++)s[0][a]=909522486^t[a],s[1][a]=1549556828^t[a];e._baseHash[0].update(s[0]),e._baseHash[1].update(s[1]),e._resultHash=new n(e._baseHash[0])}reset(){const t=this;t._resultHash=new t._hash(t._baseHash[0]),t._updated=!1}update(t){this._updated=!0,this._resultHash.update(t)}digest(){const t=this,e=t._resultHash.finalize(),n=new t._hash(t._baseHash[1]).update(e).finalize();return t.reset(),n}encrypt(t){if(this._updated)throw new S("encrypt on already updated hmac called!");return this.update(t),this.digest(t)}}},Ee=typeof B!=N&&typeof B.getRandomValues==O,le="Invalid password",ue="Invalid signature",he="zipjs-abort-check-password";function pe(t){return Ee?B.getRandomValues(t):xe.getRandomValues(t)}const P=16,fe={name:"PBKDF2"},Ue=m.assign({hash:{name:"HMAC"}},fe),X=m.assign({iterations:1e3,hash:{name:"SHA-1"}},fe),We=["deriveBits"],T=[8,12,16],V=[16,24,32],A=10,Me=[0,0,0,0],F=typeof B!=N,x=F&&B.subtle,de=F&&typeof x!=N,z=L.bytes,Ne=class{constructor(t){const e=this;e._tables=[[[],[],[],[],[]],[[],[],[],[],[]]],e._tables[0][0][0]||e._precompute();const n=e._tables[0][4],s=e._tables[1],r=t.length;let a,o,l,h=1;if(r!==4&&r!==6&&r!==8)throw new S("invalid aes key size");for(e._key=[o=t.slice(0),l=[]],a=r;4*r+28>a;a++){let c=o[a-1];(a%r===0||r===8&&a%r===4)&&(c=n[c>>>24]<<24^n[c>>16&255]<<16^n[c>>8&255]<<8^n[255&c],a%r===0&&(c=c<<8^c>>>24^h<<24,h=h<<1^283*(h>>7))),o[a]=o[a-r]^c}for(let c=0;a;c++,a--){const i=o[3&c?a:a-4];l[c]=4>=a||4>c?i:s[0][n[i>>>24]]^s[1][n[i>>16&255]]^s[2][n[i>>8&255]]^s[3][n[255&i]]}}encrypt(t){return this._crypt(t,0)}decrypt(t){return this._crypt(t,1)}_precompute(){const t=this._tables[0],e=this._tables[1],n=t[4],s=e[4],r=[],a=[];let o,l,h,c;for(let i=0;256>i;i++)a[(r[i]=i<<1^283*(i>>7))^i]=i;for(let i=o=0;!n[i];i^=l||1,o=a[o]||1){let u=o^o<<1^o<<2^o<<3^o<<4;u=u>>8^255&u^99,n[i]=u,s[u]=i,c=r[h=r[l=r[i]]];let p=16843009*c^65537*h^257*l^16843008*i,f=257*r[u]^16843008*u;for(let d=0;4>d;d++)t[d][i]=f=f<<24^f>>>8,e[d][u]=p=p<<24^p>>>8}for(let i=0;5>i;i++)t[i]=t[i].slice(0),e[i]=e[i].slice(0)}_crypt(t,e){if(t.length!==4)throw new S("invalid aes block size");const n=this._key[e],s=n.length/4-2,r=[0,0,0,0],a=this._tables[e],o=a[0],l=a[1],h=a[2],c=a[3],i=a[4];let u,p,f,d=t[0]^n[0],g=t[e?3:1]^n[1],y=t[2]^n[2],b=t[e?1:3]^n[3],C=4;for(let D=0;s>D;D++)u=o[d>>>24]^l[g>>16&255]^h[y>>8&255]^c[255&b]^n[C],p=o[g>>>24]^l[y>>16&255]^h[b>>8&255]^c[255&d]^n[C+1],f=o[y>>>24]^l[b>>16&255]^h[d>>8&255]^c[255&g]^n[C+2],b=o[b>>>24]^l[d>>16&255]^h[g>>8&255]^c[255&y]^n[C+3],C+=4,d=u,g=p,y=f;for(let D=0;4>D;D++)r[e?3&-D:D]=i[d>>>24]<<24^i[g>>16&255]<<16^i[y>>8&255]<<8^i[255&b]^n[C++],u=d,d=g,g=y,y=b,b=u;return r}},Oe=class{constructor(t,e){this._prf=t,this._initIv=e,this._iv=e}reset(){this._iv=this._initIv}update(t){return this.calculate(this._prf,t,this._iv)}incWord(t){if(255&~(t>>24))t+=1<<24;else{let e=t>>16&255,n=t>>8&255,s=255&t;e===255?(e=0,n===255?(n=0,s===255?s=0:++s):++n):++e,t=0,t+=e<<16,t+=n<<8,t+=s}return t}incCounter(t){(t[0]=this.incWord(t[0]))===0&&(t[1]=this.incWord(t[1]))}calculate(t,e,n){let s;if(!(s=e.length))return[];const r=_.bitLength(e);for(let a=0;s>a;a+=4){this.incCounter(n);const o=t.encrypt(n);e[a]^=o[0],e[a+1]^=o[1],e[a+2]^=o[2],e[a+3]^=o[3]}return _.clamp(e,r)}},Le=H.hmacSha1;let ge=F&&de&&typeof x.importKey==O,we=F&&de&&typeof x.deriveBits==O;class Fe extends v{constructor({password:e,rawPassword:n,signed:s,encryptionStrength:r,checkPasswordOnly:a}){super({start(){m.assign(this,{ready:new M(o=>this.resolveReady=o),password:_e(e,n),signed:s,strength:r-1,pending:new w})},async transform(o,l){const h=this,{password:c,strength:i,resolveReady:u,ready:p}=h;c?(await(async(d,g,y,b)=>{const C=await me(d,g,y,k(b,0,T[g])),D=k(b,T[g]);if(C[0]!=D[0]||C[1]!=D[1])throw new S(le)})(h,i,c,k(o,0,T[i]+2)),o=k(o,T[i]+2),a?l.error(new S(he)):u()):await p;const f=new w(o.length-A-(o.length-A)%P);l.enqueue(ye(h,o,f,0,A,!0))},async flush(o){const{signed:l,ctr:h,hmac:c,pending:i,ready:u}=this;if(c&&h){await u;const p=k(i,0,i.length-A),f=k(i,i.length-A);let d=new w;if(p.length){const g=U(z,p);c.update(g);const y=h.update(g);d=E(z,y)}if(l){const g=k(E(z,c.digest()),0,A);for(let y=0;A>y;y++)if(g[y]!=f[y])throw new S(ue)}o.enqueue(d)}}})}}class je extends v{constructor({password:e,rawPassword:n,encryptionStrength:s}){let r;super({start(){m.assign(this,{ready:new M(a=>this.resolveReady=a),password:_e(e,n),strength:s-1,pending:new w})},async transform(a,o){const l=this,{password:h,strength:c,resolveReady:i,ready:u}=l;let p=new w;h?(p=await(async(d,g,y)=>{const b=pe(new w(T[g]));return J(b,await me(d,g,y,b))})(l,c,h),i()):await u;const f=new w(p.length+a.length-a.length%P);f.set(p,0),o.enqueue(ye(l,a,f,p.length,0))},async flush(a){const{ctr:o,hmac:l,pending:h,ready:c}=this;if(l&&o){await c;let i=new w;if(h.length){const u=o.update(U(z,h));l.update(u),i=E(z,u)}r.signature=E(z,l.digest()).slice(0,A),a.enqueue(J(i,r.signature))}}}),r=this}}function ye(t,e,n,s,r,a){const{ctr:o,hmac:l,pending:h}=t,c=e.length-r;let i;for(h.length&&(e=J(h,e),n=((u,p)=>{if(p&&p>u.length){const f=u;(u=new w(p)).set(f,0)}return u})(n,c-c%P)),i=0;c-P>=i;i+=P){const u=U(z,k(e,i,i+P));a&&l.update(u);const p=o.update(u);a||l.update(p),n.set(E(z,p),i+s)}return t.pending=k(e,i),n}async function me(t,e,n,s){t.password=null;const r=await(async(i,u,p,f,d)=>{if(!ge)return H.importKey(u);try{return await x.importKey("raw",u,p,!1,d)}catch{return ge=!1,H.importKey(u)}})(0,n,Ue,0,We),a=await(async(i,u,p)=>{if(!we)return H.pbkdf2(u,i.salt,X.iterations,p);try{return await x.deriveBits(i,u,p)}catch{return we=!1,H.pbkdf2(u,i.salt,X.iterations,p)}})(m.assign({salt:s},X),r,8*(2*V[e]+2)),o=new w(a),l=U(z,k(o,0,V[e])),h=U(z,k(o,V[e],2*V[e])),c=k(o,2*V[e]);return m.assign(t,{keys:{key:l,authentication:h,passwordVerification:c},ctr:new Oe(new Ne(l),te.from(Me)),hmac:new Le(h)}),c}function _e(t,e){return e===j?(n=>{if(typeof ae==N){const s=new w((n=unescape(encodeURIComponent(n))).length);for(let r=0;r<s.length;r++)s[r]=n.charCodeAt(r);return s}return new ae().encode(n)})(t):e}function J(t,e){let n=t;return t.length+e.length&&(n=new w(t.length+e.length),n.set(t,0),n.set(e,t.length)),n}function k(t,e,n){return t.subarray(e,n)}function E(t,e){return t.fromBits(e)}function U(t,e){return t.toBits(e)}class Ge extends v{constructor({password:e,passwordVerification:n,checkPasswordOnly:s}){super({start(){m.assign(this,{password:e,passwordVerification:n}),ve(this,e)},transform(r,a){const o=this;if(o.password){const l=be(o,r.subarray(0,12));if(o.password=null,l[11]!=o.passwordVerification)throw new S(le);r=r.subarray(12)}s?a.error(new S(he)):a.enqueue(be(o,r))}})}}class Xe extends v{constructor({password:e,passwordVerification:n}){super({start(){m.assign(this,{password:e,passwordVerification:n}),ve(this,e)},transform(s,r){const a=this;let o,l;if(a.password){a.password=null;const h=pe(new w(12));h[11]=a.passwordVerification,o=new w(s.length+h.length),o.set(Se(a,h),0),l=12}else o=new w(s.length),l=0;o.set(Se(a,s),l),r.enqueue(o)}})}}function be(t,e){const n=new w(e.length);for(let s=0;s<e.length;s++)n[s]=ke(t)^e[s],Q(t,n[s]);return n}function Se(t,e){const n=new w(e.length);for(let s=0;s<e.length;s++)n[s]=ke(t)^e[s],Q(t,e[s]);return n}function ve(t,e){const n=[305419896,591751049,878082192];m.assign(t,{keys:n,crcKey0:new G(n[0]),crcKey2:new G(n[2])});for(let s=0;s<e.length;s++)Q(t,e.charCodeAt(s))}function Q(t,e){let[n,s,r]=t.keys;t.crcKey0.append([e]),n=~t.crcKey0.get(),s=De(I.imul(De(s+ze(n)),134775813)+1),t.crcKey2.append([s>>>24]),r=~t.crcKey2.get(),t.keys=[n,s,r]}function ke(t){const e=2|t.keys[2];return ze(I.imul(e,1^e)>>>8)}function ze(t){return 255&t}function De(t){return 4294967295&t}class Je extends v{constructor(e,{chunkSize:n,CompressionStream:s,CompressionStreamNative:r}){super({});const{compressed:a,encrypted:o,useCompressionStream:l,zipCrypto:h,signed:c,level:i}=e,u=this;let p,f,d=super.readable;o&&!h||!c||(p=new ce,d=q(d,p)),a&&(d=Ie(d,l,{level:i,chunkSize:n},r,s)),o&&(h?d=q(d,new Xe(e)):(f=new je(e),d=q(d,f))),Ce(u,d,()=>{let g;o&&!h&&(g=f.signature),o&&!h||!c||(g=new W(p.value.buffer).getUint32(0)),u.signature=g})}}class Qe extends v{constructor(e,{chunkSize:n,DecompressionStream:s,DecompressionStreamNative:r}){super({});const{zipCrypto:a,encrypted:o,signed:l,signature:h,compressed:c,useCompressionStream:i}=e;let u,p,f=super.readable;o&&(a?f=q(f,new Ge(e)):(p=new Fe(e),f=q(f,p))),c&&(f=Ie(f,i,{chunkSize:n},r,s)),o&&!a||!l||(u=new ce,f=q(f,u)),Ce(this,f,()=>{if((!o||a)&&l){const d=new W(u.value.buffer);if(h!=d.getUint32(0,!1))throw new S(ue)}})}}function Ce(t,e,n){e=q(e,new v({flush:n})),m.defineProperty(t,"readable",{get:()=>e})}function Ie(t,e,n,s,r){return q(t,new(e&&s?s:r)("deflate-raw",n))}function q(t,e){return t.pipeThrough(e)}const Ae="data",qe="close";class Ye extends v{constructor(e,n){super({});const s=this,{codecType:r}=e;let a;r.startsWith("deflate")?a=Je:r.startsWith("inflate")&&(a=Qe),s.outputSize=0;let o=0;const l=new a(e,n),h=super.readable,c=new v({transform(u,p){u&&u.length&&(o+=u.length,p.enqueue(u))},flush(){m.assign(s,{inputSize:o})}}),i=new v({transform(u,p){if(u&&u.length&&(p.enqueue(u),s.outputSize+=u.length,e.outputSize&&s.outputSize>e.outputSize))throw new S("Invalid uncompressed size")},flush(){const{signature:u}=l;m.assign(s,{signature:u,inputSize:o})}});m.defineProperty(s,"readable",{get:()=>h.pipeThrough(c).pipeThrough(l).pipeThrough(i)})}}class Ze extends v{constructor(e){let n;super({transform:function s(r,a){if(n){const o=new w(n.length+r.length);o.set(n),o.set(r,n.length),r=o,n=null}r.length>e?(a.enqueue(r.slice(0,e)),s(r.slice(e),a)):n=r},flush(s){n&&n.length&&s.enqueue(n)}})}}const Y=new re,Z=new re;let $,R=0,Re=!0;async function $e(t){try{const{options:e,scripts:n,config:s}=t;if(n&&n.length)try{Re?importScripts.apply(j,n):await He(n)}catch{Re=!1,await He(n)}self.initCodec&&self.initCodec(),s.CompressionStreamNative=self.CompressionStream,s.DecompressionStreamNative=self.DecompressionStream,self.Deflate&&(s.CompressionStream=new oe(self.Deflate)),self.Inflate&&(s.DecompressionStream=new oe(self.Inflate));const r={highWaterMark:1},a=t.readable||new Ke({async pull(p){const f=new M(y=>Y.set(R,y));ee({type:"pull",messageId:R}),R=(R+1)%ne.MAX_SAFE_INTEGER;const{value:d,done:g}=await f;p.enqueue(d),g&&p.close()}},r),o=t.writable||new Te({async write(p){let f;const d=new M(g=>f=g);Z.set(R,f),ee({type:Ae,value:p,messageId:R}),R=(R+1)%ne.MAX_SAFE_INTEGER,await d}},r),l=new Ye(e,s);$=new AbortController;const{signal:h}=$;await a.pipeThrough(l).pipeThrough(new Ze(s.chunkSize)).pipeTo(o,{signal:h,preventClose:!0,preventAbort:!0}),await o.getWriter().close();const{signature:c,inputSize:i,outputSize:u}=l;ee({type:qe,result:{signature:c,inputSize:i,outputSize:u}})}catch(e){e.outputSize=0,Pe(e)}}async function He(t){for(const e of t)await import(e)}function ee(t){let{value:e}=t;if(e)if(e.length)try{e=new w(e),t.value=e.buffer,K(t,[t.value])}catch{K(t)}else K(t);else K(t)}function Pe(t=new S("Unknown error")){const{message:e,stack:n,code:s,name:r,outputSize:a}=t;K({error:{message:e,stack:n,code:s,name:r,outputSize:a}})}function Be(t,e,n){return class{constructor(r){const a=this;var o,l;o=r,l="level",(typeof m.hasOwn===O?m.hasOwn(o,l):o.hasOwnProperty(l))&&r.level===j&&delete r.level,a.codec=new t(m.assign({},e,r)),n(a.codec,h=>{if(a.pendingData){const c=a.pendingData;a.pendingData=new w(c.length+h.length);const{pendingData:i}=a;i.set(c,0),i.set(h,c.length)}else a.pendingData=new w(h)})}append(r){return this.codec.push(r),s(this)}flush(){return this.codec.push(new w,!0),s(this)}};function s(r){if(r.pendingData){const a=r.pendingData;return r.pendingData=null,a}return new w}}addEventListener("message",({data:t})=>{const{type:e,messageId:n,value:s,done:r}=t;try{if(e=="start"&&$e(t),e==Ae){const a=Y.get(n);Y.delete(n),a({value:new w(s),done:r})}if(e=="ack"){const a=Z.get(n);Z.delete(n),a()}e==qe&&$.abort()}catch(a){Pe(a)}}),self.initCodec=()=>{const{Deflate:t,Inflate:e}=((n,s={},r)=>({Deflate:Be(n.Deflate,s.deflate,r),Inflate:Be(n.Inflate,s.inflate,r)}))(pako,{deflate:{raw:!0},inflate:{raw:!0}},(n,s)=>n.onData=s);self.Deflate=t,self.Inflate=e}})();
