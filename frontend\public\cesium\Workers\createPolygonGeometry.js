/**
 * @license
 * Cesium - https://github.com/CesiumGS/cesium
 * Version 1.132
 *
 * Copyright 2011-2022 Cesium Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * Columbus View (Pat. Pend.)
 *
 * Portions licensed separately.
 * See https://github.com/CesiumGS/cesium/blob/main/LICENSE.md for full licensing details.
 */

import{a as mt}from"./chunk-WGGIH7QW.js";import{a as L}from"./chunk-AJYK4IVJ.js";import{a as st}from"./chunk-J6UP6FLE.js";import{a as rt}from"./chunk-D6AA5QVT.js";import{a as bt}from"./chunk-VW6VD53G.js";import"./chunk-DEPHB2WM.js";import"./chunk-QOUAJ6TL.js";import{a as lt}from"./chunk-GBYLG25F.js";import{a as q}from"./chunk-CYCB63OH.js";import{a as Z}from"./chunk-TCGIRNHN.js";import"./chunk-N4VJKXZS.js";import{a as dt,b as ot}from"./chunk-77IHZJJ3.js";import"./chunk-3W4GT7KQ.js";import"./chunk-G5AGHVVC.js";import{a as wt,b as yt}from"./chunk-DMEY62ID.js";import"./chunk-HTFSEEMT.js";import{a as ct}from"./chunk-OFUUQVMR.js";import"./chunk-A56XVLQR.js";import{c as ut,d as $}from"./chunk-RCV6KWXS.js";import{d as Tt}from"./chunk-4IW2T6GF.js";import{f as at,h as _t}from"./chunk-PSPPBZWI.js";import{a as X}from"./chunk-AU7IKHOH.js";import{a as c,b as et,c as O,d as S,e as D,f as Pt}from"./chunk-64RSHJUE.js";import{a as l}from"./chunk-3SSKC3VN.js";import"./chunk-OSW76XDF.js";import"./chunk-ED5JPB3S.js";import{a as ft,b as Y}from"./chunk-LEYMRMBK.js";import{e as P}from"./chunk-VTAIKJXX.js";function b(t,e){this.position=t,P(this.position)||(this.position=new O),this.tangentPlane=e,P(this.tangentPlane)||(this.tangentPlane=b.NORTH_POLE_TANGENT_PLANE)}Object.defineProperties(b.prototype,{ellipsoid:{get:function(){return this.tangentPlane.ellipsoid}},x:{get:function(){return this.position.x}},y:{get:function(){return this.position.y}},conformalLatitude:{get:function(){let t=O.magnitude(this.position),e=2*this.ellipsoid.maximumRadius;return this.tangentPlane.plane.normal.z*(l.PI_OVER_TWO-2*Math.atan2(t,e))}},longitude:{get:function(){let t=l.PI_OVER_TWO+Math.atan2(this.y,this.x);return t>Math.PI&&(t-=l.TWO_PI),t}}});var nt=new et,Rt=new c;b.prototype.getLatitude=function(t){P(t)||(t=S.default),nt.latitude=this.conformalLatitude,nt.longitude=this.longitude,nt.height=0;let e=this.ellipsoid.cartographicToCartesian(nt,Rt);return t.cartesianToCartographic(e,nt),nt.latitude};var It=new wt,Lt=new c,Ft=new c;b.fromCartesian=function(t,e){Y.defined("cartesian",t);let o=l.signNotZero(t.z),n=b.NORTH_POLE_TANGENT_PLANE,s=b.SOUTH_POLE;o<0&&(n=b.SOUTH_POLE_TANGENT_PLANE,s=b.NORTH_POLE);let i=It;i.origin=n.ellipsoid.scaleToGeocentricSurface(t,i.origin),i.direction=c.subtract(i.origin,s,Lt),c.normalize(i.direction,i.direction);let r=yt.rayPlane(i,n.plane,Ft),f=c.subtract(r,s,r),d=c.dot(n.xAxis,f),p=o*c.dot(n.yAxis,f);return P(e)?(e.position=new O(d,p),e.tangentPlane=n,e):new b(new O(d,p),n)};b.fromCartesianArray=function(t,e){Y.defined("cartesians",t);let o=t.length;P(e)?e.length=o:e=new Array(o);for(let n=0;n<o;n++)e[n]=b.fromCartesian(t[n],e[n]);return e};b.clone=function(t,e){if(P(t))return P(e)?(e.position=t.position,e.tangentPlane=t.tangentPlane,e):new b(t.position,t.tangentPlane)};b.HALF_UNIT_SPHERE=Object.freeze(new S(.5,.5,.5));b.NORTH_POLE=Object.freeze(new c(0,0,.5));b.SOUTH_POLE=Object.freeze(new c(0,0,-.5));b.NORTH_POLE_TANGENT_PLANE=Object.freeze(new Z(b.NORTH_POLE,b.HALF_UNIT_SPHERE));b.SOUTH_POLE_TANGENT_PLANE=Object.freeze(new Z(b.SOUTH_POLE,b.HALF_UNIT_SPHERE));var z=b;var vt=new et,Ot=new et;function St(t,e,o,n){let i=n.cartesianToCartographic(t,vt).height,r=n.cartesianToCartographic(e,Ot);r.height=i,n.cartographicToCartesian(r,e);let f=n.cartesianToCartographic(o,Ot);f.height=i-100,n.cartographicToCartesian(f,o)}var Et=new mt,Vt=new c,Bt=new c,Mt=new c,jt=new c,kt=new c,zt=new c,ht=new c,Q=new c,it=new c,Dt=new O,Ut=new O,Wt=new c,At=new at,Gt=new D,Yt=new D;function gt(t){let e=t.vertexFormat,o=t.geometry,n=t.shadowVolume,s=o.attributes.position.values,i=P(o.attributes.st)?o.attributes.st.values:void 0,r=s.length,f=t.wall,d=t.top||f,p=t.bottom||f;if(e.st||e.normal||e.tangent||e.bitangent||n){let w=t.boundingRectangle,a=t.rotationAxis,H=t.projectTo2d,x=t.ellipsoid,M=t.stRotation,C=t.perPositionHeight,R=Dt;R.x=w.x,R.y=w.y;let V=e.st?new Float32Array(2*(r/3)):void 0,u;e.normal&&(C&&d&&!f?u=o.attributes.normal.values:u=new Float32Array(r));let E=e.tangent?new Float32Array(r):void 0,A=e.bitangent?new Float32Array(r):void 0,F=n?new Float32Array(r):void 0,T=0,y=0,_=Bt,h=Mt,g=jt,j=!0,G=Gt,K=Yt;if(M!==0){let B=at.fromAxisAngle(a,M,At);G=D.fromQuaternion(B,G),B=at.fromAxisAngle(a,-M,At),K=D.fromQuaternion(B,K)}else G=D.clone(D.IDENTITY,G),K=D.clone(D.IDENTITY,K);let m=0,I=0;d&&p&&(m=r/2,I=r/3,r/=2);for(let B=0;B<r;B+=3){let k=c.fromArray(s,B,Wt);if(e.st&&!P(i)){let N=D.multiplyByVector(G,k,Vt);N=x.scaleToGeodeticSurface(N,N);let v=H([N],Ut)[0];O.subtract(v,R,v);let J=l.clamp(v.x/w.width,0,1),tt=l.clamp(v.y/w.height,0,1);p&&(V[T+I]=J,V[T+1+I]=tt),d&&(V[T]=J,V[T+1]=tt),T+=2}if(e.normal||e.tangent||e.bitangent||n){let N=y+1,v=y+2;if(f){if(B+3<r){let J=c.fromArray(s,B+3,kt);if(j){let tt=c.fromArray(s,B+r,zt);C&&St(k,J,tt,x),c.subtract(J,k,J),c.subtract(tt,k,tt),_=c.normalize(c.cross(tt,J,_),_),j=!1}c.equalsEpsilon(J,k,l.EPSILON10)&&(j=!0)}(e.tangent||e.bitangent)&&(g=x.geodeticSurfaceNormal(k,g),e.tangent&&(h=c.normalize(c.cross(g,_,h),h)))}else _=x.geodeticSurfaceNormal(k,_),(e.tangent||e.bitangent)&&(C&&(ht=c.fromArray(u,y,ht),Q=c.cross(c.UNIT_Z,ht,Q),Q=c.normalize(D.multiplyByVector(K,Q,Q),Q),e.bitangent&&(it=c.normalize(c.cross(ht,Q,it),it))),h=c.cross(c.UNIT_Z,_,h),h=c.normalize(D.multiplyByVector(K,h,h),h),e.bitangent&&(g=c.normalize(c.cross(_,h,g),g)));e.normal&&(t.wall?(u[y+m]=_.x,u[N+m]=_.y,u[v+m]=_.z):p&&(u[y+m]=-_.x,u[N+m]=-_.y,u[v+m]=-_.z),(d&&!C||f)&&(u[y]=_.x,u[N]=_.y,u[v]=_.z)),n&&(f&&(_=x.geodeticSurfaceNormal(k,_)),F[y+m]=-_.x,F[N+m]=-_.y,F[v+m]=-_.z),e.tangent&&(t.wall?(E[y+m]=h.x,E[N+m]=h.y,E[v+m]=h.z):p&&(E[y+m]=-h.x,E[N+m]=-h.y,E[v+m]=-h.z),d&&(C?(E[y]=Q.x,E[N]=Q.y,E[v]=Q.z):(E[y]=h.x,E[N]=h.y,E[v]=h.z))),e.bitangent&&(p&&(A[y+m]=g.x,A[N+m]=g.y,A[v+m]=g.z),d&&(C?(A[y]=it.x,A[N]=it.y,A[v]=it.z):(A[y]=g.x,A[N]=g.y,A[v]=g.z))),y+=3}}e.st&&!P(i)&&(o.attributes.st=new $({componentDatatype:X.FLOAT,componentsPerAttribute:2,values:V})),e.normal&&(o.attributes.normal=new $({componentDatatype:X.FLOAT,componentsPerAttribute:3,values:u})),e.tangent&&(o.attributes.tangent=new $({componentDatatype:X.FLOAT,componentsPerAttribute:3,values:E})),e.bitangent&&(o.attributes.bitangent=new $({componentDatatype:X.FLOAT,componentsPerAttribute:3,values:A})),n&&(o.attributes.extrudeDirection=new $({componentDatatype:X.FLOAT,componentsPerAttribute:3,values:F}))}if(t.extrude&&P(t.offsetAttribute)){let w=s.length/3,a=new Uint8Array(w);if(t.offsetAttribute===lt.TOP)d&&p||f?a=a.fill(1,0,w/2):d&&(a=a.fill(1));else{let H=t.offsetAttribute===lt.NONE?0:1;a=a.fill(H)}o.attributes.applyOffset=new $({componentDatatype:X.UNSIGNED_BYTE,componentsPerAttribute:1,values:a})}return o}var Ht=[];function qt(t,e,o,n,s,i,r,f,d,p){let w={walls:[]},a;if(r||f){let u=L.createGeometryFromPositions(t,e,o,n,i,d,p),E=u.attributes.position.values,A=u.indices,F,T;if(r&&f){let y=E.concat(E);F=y.length/3,T=ct.createTypedArray(F,A.length*2),T.set(A);let _=A.length,h=F/2;for(a=0;a<_;a+=3){let g=T[a]+h,j=T[a+1]+h,G=T[a+2]+h;T[a+_]=G,T[a+1+_]=j,T[a+2+_]=g}if(u.attributes.position.values=y,i&&d.normal){let g=u.attributes.normal.values;u.attributes.normal.values=new Float32Array(y.length),u.attributes.normal.values.set(g)}if(d.st&&P(o)){let g=u.attributes.st.values;u.attributes.st.values=new Float32Array(F*2),u.attributes.st.values=g.concat(g)}u.indices=T}else if(f){for(F=E.length/3,T=ct.createTypedArray(F,A.length),a=0;a<A.length;a+=3)T[a]=A[a+2],T[a+1]=A[a+1],T[a+2]=A[a];u.indices=T}w.topAndBottom=new rt({geometry:u})}let H=s.outerRing,x=Z.fromPoints(H,t),M=x.projectPointsOntoPlane(H,Ht),C=ot.computeWindingOrder2D(M);C===dt.CLOCKWISE&&(H=H.slice().reverse());let R=L.computeWallGeometry(H,o,t,n,i,p);w.walls.push(new rt({geometry:R}));let V=s.holes;for(a=0;a<V.length;a++){let u=V[a];M=x.projectPointsOntoPlane(u,Ht),C=ot.computeWindingOrder2D(M),C===dt.COUNTER_CLOCKWISE&&(u=u.slice().reverse()),R=L.computeWallGeometry(u,o,t,n,i,p),w.walls.push(new rt({geometry:R}))}return w}function W(t){if(Y.typeOf.object("options",t),Y.typeOf.object("options.polygonHierarchy",t.polygonHierarchy),P(t.perPositionHeight)&&t.perPositionHeight&&P(t.height))throw new ft("Cannot use both options.perPositionHeight and options.height");if(P(t.arcType)&&t.arcType!==st.GEODESIC&&t.arcType!==st.RHUMB)throw new ft("Invalid arcType. Valid options are ArcType.GEODESIC and ArcType.RHUMB.");let e=t.polygonHierarchy,o=t.vertexFormat??q.DEFAULT,n=t.ellipsoid??S.default,s=t.granularity??l.RADIANS_PER_DEGREE,i=t.stRotation??0,r=t.textureCoordinates,f=t.perPositionHeight??!1,d=f&&P(t.extrudedHeight),p=t.height??0,w=t.extrudedHeight??p;if(!d){let a=Math.max(p,w);w=Math.min(p,w),p=a}this._vertexFormat=q.clone(o),this._ellipsoid=S.clone(n),this._granularity=s,this._stRotation=i,this._height=p,this._extrudedHeight=w,this._closeTop=t.closeTop??!0,this._closeBottom=t.closeBottom??!0,this._polygonHierarchy=e,this._perPositionHeight=f,this._perPositionHeightExtrude=d,this._shadowVolume=t.shadowVolume??!1,this._workerName="createPolygonGeometry",this._offsetAttribute=t.offsetAttribute,this._arcType=t.arcType??st.GEODESIC,this._rectangle=void 0,this._textureCoordinateRotationPoints=void 0,this._textureCoordinates=r,this.packedLength=L.computeHierarchyPackedLength(e,c)+S.packedLength+q.packedLength+(r?L.computeHierarchyPackedLength(r,O):1)+12}W.fromPositions=function(t){t=t??Pt.EMPTY_OBJECT,Y.defined("options.positions",t.positions);let e={polygonHierarchy:{positions:t.positions},height:t.height,extrudedHeight:t.extrudedHeight,vertexFormat:t.vertexFormat,stRotation:t.stRotation,ellipsoid:t.ellipsoid,granularity:t.granularity,perPositionHeight:t.perPositionHeight,closeTop:t.closeTop,closeBottom:t.closeBottom,offsetAttribute:t.offsetAttribute,arcType:t.arcType,textureCoordinates:t.textureCoordinates};return new W(e)};W.pack=function(t,e,o){return Y.typeOf.object("value",t),Y.defined("array",e),o=o??0,o=L.packPolygonHierarchy(t._polygonHierarchy,e,o,c),S.pack(t._ellipsoid,e,o),o+=S.packedLength,q.pack(t._vertexFormat,e,o),o+=q.packedLength,e[o++]=t._height,e[o++]=t._extrudedHeight,e[o++]=t._granularity,e[o++]=t._stRotation,e[o++]=t._perPositionHeightExtrude?1:0,e[o++]=t._perPositionHeight?1:0,e[o++]=t._closeTop?1:0,e[o++]=t._closeBottom?1:0,e[o++]=t._shadowVolume?1:0,e[o++]=t._offsetAttribute??-1,e[o++]=t._arcType,P(t._textureCoordinates)?o=L.packPolygonHierarchy(t._textureCoordinates,e,o,O):e[o++]=-1,e[o++]=t.packedLength,e};var Qt=S.clone(S.UNIT_SPHERE),Zt=new q,Kt={polygonHierarchy:{}};W.unpack=function(t,e,o){Y.defined("array",t),e=e??0;let n=L.unpackPolygonHierarchy(t,e,c);e=n.startingIndex,delete n.startingIndex;let s=S.unpack(t,e,Qt);e+=S.packedLength;let i=q.unpack(t,e,Zt);e+=q.packedLength;let r=t[e++],f=t[e++],d=t[e++],p=t[e++],w=t[e++]===1,a=t[e++]===1,H=t[e++]===1,x=t[e++]===1,M=t[e++]===1,C=t[e++],R=t[e++],V=t[e]===-1?void 0:L.unpackPolygonHierarchy(t,e,O);P(V)?(e=V.startingIndex,delete V.startingIndex):e++;let u=t[e++];return P(o)||(o=new W(Kt)),o._polygonHierarchy=n,o._ellipsoid=S.clone(s,o._ellipsoid),o._vertexFormat=q.clone(i,o._vertexFormat),o._height=r,o._extrudedHeight=f,o._granularity=d,o._stRotation=p,o._perPositionHeightExtrude=w,o._perPositionHeight=a,o._closeTop=H,o._closeBottom=x,o._shadowVolume=M,o._offsetAttribute=C===-1?void 0:C,o._arcType=R,o._textureCoordinates=V,o.packedLength=u,o};var Jt=new O,Xt=new O,$t=new z;function xt(t,e,o,n,s,i){let r=t.longitude,f=r>=0?r:r+l.TWO_PI;s.westOverIdl=Math.min(s.westOverIdl,f),s.eastOverIdl=Math.max(s.eastOverIdl,f),i.west=Math.min(i.west,r),i.east=Math.max(i.east,r);let d=t.getLatitude(o),p=d;if(i.south=Math.min(i.south,d),i.north=Math.max(i.north,d),n!==st.RHUMB){let H=O.subtract(e.position,t.position,Jt),x=O.dot(e.position,H)/O.dot(H,H);if(x>0&&x<1){let M=O.add(e.position,O.multiplyByScalar(H,-x,H),Xt),C=z.clone(e,$t);C.position=M;let R=C.getLatitude(o);i.south=Math.min(i.south,R),i.north=Math.max(i.north,R),Math.abs(d)>Math.abs(R)&&(p=R)}}let w=e.x*t.y-t.x*e.y,a=Math.sign(w);a!==0&&(a*=O.angleBetween(e.position,t.position)),p>=0&&(s.northAngle+=a),p<=0&&(s.southAngle+=a)}var Ct=new z,te=new z,U={northAngle:0,southAngle:0,westOverIdl:0,eastOverIdl:0};W.computeRectangleFromPositions=function(t,e,o,n){if(Y.defined("positions",t),P(n)||(n=new _t),t.length<3)return n;n.west=Number.POSITIVE_INFINITY,n.east=Number.NEGATIVE_INFINITY,n.south=Number.POSITIVE_INFINITY,n.north=Number.NEGATIVE_INFINITY,U.northAngle=0,U.southAngle=0,U.westOverIdl=Number.POSITIVE_INFINITY,U.eastOverIdl=Number.NEGATIVE_INFINITY;let s=t.length,i=z.fromCartesian(t[0],te);for(let r=1;r<s;r++){let f=z.fromCartesian(t[r],Ct);xt(f,i,e,o,U,n),i=z.clone(f,i)}return xt(z.fromCartesian(t[0],Ct),i,e,o,U,n),n.east-n.west>U.eastOverIdl-U.westOverIdl&&(n.west=U.westOverIdl,n.east=U.eastOverIdl,n.east>l.PI&&(n.east=n.east-l.TWO_PI),n.west>l.PI&&(n.west=n.west-l.TWO_PI)),l.equalsEpsilon(Math.abs(U.northAngle),l.TWO_PI,l.EPSILON10)&&(n.north=l.PI_OVER_TWO,n.east=l.PI,n.west=-l.PI),l.equalsEpsilon(Math.abs(U.southAngle),l.TWO_PI,l.EPSILON10)&&(n.south=-l.PI_OVER_TWO,n.east=l.PI,n.west=-l.PI),n};var ee=new z;function oe(t,e,o){return t.height>=l.PI||t.width>=l.PI?z.fromCartesian(e[0],ee).tangentPlane:Z.fromPoints(e,o)}var Nt=new et;function ne(t,e,o){return(n,s)=>{if(t.height>=l.PI||t.width>=l.PI){if(t.south<0&&t.north>0){P(s)||(s=[]);for(let r=0;r<n.length;++r){let f=o.cartesianToCartographic(n[r],Nt);s[r]=new O(f.longitude/l.PI,f.latitude/l.PI_OVER_TWO)}return s.length=n.length,s}return z.fromCartesianArray(n,s)}return Z.fromPoints(e,o).projectPointsOntoPlane(n,s)}}function ie(t,e,o){if(t.height>=l.PI||t.width>=l.PI)return(s,i)=>{if(t.south<0&&t.north>0){let r=o.cartesianToCartographic(s,Nt);return P(i)||(i=new O),i.x=r.longitude/l.PI,i.y=r.latitude/l.PI_OVER_TWO,i}return z.fromCartesian(s,i)};let n=Z.fromPoints(e,o);return(s,i)=>n.projectPointsOntoPlane(s,i)}function re(t,e,o,n){return(s,i)=>!n&&(t.height>=l.PI_OVER_TWO||t.width>=2*l.PI_OVER_THREE)?L.splitPolygonsOnEquator(s,e,o,i):s}function se(t,e,o,n){if(e.height>=l.PI||e.width>=l.PI)return mt.fromRectangle(e,void 0,Et);let s=t,i=Z.fromPoints(s,o);return L.computeBoundingRectangle(i.plane.normal,i.projectPointOntoPlane.bind(i),s,n,Et)}W.createGeometry=function(t){let e=t._vertexFormat,o=t._ellipsoid,n=t._granularity,s=t._stRotation,i=t._polygonHierarchy,r=t._perPositionHeight,f=t._closeTop,d=t._closeBottom,p=t._arcType,w=t._textureCoordinates,a=P(w),H=i.positions;if(H.length<3)return;let x=t.rectangle,M=L.polygonsFromHierarchy(i,a,ne(x,H,o),!r,o,re(x,o,p,r)),C=M.hierarchy,R=M.polygons,V=function(m){return m},u=a?L.polygonsFromHierarchy(w,!0,V,!1,o).polygons:void 0;if(C.length===0)return;let E=C[0].outerRing,A=se(E,x,o,s),F=[],T=t._height,y=t._extrudedHeight,_=t._perPositionHeightExtrude||!l.equalsEpsilon(T,y,0,l.EPSILON2),h={perPositionHeight:r,vertexFormat:e,geometry:void 0,rotationAxis:oe(x,E,o).plane.normal,projectTo2d:ie(x,E,o),boundingRectangle:A,ellipsoid:o,stRotation:s,textureCoordinates:void 0,bottom:!1,top:!0,wall:!1,extrude:!1,arcType:p},g;if(_)for(h.extrude=!0,h.top=f,h.bottom=d,h.shadowVolume=t._shadowVolume,h.offsetAttribute=t._offsetAttribute,g=0;g<R.length;g++){let m=qt(o,R[g],a?u[g]:void 0,n,C[g],r,f,d,e,p),I;f&&d?(I=m.topAndBottom,h.geometry=L.scaleToGeodeticHeightExtruded(I.geometry,T,y,o,r)):f?(I=m.topAndBottom,I.geometry.attributes.position.values=ot.scaleToGeodeticHeight(I.geometry.attributes.position.values,T,o,!r),h.geometry=I.geometry):d&&(I=m.topAndBottom,I.geometry.attributes.position.values=ot.scaleToGeodeticHeight(I.geometry.attributes.position.values,y,o,!0),h.geometry=I.geometry),(f||d)&&(h.wall=!1,I.geometry=gt(h),F.push(I));let B=m.walls;h.wall=!0;for(let k=0;k<B.length;k++){let N=B[k];h.geometry=L.scaleToGeodeticHeightExtruded(N.geometry,T,y,o,r),N.geometry=gt(h),F.push(N)}}else for(g=0;g<R.length;g++){let m=new rt({geometry:L.createGeometryFromPositions(o,R[g],a?u[g]:void 0,n,r,e,p)});if(m.geometry.attributes.position.values=ot.scaleToGeodeticHeight(m.geometry.attributes.position.values,T,o,!r),h.geometry=m.geometry,m.geometry=gt(h),P(t._offsetAttribute)){let I=m.geometry.attributes.position.values.length,B=t._offsetAttribute===lt.NONE?0:1,k=new Uint8Array(I/3).fill(B);m.geometry.attributes.applyOffset=new $({componentDatatype:X.UNSIGNED_BYTE,componentsPerAttribute:1,values:k})}F.push(m)}let j=bt.combineInstances(F)[0];j.attributes.position.values=new Float64Array(j.attributes.position.values),j.indices=ct.createTypedArray(j.attributes.position.values.length/3,j.indices);let G=j.attributes,K=Tt.fromVertices(G.position.values);return e.position||delete G.position,new ut({attributes:G,indices:j.indices,primitiveType:j.primitiveType,boundingSphere:K,offsetAttribute:t._offsetAttribute})};W.createShadowVolume=function(t,e,o){let n=t._granularity,s=t._ellipsoid,i=e(n,s),r=o(n,s);return new W({polygonHierarchy:t._polygonHierarchy,ellipsoid:s,stRotation:t._stRotation,granularity:n,perPositionHeight:!1,extrudedHeight:i,height:r,vertexFormat:q.POSITION_ONLY,shadowVolume:!0,arcType:t._arcType})};function ae(t){let e=-t._stRotation;if(e===0)return[0,0,0,1,1,0];let o=t._ellipsoid,n=t._polygonHierarchy.positions,s=t.rectangle;return ut._textureCoordinateRotationPoints(n,e,o,s)}Object.defineProperties(W.prototype,{rectangle:{get:function(){if(!P(this._rectangle)){let t=this._polygonHierarchy.positions;this._rectangle=W.computeRectangleFromPositions(t,this._ellipsoid,this._arcType)}return this._rectangle}},textureCoordinateRotationPoints:{get:function(){return P(this._textureCoordinateRotationPoints)||(this._textureCoordinateRotationPoints=ae(this)),this._textureCoordinateRotationPoints}}});var pt=W;function ce(t,e){return P(e)&&(t=pt.unpack(t,e)),t._ellipsoid=S.clone(t._ellipsoid),pt.createGeometry(t)}var Xe=ce;export{Xe as default};
