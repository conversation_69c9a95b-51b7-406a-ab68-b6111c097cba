# getNewsText.py (增量抓取 + scraped_status 更新)
import pandas as pd
import newspaper
import requests
from tqdm import tqdm
import time
import random
import warnings
from pathlib import Path

# 关闭 SSL 警告
warnings.filterwarnings("ignore", category=requests.packages.urllib3.exceptions.InsecureRequestWarning)

HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                  "AppleWebKit/537.36 (KHTML, like Gecko) "
                  "Chrome/139.0.0.0 Safari/537.36"
}

def parse_article(url, lang="en"):
    """用 newspaper 解析文章，返回正文、关键词、作者"""
    try:
        response = requests.get(url, headers=HEADERS, timeout=15, verify=False)
        if response.status_code != 200:
            return "", "", "", f"请求失败: {response.status_code}"

        article = newspaper.Article(url, language=lang)
        article.download(input_html=response.text)
        article.parse()

        try:
            article.nlp()
        except Exception:
            pass

        text = getattr(article, "text", "")
        keywords = ", ".join(getattr(article, "keywords", []))
        authors = ", ".join(getattr(article, "authors", []))
        return text, keywords, authors, None
    except Exception as e:
        return "", "", "", str(e)

def add_news_text_columns(csv_file="south_china_sea_news.csv"):
    """
    增量抓取新闻正文、关键词、作者，只抓取 scraped_status = 'pending' 的新闻。
    成功更新 scraped_status = 'success'，失败更新 scraped_status = 'failed'。
    """
    if not Path(csv_file).exists():
        print(f"[ERROR] 文件不存在: {csv_file}")
        return

    df = pd.read_csv(csv_file)

    # 初始化列，如果不存在
    for col in ["authors", "keywords", "text", "scraped_status"]:
        if col not in df.columns:
            df[col] = "" if col != "scraped_status" else "pending"

    failed = []

    # 处理 pending 的新闻
    pending_idx = df.index[(df["scraped_status"] == "pending")].tolist()
    if not pending_idx:
        print("ℹ️ 没有 pending 的新闻需要抓取。")
        return

    for idx in tqdm(pending_idx, desc="解析新闻"):
        original_link = df.at[idx, "original_link"]
        if not isinstance(original_link, str) or not original_link.startswith("http"):
            df.at[idx, "scraped_status"] = "failed"
            failed.append((idx, "无效链接"))
            continue

        text, keywords, authors, error = parse_article(original_link)

        if error:
            df.at[idx, "scraped_status"] = "failed"
            failed.append((idx, error))
        else:
            df.at[idx, "scraped_status"] = "success"

        df.at[idx, "authors"] = authors
        df.at[idx, "keywords"] = keywords
        df.at[idx, "text"] = text

        # 随机延时 1-3 秒
        time.sleep(random.uniform(1, 3))

    # 保存回原 CSV
    df.to_csv(csv_file, index=False, encoding="utf-8-sig")
    print(f"✅ 新闻正文、关键词、作者已更新并写回 {csv_file}")

    if failed:
        fail_df = pd.DataFrame(failed, columns=["row_index", "error"])
        fail_file = "failed_links.csv"
        fail_df.to_csv(fail_file, index=False, encoding="utf-8-sig")
        print(f"⚠️ 部分链接解析失败，详情见 {fail_file}")


# ----------------- 测试运行 -----------------
if __name__ == "__main__":
    add_news_text_columns("south_china_sea_news.csv")
