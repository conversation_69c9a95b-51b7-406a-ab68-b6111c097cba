{"name": "news-backend", "version": "1.0.0", "description": "News visualization backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.6.1", "express": "^4.18.2", "mysql2": "^3.14.5", "sqlite3": "^5.1.7"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["news", "api", "mysql", "express"], "author": "", "license": "ISC"}