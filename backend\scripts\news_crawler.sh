#!/bin/bash

# 新闻爬虫自动化脚本
# 用于定时运行main.py获取最新新闻并增量导入数据库

# 设置工作目录
WORK_DIR="/home/<USER>/project/s_cn_sea/backend/RSS2NEWS-v2"
LOG_DIR="/home/<USER>/project/s_cn_sea/backend/logs"
LOG_FILE="$LOG_DIR/news_crawler_$(date +%Y%m%d).log"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 记录开始时间
echo "========================================" >> "$LOG_FILE"
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始新闻爬取任务" >> "$LOG_FILE"
echo "========================================" >> "$LOG_FILE"

# 确保使用正确的Python环境
export PATH="/home/<USER>/software/anaconda3/bin:$PATH"

# 激活conda环境
if [ -f "/home/<USER>/software/anaconda3/etc/profile.d/conda.sh" ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 激活conda base环境" >> "$LOG_FILE"
    source /home/<USER>/software/anaconda3/etc/profile.d/conda.sh
    conda activate base
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 警告: conda未找到，使用系统Python" >> "$LOG_FILE"
fi

# 切换到工作目录
cd "$WORK_DIR" || {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 错误: 无法切换到工作目录 $WORK_DIR" >> "$LOG_FILE"
    exit 1
}

# 检查并启动代理
check_proxy() {
    # 假设 proxy_on 命令能返回 0 表示已开启
    proxy_status=$(proxy_on status 2>/dev/null)
    if [[ "$proxy_status" != "on" ]]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 代理未开启，正在启动代理" >> "$LOG_FILE"
        sudo bash /home/<USER>/software/clash-for-linux/start.sh >> "$LOG_FILE" 2>&1
        source /etc/profile.d/clash.sh
        proxy_on >> "$LOG_FILE" 2>&1
        sleep 3  # 等待代理完全启动
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 代理已启动" >> "$LOG_FILE"
    else
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 代理已开启" >> "$LOG_FILE"
    fi
}

check_proxy

# 检查Python环境
echo "$(date '+%Y-%m-%d %H:%M:%S') - 检查Python环境" >> "$LOG_FILE"
which python >> "$LOG_FILE" 2>&1
python --version >> "$LOG_FILE" 2>&1

# 检查main.py是否存在
if [ ! -f "main.py" ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 错误: main.py文件不存在" >> "$LOG_FILE"
    exit 1
fi

# 运行Python脚本
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始执行main.py" >> "$LOG_FILE"

# 使用绝对路径或确保python命令可用
if command -v python &> /dev/null; then
    python main.py >> "$LOG_FILE" 2>&1
    exit_code=$?
elif command -v python3 &> /dev/null; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 使用python3命令" >> "$LOG_FILE"
    python3 main.py >> "$LOG_FILE" 2>&1
    exit_code=$?
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 错误: 未找到python或python3命令" >> "$LOG_FILE"
    exit_code=1
fi

# 检查执行结果
if [ $exit_code -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 新闻爬取任务成功完成" >> "$LOG_FILE"
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 新闻爬取任务执行失败，退出码: $exit_code" >> "$LOG_FILE"
fi

# 显示数据库状态
echo "$(date '+%Y-%m-%d %H:%M:%S') - 检查数据库状态" >> "$LOG_FILE"
cd /home/<USER>/project/s_cn_sea/backend

# 检查Node.js是否可用
if command -v node &> /dev/null; then
    node -e "
    const mysql = require('mysql2/promise');
    async function checkStatus() {
      try {
        const pool = mysql.createPool({
          host: 'localhost',
          user: 'root', 
          password: '123456',
          database: 'news_system',
          charset: 'utf8mb4'
        });
        const [result] = await pool.execute('SELECT COUNT(*) as count FROM news WHERE DATE(created_at) = CURDATE()');
        console.log('今日新增新闻数量:', result[0].count);
        pool.end();
      } catch (error) {
        console.error('检查失败:', error.message);
      }
    }
    checkStatus();" >> "$LOG_FILE" 2>&1
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 警告: Node.js未找到，跳过数据库状态检查" >> "$LOG_FILE"
fi

echo "$(date '+%Y-%m-%d %H:%M:%S') - 脚本执行完毕" >> "$LOG_FILE"
echo "========================================" >> "$LOG_FILE"
echo "" >> "$LOG_FILE"

# 清理7天前的日志文件
find "$LOG_DIR" -name "news_crawler_*.log" -mtime +7 -delete 2>/dev/null
