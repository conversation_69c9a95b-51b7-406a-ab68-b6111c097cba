# Python 爬虫服务 Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    g++ \
    cron \
    tzdata \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制 requirements.txt 并安装 Python 依赖
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# 复制 Python 脚本
COPY . ./

# 创建必要的目录
RUN mkdir -p /app/logs /app/data /app/output

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 设置权限
RUN chmod +x /app/*.py

# 创建启动脚本
RUN echo '#!/bin/bash\n\
# 初始化 cron\n\
service cron start\n\
\n\
# 添加定时任务\n\
echo "0 * * * * cd /app && python main.py >> /app/logs/crawler.log 2>&1" | crontab -\n\
\n\
# 立即运行一次\n\
cd /app && python main.py\n\
\n\
# 保持容器运行\n\
tail -f /app/logs/crawler.log' > /app/start.sh && chmod +x /app/start.sh

# 默认命令
CMD ["/app/start.sh"]