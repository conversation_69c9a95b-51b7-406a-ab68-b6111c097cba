# Python 爬虫服务 Dockerfile
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# 复制 requirements.txt 并安装 Python 依赖
COPY backend/RSS2NEWS-v2/requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# 复制 Python 脚本
COPY backend/RSS2NEWS-v2/ ./

# 创建必要的目录
RUN mkdir -p /app/logs /app/data

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 默认命令
CMD ["python", "main.py"]