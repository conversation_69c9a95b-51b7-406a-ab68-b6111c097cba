# ===========================================
# 新闻可视化系统 - Docker 环境配置文件
# ===========================================

# 数据库配置
DB_HOST=mysql
DB_USER=news_user
DB_PASSWORD=your_secure_password_here
DB_NAME=news_system

# 百度翻译API配置（用于新闻翻译）
BAIDU_APP_ID=your_baidu_app_id
BAIDU_SECRET_KEY=your_baidu_secret_key

# 前端API地址配置
# 本地开发: http://localhost:3001
# 云服务器: http://your-server-ip:3001 或使用Nginx代理 /api
VITE_API_BASE_URL=http://localhost:3001

# 代理配置（如果需要访问Google News）
HTTP_PROXY=
HTTPS_PROXY=

# 服务器配置
NODE_ENV=production
PORT=3001

# 时区设置
TZ=Asia/Shanghai

# ===========================================
# 生产环境额外配置
# ===========================================

# SSL证书配置（如果使用HTTPS）
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# 域名配置
DOMAIN=your-domain.com

# 邮件通知配置（可选）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
ADMIN_EMAIL=<EMAIL>