# 新闻可视化系统 - Docker 部署指南

## 项目概述

这是一个基于 Docker 的新闻获取和 3D 地球可视化系统，包含以下组件：

- **前端**: Vue.js + Cesium 3D 地球展示
- **后端API**: Node.js + Express + MySQL
- **新闻爬虫**: Python RSS 爬虫 + 百度翻译
- **数据库**: MySQL 8.0
- **缓存**: Redis (可选)
- **Web服务器**: Nginx

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue.js)  │    │  后端API (Node) │    │ 爬虫 (Python)   │
│   Port: 80      │────│   Port: 3001    │────│   定时任务       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  MySQL 数据库    │
                    │   Port: 3306    │
                    └─────────────────┘
```

## 快速部署

### 前置要求

1. **Docker** (版本 20.10+)
2. **Docker Compose** (版本 2.0+)
3. **百度翻译API** 账号和密钥

### 一键部署

#### Linux/macOS
```bash
# 克隆项目
git clone <your-repo-url>
cd s_cn_sea

# 运行部署脚本
chmod +x deploy.sh
./deploy.sh
```

#### Windows
```cmd
# 克隆项目
git clone <your-repo-url>
cd s_cn_sea

# 运行部署脚本
deploy.bat
```

### 手动部署

1. **配置环境变量**
```bash
# 复制环境配置模板
cp .env.example .env

# 编辑配置文件
nano .env
```

2. **必须配置的环境变量**
```env
# 数据库配置
DB_PASSWORD=your_secure_password

# 百度翻译API (必须配置)
BAIDU_APP_ID=your_baidu_app_id
BAIDU_SECRET_KEY=your_baidu_secret_key
```

3. **启动服务**
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 服务说明

### 1. MySQL 数据库 (mysql)
- **端口**: 3306
- **数据持久化**: `mysql_data` volume
- **配置文件**: `docker/mysql/conf.d/mysql.cnf`
- **初始化脚本**: `docker/mysql/init/`

### 2. 后端API (backend)
- **端口**: 3001
- **技术栈**: Node.js + Express + MySQL
- **健康检查**: `http://localhost:3001/api/status`
- **日志目录**: `backend/logs/`

### 3. Python爬虫 (crawler)
- **功能**: RSS获取 + 翻译 + 数据处理
- **定时任务**: 每小时运行一次
- **日志目录**: `backend/logs/`
- **数据目录**: `backend/data/`

### 4. 前端 (frontend)
- **端口**: 80
- **技术栈**: Vue.js + Cesium + Nginx
- **健康检查**: `http://localhost/health`

### 5. Redis缓存 (redis)
- **端口**: 6379
- **用途**: 缓存优化 (可选)

## 配置说明

### 环境变量详解

| 变量名 | 说明 | 默认值 | 必填 |
|--------|------|--------|------|
| `DB_HOST` | 数据库主机 | mysql | 否 |
| `DB_USER` | 数据库用户 | news_user | 否 |
| `DB_PASSWORD` | 数据库密码 | - | 是 |
| `DB_NAME` | 数据库名称 | news_system | 否 |
| `BAIDU_APP_ID` | 百度翻译APP ID | - | 是 |
| `BAIDU_SECRET_KEY` | 百度翻译密钥 | - | 是 |
| `VITE_API_BASE_URL` | 前端API地址 | http://localhost:3001 | 否 |
| `HTTP_PROXY` | HTTP代理 | - | 否 |
| `HTTPS_PROXY` | HTTPS代理 | - | 否 |

### 百度翻译API配置

1. 访问 [百度翻译开放平台](https://fanyi-api.baidu.com/)
2. 注册账号并创建应用
3. 获取 APP ID 和密钥
4. 在 `.env` 文件中配置

## 管理命令

### 基本操作
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart backend

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend
```

### 数据库操作
```bash
# 连接数据库
docker-compose exec mysql mysql -u root -p news_system

# 备份数据库
docker-compose exec mysql mysqldump -u root -p news_system > backup.sql

# 恢复数据库
docker-compose exec -T mysql mysql -u root -p news_system < backup.sql
```

### 爬虫操作
```bash
# 手动运行爬虫
docker-compose exec crawler python main.py

# 查看爬虫日志
docker-compose logs -f crawler

# 进入爬虫容器
docker-compose exec crawler bash
```

## 监控和维护

### 健康检查
```bash
# 检查所有服务状态
docker-compose ps

# 检查前端健康状态
curl http://localhost/health

# 检查后端API状态
curl http://localhost:3001/api/status

# 检查数据库连接
docker-compose exec mysql mysqladmin ping -h localhost
```

### 日志管理
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend
docker-compose logs crawler
docker-compose logs frontend

# 实时跟踪日志
docker-compose logs -f --tail=100 backend

# 清理日志
docker-compose down
docker system prune -f
```

### 数据备份
```bash
# 创建数据库备份
docker-compose exec mysql mysqldump -u root -p news_system > backup_$(date +%Y%m%d_%H%M%S).sql

# 备份数据文件
tar -czf data_backup_$(date +%Y%m%d_%H%M%S).tar.gz backend/data backend/logs

# 备份Docker volumes
docker run --rm -v news_mysql_data:/data -v $(pwd):/backup alpine tar czf /backup/mysql_volume_backup.tar.gz -C /data .
```

## 故障排除

### 常见问题

#### 1. 数据库连接失败
```bash
# 检查数据库容器状态
docker-compose ps mysql

# 查看数据库日志
docker-compose logs mysql

# 重启数据库
docker-compose restart mysql
```

#### 2. 前端无法访问
```bash
# 检查Nginx配置
docker-compose exec frontend nginx -t

# 重新构建前端
docker-compose build --no-cache frontend
docker-compose up -d frontend
```

#### 3. 爬虫不工作
```bash
# 检查爬虫日志
docker-compose logs crawler

# 手动运行爬虫测试
docker-compose exec crawler python main.py

# 检查网络连接
docker-compose exec crawler ping google.com
```

#### 4. API响应慢
```bash
# 检查数据库性能
docker-compose exec mysql mysql -u root -p -e "SHOW PROCESSLIST;"

# 查看系统资源使用
docker stats

# 重启后端服务
docker-compose restart backend
```

### 性能优化

#### 1. 数据库优化
- 调整 `docker/mysql/conf.d/mysql.cnf` 中的缓存设置
- 定期清理旧数据
- 添加适当的索引

#### 2. 前端优化
- 启用Nginx gzip压缩
- 配置静态资源缓存
- 使用CDN加速

#### 3. 爬虫优化
- 调整爬取频率
- 配置代理池
- 优化翻译API调用

## 生产环境部署

### 安全配置

1. **修改默认密码**
```env
DB_PASSWORD=your_very_secure_password_here
```

2. **配置SSL证书**
```bash
# 将SSL证书放入docker/ssl目录
cp your_cert.pem docker/ssl/cert.pem
cp your_key.pem docker/ssl/key.pem
```

3. **配置防火墙**
```bash
# 只开放必要端口
ufw allow 80
ufw allow 443
ufw deny 3306  # 数据库端口不对外开放
```

### 域名配置

1. **修改Nginx配置**
```nginx
server_name your-domain.com;
```

2. **配置HTTPS**
```nginx
listen 443 ssl;
ssl_certificate /etc/nginx/ssl/cert.pem;
ssl_certificate_key /etc/nginx/ssl/key.pem;
```

### 监控告警

1. **配置日志监控**
2. **设置资源使用告警**
3. **配置服务可用性监控**

## 扩展和定制

### 添加新的新闻源
1. 修改 `backend/RSS2NEWS-v2/getRSS.py`
2. 更新域名映射表
3. 重新构建爬虫镜像

### 自定义前端样式
1. 修改 `frontend/src/` 下的Vue组件
2. 重新构建前端镜像

### 添加新的API接口
1. 修改 `backend/server.js`
2. 重新构建后端镜像

## 技术支持

如果遇到问题，请：

1. 查看相关服务日志
2. 检查环境配置
3. 参考故障排除章节
4. 提交Issue到项目仓库

---

**注意**: 请确保在生产环境中使用强密码，并定期备份数据。