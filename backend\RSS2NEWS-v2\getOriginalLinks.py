#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import json
import csv
import time
import os
from urllib.parse import quote
from lxml import etree
import requests

# === 配置区 ===
PROXIES = {
    'http': 'http://127.0.0.1:7890',
    'https': 'http://127.0.0.1:7890'
}
HEADERS = {
    'Host': 'news.google.com',
    'X-Same-Domain': '1',
    'Accept-Language': 'zh-CN',
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; rv:109.0) Gecko/20100101 Firefox/115.0',
    'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
    'Accept': '*/*',
    'Origin': 'https://news.google.com',
    'Referer': 'https://news.google.com/',
    'Accept-Encoding': 'gzip, deflate, br',
}
SLEEP_SECONDS = 1.0
MAX_RETRIES = 2

def get_google_params(url):
    """从 Google 新闻跳转链接中提取 source、sign 和 ts 参数"""
    try:
        resp = requests.get(url, proxies=PROXIES, timeout=10, headers={"User-Agent": HEADERS['User-Agent']})
        tree = etree.HTML(resp.text)
        sign = tree.xpath('//c-wiz/div/@data-n-a-sg')[0]
        ts = tree.xpath('//c-wiz/div/@data-n-a-ts')[0]
        source = tree.xpath('//c-wiz/div/@data-n-a-id')[0]
        return source, sign, ts
    except Exception as e:
        print(f"[WARN] 提取参数失败: {url} -> {e}")
        return None, None, None

def get_origin_url_from_params(source, sign, ts):
    """根据参数请求 batchexecute 获取重定向的原始新闻链接"""
    try:
        endpoint = "https://news.google.com/_/DotsSplashUi/data/batchexecute"
        req_data = [[[
            "Fbv4je",
            f"[\"garturlreq\",[[\"zh-HK\",\"HK\",[\"FINANCE_TOP_INDICES\",\"WEB_TEST_1_0_0\"],null,null,1,1,\"HK:zh-Hant\",null,480,null,null,null,null,null,0,5],\"zh-HK\",\"HK\",1,[2,4,8],1,1,null,0,0,null,0],\"{source}\",{ts},\"{sign}\"]",
            None,
            "generic"
        ]]]
        payload = f"f.req={quote(json.dumps(req_data))}"
        resp = requests.post(endpoint, headers=HEADERS, data=payload, proxies=PROXIES, timeout=12)
        text = resp.text
        match = re.search(r'https?://[^\s",\\]+', text)
        return match.group() if match else ""
    except Exception as e:
        print(f"[WARN] 请求 batchexecute 失败: {e}")
        return ""

def resolve_original_link(rss_link):
    """从 RSS link 解析出原始新闻链接，如果失败则返回空"""
    source, sign, ts = get_google_params(rss_link)
    if not all([source, sign, ts]):
        return ""
    for attempt in range(1, MAX_RETRIES + 1):
        redirect = get_origin_url_from_params(source, sign, ts)
        if redirect:
            return redirect
        print(f"[INFO] 第 {attempt} 次尝试未成功，重试...")
        time.sleep(0.5)
    return ""

def process_csv(csv_file="south_china_sea_news.csv"):
    """
    处理 CSV：
    - 新增 original_link 列（如果不存在）
    - 只处理 original_link 为空的行
    - 不修改原始 link 列
    - 如果第一次解析失败，立即再尝试一次
    """
    if not os.path.exists(csv_file):
        print(f"[ERROR] 文件不存在: {csv_file}")
        return

    # 读取所有行
    with open(csv_file, "r", encoding="utf-8", newline="") as f_in:
        rows = list(csv.DictReader(f_in))
        if not rows:
            print("[ERROR] CSV 文件为空")
            return
        fieldnames = list(rows[0].keys())
        if "original_link" not in fieldnames:
            fieldnames.append("original_link")

    # 写回 CSV
    with open(csv_file, "w", encoding="utf-8", newline="") as f_out:
        writer = csv.DictWriter(f_out, fieldnames=fieldnames)
        writer.writeheader()

        for idx, row in enumerate(rows, start=1):
            rss_link = row.get("link", "").strip()
            # 如果 original_link 已经有值，跳过
            if row.get("original_link"):
                writer.writerow(row)
                continue
            if not rss_link:
                row["original_link"] = ""
                writer.writerow(row)
                continue

            print(f"[{idx}/{len(rows)}] 解析: {rss_link}")
            try:
                # 第一次解析
                original = resolve_original_link(rss_link)
                if not original:
                    # 第一次失败，立即再尝试一次
                    print("    -> 第一次失败，立即重试一次...")
                    original = resolve_original_link(rss_link)
                row["original_link"] = original
                print(f"    -> {original if original else '[重试失败]'}")
            except Exception as e:
                print(f"[ERROR] 处理失败: {e}")
                row["original_link"] = ""
            writer.writerow(row)
            time.sleep(SLEEP_SECONDS)

    print(f"\n✅ 处理完成，结果已写回 {csv_file}")

if __name__ == "__main__":
    process_csv("south_china_sea_news.csv")
