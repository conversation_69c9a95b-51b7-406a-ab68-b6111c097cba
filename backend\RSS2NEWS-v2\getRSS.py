# getRSS.py (增量存储 + original_link + scraped_status + title_translated + db_status)
import feedparser
import os
from urllib.parse import quote
import re
import pandas as pd
from datetime import datetime
import time


def clean_text(text):
    """清理常见乱码字符并替换成正确符号"""
    if text is None:
        return None
    try:
        text = text.encode('latin1').decode('utf-8')
    except (UnicodeEncodeError, UnicodeDecodeError):
        pass
    replacements = {
        '鈥檚': "'s",
        '鈥�': '"',
        'â€™': "'",
        'â€œ': '"',
        'â€': '"',
        'â€“': '–',
        'â€”': '—',
        'â€¦': '…',
        'Â': '',
        'â€˜': "'",
        'â€': '"',
    }
    for bad, good in replacements.items():
        text = text.replace(bad, good)
    text = re.sub(r'\s+', ' ', text).strip()
    return text


def strip_source_from_title(title):
    """去掉 title 中的来源，保留新闻标题"""
    if not title:
        return title
    if ' - ' in title:
        title = ' - '.join(title.split(' - ')[:-1])
    return title.strip()


def parse_date(s):
    """解析 RSS 时间字符串"""
    if s is None:
        return pd.NaT
    try:
        return datetime.strptime(s, "%a, %d %b %Y %H:%M:%S %Z")
    except Exception:
        try:
            return pd.to_datetime(s, errors="coerce")
        except Exception:
            return pd.NaT


def collect_google_news(keyword="south china sea", max_results=100):
    """从 Google News RSS 获取新闻列表"""
    encoded_keyword = quote(keyword)
    rss_url = f"https://news.google.com/rss/search?q={encoded_keyword}&hl=en-US&gl=US&ceid=US:en"
    feed = feedparser.parse(rss_url)

    news_list = []
    for entry in feed.entries[:max_results]:
        title = clean_text(entry.title) if hasattr(entry, "title") else ""
        title = strip_source_from_title(title)

        # 安全获取发布时间
        published = None
        if hasattr(entry, "published"):
            published = entry.published
        elif hasattr(entry, "updated"):
            published = entry.updated
        elif "pubDate" in entry:
            published = entry["pubDate"]

        # 安全获取来源
        source = None
        if hasattr(entry, "source") and hasattr(entry.source, "title"):
            source = clean_text(entry.source.title)

        news_item = {
            "title": title,
            "link": entry.link if hasattr(entry, "link") else "",
            "published": published,
            "source": source,
            "original_link": "",            # 初始为空
            "scraped_status": "pending",    # 初始状态 pending
            "title_translated": "",         # 初始为空，避免重复翻译
            "db_status": "pending"          # 初始状态
        }
        news_list.append(news_item)
    return news_list


def save_news_to_csv(news_list, filename="south_china_sea_news.csv"):
    """
    保存新闻到 CSV：
    - 第一次运行：保存所有新闻
    - 后续运行：只保存比 CSV 最新时间更新的新闻
    - 保证 original_link、scraped_status、title_translated、db_status 列存在
    - 新闻按 published 时间降序排列
    """
    df_new = pd.DataFrame(news_list)
    if "published" not in df_new.columns:
        df_new["published"] = None
    df_new["published_dt"] = df_new["published"].apply(parse_date)

    if os.path.exists(filename):
        df_existing = pd.read_csv(filename)

        # 确保新列存在
        for col, default in [
            ("original_link", ""),
            ("scraped_status", "pending"),
            ("title_translated", ""),
            ("db_status", "pending"),
        ]:
            if col not in df_existing.columns:
                df_existing[col] = default

        # 取已有新闻的最新发布时间
        if "published" in df_existing.columns and not df_existing.empty:
            df_existing["published_dt"] = df_existing["published"].apply(parse_date)
            latest_time = df_existing["published_dt"].max()
        else:
            latest_time = None

        # 只保留比最新时间更晚的新闻
        if latest_time is not None:
            df_new = df_new[df_new["published_dt"] > latest_time]

        if not df_new.empty:
            df_combined = pd.concat([df_existing, df_new], ignore_index=True)
            print(f"✅ 已添加 {len(df_new)} 条新新闻，总共 {len(df_combined)} 条新闻。")
        else:
            df_combined = df_existing.copy()
            print("ℹ️ 没有新新闻需要添加。")
    else:
        # 文件不存在 → 第一次运行，保存所有新闻
        df_combined = df_new.copy()
        for col, default in [
            ("original_link", ""),
            ("scraped_status", "pending"),
            ("title_translated", ""),
            ("db_status", "pending"),
        ]:
            if col not in df_combined.columns:
                df_combined[col] = default
        print(f"✅ 初次运行，已保存 {len(df_new)} 条新闻到 {filename}")

    # 最终排序
    df_combined["published_dt"] = df_combined["published"].apply(parse_date)
    df_combined = df_combined.sort_values(by="published_dt", ascending=False).drop(columns=["published_dt"])

    # 写回 CSV
    df_combined.to_csv(filename, index=False, encoding="utf-8-sig")
    return filename


# ----------------- 主运行测试 -----------------
if __name__ == "__main__":
    keyword = "south china sea"
    news_list = collect_google_news(keyword)
    save_news_to_csv(news_list, filename="south_china_sea_news.csv")
