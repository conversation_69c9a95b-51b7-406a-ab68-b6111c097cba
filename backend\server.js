const express = require('express');
const mysql = require('mysql2/promise');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件
app.use(cors());
app.use(express.json());

// MySQL 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'news_system',
  charset: 'utf8mb4'
};

// 创建数据库连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    connection.release();
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
  }
}

// 创建路由
const router = express.Router();

// 1、获取所有新闻（支持语言过滤）
router.get('/all', async (req, res) => {
  try {
    const { lang = 'zh' } = req.query;
    
    console.log(`获取所有新闻，语言: ${lang}`);
    
    const query = `
      SELECT 
        n.id,
        n.publish_time,
        n.created_at,
        n.link,
        nt.title,
        nt.keyword,
        nt.content,
        nt.source as source,
        nt.country as country,
        nt.lang
      FROM news n
      INNER JOIN news_translations nt ON n.id = nt.news_id
      WHERE nt.lang = ?
      ORDER BY n.publish_time DESC
    `;
    
    const [rows] = await pool.execute(query, [lang]);
    
    console.log(`查询结果: ${rows.length} 条新闻`);
    
    res.json({
      success: true,
      data: rows,
      message: `获取${lang === 'zh' ? '中文' : '英文'}新闻成功`
    });
    
  } catch (error) {
    console.error('获取新闻列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取新闻失败',
      error: error.message
    });
  }
});

// 2、获取最新新闻（用于实时更新）
router.get('/latest', async (req, res) => {
  try {
    const { since, lang = 'zh' } = req.query;
    
    console.log(`获取最新新闻，时间点: ${since}, 语言: ${lang}`);
    
    let query = `
      SELECT 
        n.id,
        n.publish_time,
        n.created_at,
        n.link,
        nt.title,
        nt.keyword,
        nt.content,
        nt.source as source,
        nt.country as country,
        nt.lang
      FROM news n
      INNER JOIN news_translations nt ON n.id = nt.news_id
      WHERE nt.lang = ?
    `;
    
    const params = [lang];
    
    if (since) {
      query += ' AND n.created_at > ?';
      params.push(since);
    }
    
    query += ' ORDER BY n.created_at DESC LIMIT 50';
    
    const [rows] = await pool.execute(query, params);
    
    console.log(`查询到 ${rows.length} 条最新新闻`);
    
    res.json({
      success: true,
      data: rows,
      message: '获取最新新闻成功'
    });
    
  } catch (error) {
    console.error('获取最新新闻失败:', error);
    res.status(500).json({
      success: false,
      message: '获取最新新闻失败',
      error: error.message
    });
  }
});

// 3、根据ID获取新闻
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { lang = 'zh' } = req.query;
    
    const query = `
      SELECT 
        n.id,
        n.publish_time,
        n.created_at,
        n.link,
        nt.title,
        nt.keyword,
        nt.content,
        nt.source as source,
        nt.country as country,
        nt.lang
      FROM news n
      INNER JOIN news_translations nt ON n.id = nt.news_id
      WHERE n.id = ? AND nt.lang = ?
    `;
    
    const [rows] = await pool.execute(query, [id, lang]);
    
    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '新闻不存在'
      });
    }
    
    res.json({
      success: true,
      data: rows[0],
      message: '获取新闻成功'
    });
    
  } catch (error) {
    console.error('获取新闻详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取新闻失败',
      error: error.message
    });
  }
});

// 使用路由
app.use('/api/news', router);

// 服务器状态检查
app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    message: '服务器运行正常',
    timestamp: new Date().toISOString(),
    database: '已连接'
  });
});

// 获取所有国家配置（包含标签位置）
app.get('/api/countries', async (req, res) => {
  try {
    const [countries] = await pool.execute(`
      SELECT code, name_en, name_zh, label_latitude, label_longitude 
      FROM countries 
      ORDER BY name_en
    `);
    
    res.json({
      success: true,
      data: countries
    });
  } catch (error) {
    console.error('获取国家配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取国家配置失败',
      error: error.message
    });
  }
});

// 根据域名获取国家信息
app.get('/api/domain/:domain/country', async (req, res) => {
  try {
    const { domain } = req.params;
    
    const [result] = await pool.execute(`
      SELECT c.code, c.name_en, c.name_zh, c.label_latitude, c.label_longitude, dm.media_type
      FROM domain_mappings dm
      JOIN countries c ON dm.country_code = c.code
      WHERE dm.domain = ?
    `, [domain]);
    
    if (result.length > 0) {
      res.json({
        success: true,
        data: result[0]
      });
    } else {
      res.json({
        success: true,
        data: {
          code: 'UNKNOWN',
          name_en: 'Unknown',
          name_zh: '未知',
          label_latitude: 12.0,
          label_longitude: 114.0,
          media_type: 'news'
        }
      });
    }
  } catch (error) {
    console.error('域名查询失败:', error);
    res.status(500).json({
      success: false,
      message: '域名查询失败',
      error: error.message
    });
  }
});

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: '新闻可视化系统 API',
    version: '1.0.0',
    endpoints: [
      'GET /api/news/all?lang=zh',
      'GET /api/news/latest?since=timestamp&lang=zh',
      'GET /api/news/:id?lang=zh',
      'GET /api/status'
    ]
  });
});

// 启动服务器
app.listen(PORT, async () => {
  console.log(`服务器运行在 http://0.0.0.0:${PORT}`);
  console.log(`可以在局域网访问: http://*************:${PORT}`);
  console.log('API 接口:');
  console.log(`  • GET http://localhost:${PORT}/api/news/all?lang=zh - 获取所有新闻`);
  console.log(`  • GET http://localhost:${PORT}/api/news/latest?lang=zh - 获取最新新闻`);
  console.log(`  • GET http://localhost:${PORT}/api/news/:id?lang=zh - 获取单条新闻`);
  console.log(`  • GET http://localhost:${PORT}/api/status - 服务器状态`);
  await testConnection();
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n正在关闭服务器...');
  await pool.end();
  process.exit(0);
});