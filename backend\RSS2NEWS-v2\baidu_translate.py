# -*- coding: utf-8 -*-
import requests
import random
import pandas as pd
import re
from hashlib import md5

# ================= 配置百度翻译 API =================
APP_ID = "20241030002189534"  # 你的APPID
APP_KEY = "cbPbUnZ1PbydOjjt_dt3"  # 你的密钥
FROM_LANG = "en"
TO_LANG = "zh"
URL = "http://api.fanyi.baidu.com/api/trans/vip/translate"
MAX_CHARS = 5800  # 百度接口最大支持6000字符，留一点余量


# ================= 工具函数 =================
def make_md5(s, encoding="utf-8"):
    return md5(s.encode(encoding)).hexdigest()


def baidu_translate(query, from_lang=FROM_LANG, to_lang=TO_LANG):
    """调用百度翻译 API 翻译文本"""
    if not query.strip():
        return ""
    salt = random.randint(32768, 65536)
    sign = make_md5(APP_ID + query + str(salt) + APP_KEY)

    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    payload = {
        "appid": APP_ID,
        "q": query,
        "from": from_lang,
        "to": to_lang,
        "salt": salt,
        "sign": sign,
    }

    try:
        r = requests.post(URL, params=payload, headers=headers, timeout=10)
        result = r.json()
        if "trans_result" in result:
            return " ".join([item["dst"] for item in result["trans_result"]])
        else:
            return f"[翻译失败] {result}"
    except Exception as e:
        return f"[请求异常] {e}"

def extract_first_two_paragraphs(text):
    """提取文本的前两段"""
    if not text or not text.strip():
        return ""
    
    # 按换行符分割段落
    paragraphs = [p.strip() for p in text.split('\n') if p.strip()]
    
    # 如果段落数不足2段，返回全部内容
    if len(paragraphs) <= 2:
        return '\n'.join(paragraphs)
    
    # 只返回前两段
    return '\n'.join(paragraphs[:2])

def split_long_text(text):
    """按段落和句子拆分，保证每次翻译不超过 MAX_CHARS"""
    paragraphs = text.split("\n")
    result = []

    for para in paragraphs:
        para = para.strip()
        if not para:
            continue

        if len(para) <= MAX_CHARS:
            result.append(para)
        else:
            sentences = re.split(r"(?<=[.!?])\s+", para)
            current = ""
            for sent in sentences:
                if len(current) + len(sent) + 1 <= MAX_CHARS:
                    current += " " + sent
                else:
                    result.append(current.strip())
                    current = sent
            if current:
                result.append(current.strip())

    return result


def translate_text_block(text):
    """翻译整段新闻（可能包含多个段落、句子）"""
    
    # 提取前两段
    first_two_paragraphs = extract_first_two_paragraphs(text)
    
    if not first_two_paragraphs:
        return ""
    
    parts = split_long_text(first_two_paragraphs)
    translated_parts = [baidu_translate(p) for p in parts]
    return "\n".join(translated_parts)


# ================= 主功能函数（供 main.py 调用） =================
def add_translation_column(csv_file):
    """
    给 CSV 中缺失翻译的新闻添加翻译结果（标题、正文前两段、来源）
    只翻译 db_status 为 pending 的新闻
    :param csv_file: 输入的 CSV 文件路径
    """
    df = pd.read_csv(csv_file)

    # 确保存在翻译列
    if "title_translated" not in df.columns:
        df["title_translated"] = ""
    if "content_translated" not in df.columns:
        df["content_translated"] = ""
    if "source_translated" not in df.columns:
        df["source_translated"] = ""

    # 先把 NaN 填充为空字符串，再转字符串类型
    df["title_translated"] = df["title_translated"].fillna("").astype(str)
    df["content_translated"] = df["content_translated"].fillna("").astype(str)
    df["source_translated"] = df["source_translated"].fillna("").astype(str)

    # 只翻译 db_status 为 pending 的新闻
    pending_mask = df["db_status"].astype(str).str.strip() == "pending"
    
    # 检查是否有pending状态的新闻
    pending_count = pending_mask.sum()
    if pending_count == 0:
        print("ℹ️ 没有 pending 状态的新闻需要翻译。")
        return
    
    print(f"ℹ️ 发现 {pending_count} 条 pending 状态的新闻需要处理翻译。")

    # 找出需要翻译标题的行（仅限pending状态）
    title_mask = (
        pending_mask & 
        (df["title_translated"].isna() | (df["title_translated"].astype(str).str.strip() == ""))
    )
    df_title_to_translate = df[title_mask]

    # 找出需要翻译正文的行（仅限pending状态）
    content_mask = (
        pending_mask & 
        (df["content_translated"].isna() | (df["content_translated"].astype(str).str.strip() == ""))
    )
    df_content_to_translate = df[content_mask]

    # 找出需要翻译来源的行（仅限pending状态）
    source_mask = (
        pending_mask &
        (df["source_translated"].isna() | (df["source_translated"].astype(str).str.strip() == "")) &
        df["source"].notna() & 
        (df["source"].astype(str).str.strip() != "")
    )
    df_source_to_translate = df[source_mask]

    print(f"需要翻译 {len(df_title_to_translate)} 条新闻标题...")
    print(f"需要翻译 {len(df_content_to_translate)} 条新闻正文...")
    print(f"需要翻译 {len(df_source_to_translate)} 条新闻来源...")

    # 翻译标题
    for idx in df_title_to_translate.index:
        title = str(df.at[idx, "title"])
        print(f"\n正在翻译第 {idx+1} 条新闻标题: {title[:50]}...")
        trans_title = baidu_translate(title)
        df.at[idx, "title_translated"] = trans_title

    # 翻译正文前两段
    for idx in df_content_to_translate.index:
        content = str(df.at[idx, "text"]) if "text" in df.columns else ""
        if content and content.strip() and content.strip() != "nan":
            print(f"\n正在翻译第 {idx+1} 条新闻正文前两段...")
            print(f"原文预览: {content[:80]}...")
            trans_content = translate_text_block(content)
            df.at[idx, "content_translated"] = trans_content
        else:
            print(f"\n第 {idx+1} 条新闻没有正文内容，跳过...")
            df.at[idx, "content_translated"] = ""

    # 翻译来源
    for idx in df_source_to_translate.index:
        source = str(df.at[idx, "source"])
        print(f"\n正在翻译第 {idx+1} 条新闻来源: {source}")
        trans_source = baidu_translate(source)
        df.at[idx, "source_translated"] = trans_source

    # 写回 CSV
    df.to_csv(csv_file, index=False, encoding="utf-8-sig")
    print(f"\n✅ 翻译完成，已写回 {csv_file}")
    print(f"   - 翻译了 {len(df_title_to_translate)} 条标题")
    print(f"   - 翻译了 {len(df_content_to_translate)} 条正文")
    print(f"   - 翻译了 {len(df_source_to_translate)} 条来源")


# ================= 独立运行入口 =================
def main():
    add_translation_column("south_china_sea_news.csv")


if __name__ == "__main__":
    main()
