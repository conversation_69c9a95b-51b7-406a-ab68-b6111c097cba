#!/bin/bash
echo "========================================"
echo "     新闻可视化系统 - 完整启动脚本"
echo "========================================"
echo

# 切换到当前脚本所在目录
cd "$(dirname "$0")"

echo "🚀 正在启动新闻可视化系统..."
echo

# 1. 启动后端API服务器
echo "1. 启动后端API服务器..."
(cd backend && ./start.sh) &

# 等待后端服务启动
echo "   等待后端服务启动..."
sleep 5

# 2. 启动前端开发服务器
echo
echo "2. 启动前端开发服务器..."
npm run dev &

echo
echo "✅ 系统启动完成！"
echo
echo "📖 访问地址："
echo "   前端页面: http://localhost:5173"
echo "   后端API:  http://localhost:3001"
echo
echo "💡 测试功能："
echo "   - 在浏览器控制台执行: simulateNewNews()"
echo "   - 点击语言切换按钮测试多语言"
echo
echo "⚠️  注意：请确保已配置好 backend/.env 文件"
echo
wait
