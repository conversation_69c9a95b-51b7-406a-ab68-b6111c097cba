const mysql = require('mysql2');
const fs = require('fs');

// 尝试加载 .env 文件
try {
  if (fs.existsSync('.env')) {
    console.log('📄 发现 .env 文件，加载配置...');
    require('dotenv').config();
  } else {
    console.log('📄 没有找到 .env 文件');
  }
} catch (error) {
  console.log('📄 没有安装 dotenv 模块，尝试手动读取 .env');
  if (fs.existsSync('.env')) {
    const envContent = fs.readFileSync('.env', 'utf8');
    console.log('.env 文件内容:');
    console.log(envContent);
  }
}

console.log('🔍 检查 MySQL 数据库...');

// MySQL 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'news_system',
  charset: 'utf8mb4'
};

console.log('📋 数据库配置:');
console.log('Host:', dbConfig.host);
console.log('User:', dbConfig.user);
console.log('Password:', dbConfig.password ? '***已设置***' : '❌ 未设置');
console.log('Database:', dbConfig.database);
console.log('Charset:', dbConfig.charset);

// 创建数据库连接
const connection = mysql.createConnection(dbConfig);

async function checkMySQLDatabase() {
  try {
    // 测试连接
    console.log('\n🔌 测试数据库连接...');
    await new Promise((resolve, reject) => {
      connection.connect((err) => {
        if (err) {
          console.error('❌ 数据库连接失败:', err.message);
          console.error('错误代码:', err.code);
          if (err.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('💡 可能的解决方案:');
            console.log('1. 检查用户名和密码是否正确');
            console.log('2. 确认 MySQL 用户权限');
            console.log('3. 检查 .env 文件中的数据库配置');
          } else if (err.code === 'ECONNREFUSED') {
            console.log('💡 可能的解决方案:');
            console.log('1. 确认 MySQL 服务是否启动');
            console.log('2. 检查数据库主机地址和端口');
            console.log('3. 运行: sudo systemctl start mysql');
          } else if (err.code === 'ER_BAD_DB_ERROR') {
            console.log('💡 可能的解决方案:');
            console.log('1. 数据库不存在，需要先创建');
            console.log('2. 检查数据库名称是否正确');
          }
          reject(err);
        } else {
          console.log('✅ 数据库连接成功');
          resolve();
        }
      });
    });

    // 1. 检查数据库是否存在
    console.log('\n📋 检查数据库...');
    const databases = await new Promise((resolve, reject) => {
      connection.query('SHOW DATABASES', (err, results) => {
        if (err) reject(err);
        resolve(results);
      });
    });
    
    const dbExists = databases.some(db => db.Database === dbConfig.database);
    console.log('可用数据库:', databases.map(db => db.Database));
    console.log(`目标数据库 "${dbConfig.database}" ${dbExists ? '✅ 存在' : '❌ 不存在'}`);

    if (!dbExists) {
      console.log('\n🔧 创建数据库...');
      await new Promise((resolve, reject) => {
        connection.query(`CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`, (err) => {
          if (err) reject(err);
          console.log(`✅ 数据库 "${dbConfig.database}" 创建成功`);
          resolve();
        });
      });
      
      // 重新连接到新创建的数据库
      connection.end();
      const newConnection = mysql.createConnection(dbConfig);
      await new Promise((resolve, reject) => {
        newConnection.connect((err) => {
          if (err) reject(err);
          resolve();
        });
      });
      Object.assign(connection, newConnection);
    }

    // 2. 检查表结构
    console.log('\n📋 检查表结构...');
    const tables = await new Promise((resolve, reject) => {
      connection.query('SHOW TABLES', (err, results) => {
        if (err) reject(err);
        resolve(results);
      });
    });
    
    const tableKey = `Tables_in_${dbConfig.database}`;
    const tableNames = tables.map(table => table[tableKey]);
    console.log('数据库中的表:', tableNames);

    if (tableNames.length === 0) {
      console.log('\n❌ 数据库为空！没有任何表。');
      console.log('这可能是因为：');
      console.log('1. 数据还没有导入');
      console.log('2. 需要运行表创建脚本');
      console.log('3. 数据导入脚本有问题');
      
      console.log('\n🔍 查找可能的导入脚本...');
      const jsFiles = fs.readdirSync('./').filter(file => file.endsWith('.js'));
      jsFiles.forEach(file => {
        const content = fs.readFileSync(file, 'utf8');
        if (content.includes('CREATE TABLE') || content.includes('INSERT INTO') || content.includes('csv') || content.includes('mysql')) {
          console.log(`📄 可能的导入脚本: ${file}`);
        }
      });
      
    } else {
      // 检查每个表的结构和数据
      for (const tableName of tableNames) {
        console.log(`\n📋 ${tableName} 表结构:`);
        
        // 显示表结构
        const tableStructure = await new Promise((resolve, reject) => {
          connection.query(`DESCRIBE \`${tableName}\``, (err, results) => {
            if (err) reject(err);
            resolve(results);
          });
        });
        console.table(tableStructure);
        
        // 检查记录数
        const count = await new Promise((resolve, reject) => {
          connection.query(`SELECT COUNT(*) as count FROM \`${tableName}\``, (err, results) => {
            if (err) reject(err);
            resolve(results[0]);
          });
        });
        console.log(`${tableName} 表记录数: ${count.count}`);
      }
      
      // 如果有 news 和 news_translations 表，显示样例数据
      if (tableNames.includes('news') && tableNames.includes('news_translations')) {
        console.log('\n📄 样例数据:');
        const sampleData = await new Promise((resolve, reject) => {
          connection.query(`
            SELECT 
              n.id,
              nt.source,
              nt.country,
              n.publish_time,
              nt.title,
              nt.lang
            FROM news n
            JOIN news_translations nt ON n.id = nt.news_id
            LIMIT 3
          `, (err, results) => {
            if (err) reject(err);
            resolve(results);
          });
        });
        
        sampleData.forEach((row, index) => {
          console.log(`\n=== 第 ${index + 1} 条记录 ===`);
          console.log('ID:', row.id);
          console.log('来源 (source):', JSON.stringify(row.source));
          console.log('国家 (country):', row.country);
          console.log('发布时间:', row.publish_time);
          console.log('标题:', row.title ? row.title.substring(0, 50) + '...' : 'NULL');
          console.log('语言:', row.lang);
        });

        // 检查 source 字段的具体值
        console.log('\n📊 source 字段分析:');
        const sourceStats = await new Promise((resolve, reject) => {
          connection.query(`
            SELECT 
              source,
              COUNT(*) as count
            FROM news_translations
            WHERE source IS NOT NULL AND source != ''
            GROUP BY source
            ORDER BY count DESC
            LIMIT 10
          `, (err, results) => {
            if (err) reject(err);
            resolve(results);
          });
        });
        console.log('source 字段的不同值及其出现次数:');
        console.table(sourceStats);

        // 检查空的 source 字段
        const emptySourceCount = await new Promise((resolve, reject) => {
          connection.query(`
            SELECT COUNT(*) as count
            FROM news_translations
            WHERE source IS NULL OR source = ''
          `, (err, results) => {
            if (err) reject(err);
            resolve(results[0]);
          });
        });
        console.log(`source 字段为空或 NULL 的记录数: ${emptySourceCount.count}`);
      }
    }

    console.log('\n✅ MySQL 数据库检查完成');
    
  } catch (error) {
    console.error('❌ 检查过程中出错:', error.message);
    console.error('错误堆栈:', error.stack);
  } finally {
    connection.end();
    console.log('📁 数据库连接已关闭');
  }
}

checkMySQLDatabase();