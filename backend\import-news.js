require('dotenv').config();
const fs = require('fs');
const csv = require('csv-parser');
const mysql = require('mysql2/promise');

// // 将GMT时间转换为中国时间的函数
// function convertToChinaTime(gmtTimeStr) {
//   if (!gmtTimeStr) return new Date();
  
//   const gmtTime = new Date(gmtTimeStr);
//   if (isNaN(gmtTime.getTime())) return new Date();
  
//   // 转换为中国时间 (UTC+8)
//   const chinaTime = new Date(gmtTime.getTime() + 8 * 60 * 60 * 1000);
//   return chinaTime;
// }

async function importNews() {
  console.log('开始导入新闻数据...');
  
  try {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      charset: 'utf8mb4'
    });

    console.log('数据库连接成功');

    const results = [];
    
    // 读取CSV文件
    fs.createReadStream('./data/news_translate.csv')
      .pipe(csv({mapHeaders: ({ header }) => header.trim().replace(/^\uFEFF/, '')}))  // 去掉BOM和空格
      .on('data', (data) => results.push(data))
      .on('end', async () => {
        console.log(`读取到 ${results.length} 条新闻记录`);
        
        let successCount = 0;
        let errorCount = 0;

        for (const [index, row] of results.entries()) {
          try {
            // 开始事务
            await connection.query('START TRANSACTION');
            
            // 插入主表 news
            // const publishTime = convertToChinaTime(row.published);
            // console.log(`第 ${index + 1} 条记录 - GMT: ${row.published} -> 中国时间: ${publishTime.toISOString()}`);
            const [newsResult] = await connection.execute(
              'INSERT INTO news (publish_time, link) VALUES (?, ?)',
              [row.published, row.original_link || null]
            );
            
            const newsId = newsResult.insertId;
            
            // 插入中文版本
            if (row.title_translated && row.content_translated) {
              await connection.execute(
                'INSERT INTO news_translations (news_id, lang, title, content, source, country, keyword) VALUES (?, ?, ?, ?, ?, ?, ?)',
                [
                  newsId,
                  'zh',
                  row.title_translated,              // 中文标题
                  row.content_translated || '',     // 中文内容
                  row.source_translated  || '未知来源',  // 中文来源
                  row.country_translated  || '未知', // 中文国家
                  row.keywords || null              // 关键词（使用原始字段）
                ]
              );
            }
            
            // 插入英文版本
            if (row.title && row.text) {  // 只要有英文标题和内容就插入
              await connection.execute(
                'INSERT INTO news_translations (news_id, lang, title, content, source, country, keyword) VALUES (?, ?, ?, ?, ?, ?, ?)',
                [
                  newsId,
                  'en',
                  row.title,                    // 英文标题
                  row.text || '',               // 英文内容
                  row.source || 'Unknown Source',     // 英文来源
                  row.country || 'Unknown',     // 英文国家
                  row.keywords || null          // 关键词
                ]
              );
            }
            
            // 提交事务
            await connection.query('COMMIT');
            successCount++;
            
            console.log(`第 ${index + 1} 条记录导入成功`);
            
          } catch (error) {
            // 回滚事务
            await connection.query('ROLLBACK');
            console.error(`第 ${index + 1} 条记录导入失败:`, error.message);
            console.error('数据内容:', {
              title: row.title ? row.title.substring(0, 50) : '无标题',
              published: row.published,
              source: row.source
            });
            errorCount++;
          }
        }
        
        await connection.end();
        console.log('导入完成！');
        console.log(`成功: ${successCount} 条，失败: ${errorCount} 条`);
      })
      .on('error', (error) => {
        console.error('读取CSV文件失败:', error.message);
      });
      
  } catch (error) {
    console.error('数据库连接失败:', error.message);
  }
}

// 检查CSV文件是否存在
if (!fs.existsSync('./data/news_translate.csv')) {
  console.error('找不到 CSV 文件: ./data/news_translate.csv');
  console.log('请将 CSV 文件放置在 backend/data/news_translate.csv');
  process.exit(1);
}

importNews().catch(console.error);