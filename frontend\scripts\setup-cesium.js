import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const cesiumSource = path.resolve(__dirname, '../node_modules/cesium/Build/Cesium');
const cesiumDest = path.resolve(__dirname, '../public/cesium');

async function copyCesiumAssets() {
  try {
    // 确保目标目录存在
    await fs.ensureDir(cesiumDest);
    
    // 复制必要的静态资源
    const folders = ['Assets', 'Workers', 'ThirdParty', 'Widgets'];
    
    for (const folder of folders) {
      const srcPath = path.join(cesiumSource, folder);
      const destPath = path.join(cesiumDest, folder);
      
      if (await fs.pathExists(srcPath)) {
        await fs.copy(srcPath, destPath);
        console.log(`✓ 已复制 ${folder} 到 public/cesium/`);
      }
    }
    
    // 复制 Cesium.js 文件
    const cesiumJs = path.join(cesiumSource, 'Cesium.js');
    if (await fs.pathExists(cesiumJs)) {
      await fs.copy(cesiumJs, path.join(cesiumDest, 'Cesium.js'));
      console.log('✓ 已复制 Cesium.js');
    }
    
    console.log('🎉 Cesium 静态资源复制完成！');
  } catch (error) {
    console.error('❌ 复制 Cesium 资源时出错:', error);
  }
}

copyCesiumAssets();